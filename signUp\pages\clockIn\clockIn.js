// signUp/pages/clockIn/clockIn.js
const app = getApp()
import {
  SignInUserOpen,
  getQuestionnaireList,
  getCampaignType
} from '../../api/index.js'
let requiredFields = []
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isComplate: false,
    loda: false,
    loading: false,
    recordList: [{
      title: '舒适度',
      text: '使用时肌肤是否感到舒适、温和',
      key: 4,
      value: 'comfort',
      maxScore: 5
    },
    {
      title: '保湿效果',
      text: '使用时肌肤是否感到滋润、保湿',
      key: 2,
      value: 'moisturize',
      maxScore: 5
    },
    {
      title: '吸收速度',
      text: '使用时肌肤是否能够快速吸收、清爽不黏腻',
      key: 5,
      value: 'absorption',
      maxScore: 5
    },
    {
      title: '持久效果',
      text: '肌肤保湿舒缓持续时长',
      key: 4,
      value: 'persistence',
      maxScore: 5
    },
    {
      title: '整体满意度',
      text: '对今天使用结果综合评价',
      key: 4,
      value: 'satisfaction',
      maxScore: 5
    },
    ],
    disabled: false,
    islastDay: true, // 是否为打卡最后一天 7，8，9,10都是最后一天
    info: {
      answerValue: '',
      satisfaction: '', // 整体满意度
      comfort: '', // 舒适度
      absorption: '', // 吸收速度
      moisturize: '', // 保湿效果
      suggestion: '', // 提升空间（可多选，逗号分隔）
      feeling: '', // 使用感受
      recommend: '', // 推荐
      improve: '', // 改善（可多选，逗号分隔） 1、敏感现象减少 2、保湿效果显著提升 3、毛孔收缩明显
      persistence: '', // 持久效果
      purchase: '', // 是否继续购买
      channelTwo: app.globalData.channelTwo,
      channelOne: app.globalData.channelOne,
      campaignCode: wx.getStorageSync('campaignCode'),
    },
    fileList: [],
    clickType: [],
    clickList: [],
    type: '',
    days: '',
    skinCondition: [{
      label: '敏感现象（泛红、刺痛等)',
      isClick: false
    },
    {
      label: '暗沉改善',
      isClick: false
    },
    {
      label: '皮肤更加水润',
      isClick: false
    },
    {
      label: '毛孔变得细腻',
      isClick: false
    },
    {
      label: '光泽度提升',
      isClick: false
    },
    {
      label: '细纹/初老迹象减少',
      isClick: false
    },
    {
      label: '痘痘/闭口减少',
      isClick: false
    },
    {
      label: '未见明显改善',
      isClick: false
    },
    ],
    recordList1: [{
      title: '对产品的总体满意度评分？',
      text: '打分1~5分',
      key: 4,
      value: 'satisfaction',
      maxScore: 5
    },
    {
      title: '在未来是否愿意推荐该产品给他人？',
      text: '打分1~5分',
      key: 2,
      value: 'recommend',
      maxScore: 5
    },
    {
      title: '在未来，您是否会继续购买该产品？',
      text: '打分1~5分',
      key: 5,
      value: 'absorption',
      maxScore: 5
    },
    ],
    questionList: [],
    showContact1: false, // 拒绝订阅 弹框提示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getDatas()
    console.log(app.globalData, 'app.globalData 渠道');
    this.setData({
      islastDay: /^true$/i.test(options.isLastDay),
      type: options.type,
      disabled: options.type == 'view' ? true : false,
      info: options.info ? JSON.parse(options.info) : {
        answerValue: '',
        satisfaction: '', // 整体满意度
        comfort: '', // 舒适度
        absorption: '', // 吸收速度
        moisturize: '', // 保湿效果
        suggestion: '', // 提升空间（可多选，逗号分隔）
        feeling: '', // 使用感受
        recommend: '', // 推荐
        improve: '', // 改善（可多选，逗号分隔） 1、敏感现象减少 2、保湿效果显著提升 3、毛孔收缩明显
        persistence: '', // 持久效果
        purchase: '', // 是否继续购买
        channelTwo: app.globalData.channelTwo,
        channelOne: app.globalData.channelOne,
        campaignCode: wx.getStorageSync('campaignCode'),
      },
      days: options.days >= 7 ? 7 : (options.days || 1)
    })
    this.getList()
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const {
      info
    } = this.data;
    const allFilled = requiredFields.every(field => info[field]);
    console.log(info, 'info:');
    console.log(allFilled, 'allFilled:');
    this.setData({
      isComplate: allFilled,
    })
  },
  getDatas() {
    getCampaignType().then(res => {
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      this.setData({
        CampaignData: res.data
      })
    })
  },
  bindinput(e) {
    let info = this.data.info
    let {
      alias
    } = e.currentTarget.dataset
    let value = e.detail.value.length > 200 ? e.detail.value.substring(0, 200) : e.detail.value
    info[alias] = value
    this.setData({
      info
    })
    this.validateForm()
  },
  getList() {
    let {
      improve,
      materialUrl
    } = this.data.info
    improve = improve ? improve.split(',') : []
    improve = improve.map(item => item)
    console.log(improve, '第七天查看详情 改善');
    getQuestionnaireList({
      days: this.data.days,
      scene: 1
    }).then(({
      data
    }) => {
      requiredFields = []
      data.forEach(item => {
        if (item.isMust == 1) {
          requiredFields.push(item.alias)
        }
        if (item.questionType == 2) {
          item.options.forEach(i => {
            if (improve.includes(i.title)) {
              i.isClick = true
            } else {
              i.isClick = false
            }
          })
        }
      })
      console.log(materialUrl, 'materialUrl 图片');
      if (materialUrl) {
        let fileList = []
        materialUrl.split(',').map(item => fileList.push({
          url: item
        }))
        this.setData({
          fileList
        })
      }
      this.setData({
        questionList: data
      })
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },
  changeRate1(e) {
    let {
      index,
      alias
    } = e.target.dataset
    let value = e.detail
    let info = this.data.info
    info[alias] = value
    this.setData({
      info
    })
    this.validateForm()
  },

  // 上传前校验
  beforeRead: app.debounce(async function (e) {
    let file = e.detail.file
    file.forEach(item => {
      // console.log(item, 'file 上传图片循环');
      let {
        fileType,
        size,
        type
      } = item
      if (fileType != 'image') {
        app.toast('请上传图片格式')
        return Promise.reject()
      } else if (size > 20 * 1024 * 1024) {
        app.toast('图片限制在20M以内')
        return Promise.reject()
      }
    })
    this.afterRead(e)
    return Promise.resolve()
  }),
  // 上传图片
  afterRead: app.debounce(async function (event) {
    console.log(event, 'event 上传图片');
    const {
      file
    } = event.detail
    let that = this
    this.setData({
      loading: true
    })
    if (this.data.fileList.length > 9) {
      this.setData({
        fileList: this.data.fileList.slice(0, 9)
      })
      return
    }
    let fileList = this.data.fileList
    let fileUrl = file.map(item => item.url)
    console.log(fileUrl, '多张图片上传');
    wx.cropImage({
      src: fileUrl[0], // 图片路径
      cropScale: '1:1', // 裁剪比例，可以根据需求调整
      success: (res) => {
        const tempFilePath = res.tempFilePath; // 裁剪后图片的临时文件路
        console.log('裁剪后的图片路径:', tempFilePath);
        // 如果要上传服务器，可以使用 wx.uploadFile 接口
        wx.$uploadFiles({
          fileUrl: [tempFilePath]
        }).then((url) => {
          this.setData({
            loading: false
          })
          console.log(url, '附件列表');
          let list = []
          if (url.success) {
            url.data.forEach(item => {
              list.push({
                url: item
              })
            })
          }
          fileList = [...fileList, ...list]
          if (fileList.length > 9) {
            that.setData({
              fileList: that.data.fileList.slice(0, 9)
            })
            return
          }
          that.setData({
            fileList
          })
        }).catch(() => {
          wx.showToast({
            title: '图片上传失败',
          })
          that.setData({
            loading: false
          })
        })
        this.validateForm()
      },
      fail: function (err) {
        console.error('裁剪图片失败:', err);
      }
    })

  }),
  // 删除图片
  del: app.debounce(async function (event) {
    console.log(event, '11111111111');
    const {
      index
    } = event.detail
    let {
      index: idx
    } = event.currentTarget.dataset
    let fileList = this.data.fileList
    fileList.splice(index, 1)
    console.log(fileList, 'fileList');
    this.setData({
      "fileList": fileList
    })
  }),
  clickTap(e) {
    if (this.data.type == 'view') return false
    let {
      indexs,
      index
    } = e.currentTarget.dataset
    let questionList = this.data.questionList

    let clickType = this.data.clickType
    let skinCondition = questionList[index].options
    if (clickType.includes(indexs)) {
      // 有当前索引的 删除
      clickType = clickType.filter(item => item !== index)
    } else {
      clickType.push(indexs)
    }
    skinCondition[indexs].isClick = !skinCondition[indexs].isClick
    questionList[index].options = skinCondition
    // let clickList = skinCondition.map(item => item.isClick ? item.sort : null)
    let clickList = skinCondition.map(item => item.isClick ? item.title : null)
    console.log(clickList, 'clickList');
    this.setData({
      questionList,
      clickType,
      clickList: clickList.filter(i => i)
    })
    console.log(this.data.clickList, 'clickList');
    this.validateForm()
  },
  submit: app.debounce(async function () {
    // 七天打卡
    wx.$mp.track({
      event: `punch_information${this.data.days}_submit_click`,
    })
    let info = this.data.info
    if (this.data.islastDay) {
      let list = this.data.fileList.map(item => item.url)
      // info.materialUrl = list.join(',')
      info.materialUrl = list && list.length > 0 ? list.join(',') : ''
      info.improve = this.data.clickList.join(',')
      if (info.feeling.length < 10) {
        wx.showToast({
          title: '请填写你近7日的使用感受，需10字以上',
          icon: 'none',
          duration: 2000
        })
        return
      }
    }
    this.validateForm()
    if (this.data.isComplate) {
      this.setData({
        loda: true
      })
      SignInUserOpen(info).then(res => {
        this.setData({
          loda: false
        })
        console.log(res, '用户打卡完成');
        // 第七天打卡不弹订阅信息
        if (this.data.days < 7) {
          this.subscribeTo()
        } else {
          wx.$mp.navigateBack()
        }
      }).catch(err => {
        console.log(err, '报错');
        this.setData({
          loda: false
        })
      })
    } else {
      wx.showToast({
        title: '请填完所有信息',
        icon: 'none',
        duration: 2000
      })
    }

  }, 1500),
  // 订阅消息
  subscribeTo: app.debounce(async function () {
    if (app.ifRegister()) {
      app.subscribe2('sign_in').then((res) => {
        console.log(res, 'res 订阅消息');
        let templateIds = res?.templateIds || []
        if (templateIds && templateIds.length > 0) {
          templateIds.forEach((item) => {
            if (res[item] == 'reject') {
              // this.openGuide()
              wx.showToast({
                title: '拒绝订阅',
                icon: 'none',
              });
            } else if (res[item] == "accept") {
              wx.showToast({
                title: '订阅成功',
                icon: 'none',
              });
            }
          })
        }
        wx.$mp.navigateBack()
      })
      // app.subscribe('sign_in').then(() => {
      //   wx.$mp.navigateBack()
      // })
    }
  }),
  // 用户点击拒绝授权 展开提示在某个地方开启授权弹框
  openGuide() {
    this.setData({
      showContact1: true
    })
  },
  closeGuide() {
    this.setData({
      showContact1: false
    })
    // 订阅拒绝 也要返回上一页
    wx.$mp.navigateBack()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },



  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
