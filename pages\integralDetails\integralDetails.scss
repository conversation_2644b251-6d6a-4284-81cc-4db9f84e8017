.page {
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  height: 100vh;

  .shop-details {
    flex: 1;
    height: 0;
    padding-bottom: var(--bottom);
  }

  .shop-content {
    margin: 39rpx 40rpx 0 40rpx;

    .product-tag-wrap {
      display: flex;
      align-items: center;
    }

    .product-tag {
      padding: 0 10rpx;
      height: 38rpx;
      line-height: 38rpx;
      display: inline-block;
      margin-right: 20rpx;

      &.is-grey-bg {
        background: #eeeeee;
        color: #3C3C43;
      }

      .tag-txt {
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 22rpx;
        line-height: 38rpx;
        text-align: left;
        font-style: normal;
      }
    }

    .shop-header {
      display: flex;
      align-items: center;

      .shop-title {
        flex: 1;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: var(--text-black-color);
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        margin-top: 30rpx;
      }
    }

    .points-box {
      margin-top: 20rpx;
      display: flex;

      .points-num {
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 48rpx;
        color: var(--primary-color);
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }

      .points-title {
        margin-left: 10rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #7F0019;
        line-height: 24rpx;
        text-align: left;
        font-style: normal;
      }

      .line-through {
        margin-left: 10rpx;

        height: 24rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 24rpx;
        text-align: left;
        font-style: normal;
        text-decoration-line: line-through;
      }
    }

    .tips-box {
      margin-top: 30rpx;
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: var(--text-black-color);
      line-height: 24rpx;
      text-align: left;
      font-style: normal;
      position: relative;

      .price-item {
        position: relative;
        display: flex;

        &:nth-child(2) {
          padding-left: 20rpx;

          &::before {
            content: '';
            display: block;
            width: 1px;
            height: 100%;
            background-color: var(--text-black-color);
            position: absolute;
            left: 10rpx;

          }
        }
      }

      .tips-item {
        position: relative;

        &:first-child {
          margin-right: 10rpx;
        }


      }

      .red-font {
        color: #7f0019;


      }
    }

    .tips-line {
      margin-top: 40rpx;
      width: 100%;
      height: 2rpx;
      background: var(--cart-background-color);
    }

    // .divided-line {
    //   margin-top: 60rpx;
    //   width: 100%;
    //   height: 2rpx;
    //   background: #eee;
    // }

    .cell-box {
      margin-top: 60rpx;
    }
  }


}

/* 组件配置上addGlobalClass: true后，其样式会受到页面样式的影响，但不会受到父组件样式的影响 */
.mp-html-exchange-notice,
.mp-html-instructions {
  .mp-html-root {
    color: #7e8c8d;
  }
}
