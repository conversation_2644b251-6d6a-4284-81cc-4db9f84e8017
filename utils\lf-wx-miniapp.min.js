"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},e={};!function(e){Object.defineProperty(e,"__esModule",{value:!0});const n={TYPE:"WECHATAPP SDK",VERSION:"2.16.0"},s=["gender","utm.source","utm.medium","utm.campaign","utm.content","utm.term","utm.qrcodeId"];var i,o;e.MessageDomain=void 0,(i=e.MessageDomain||(e.MessageDomain={})).Contact="contact",i.Event="event",i.Identity="identity",e.MessageContactAction=void 0,(o=e.MessageContactAction||(e.MessageContactAction={})).Set="set",o.SetOnce="set_once",o.Unset="unset",o.Append="append",o.Increase="increase",o.Decrease="decrease",e.MessageEventAction=void 0,(e.MessageEventAction||(e.MessageEventAction={})).Track="track",e.MessageIdentityAction=void 0,(e.MessageIdentityAction||(e.MessageIdentityAction={})).Identify="identify";const r=[];for(let t=0;t<256;++t)r.push((t+256).toString(16).substring(1));const a=(t=0)=>{let e,n=new Array(16);for(let t=0;t<16;t++)0==(3&t)&&(e=4294967296*Math.random()),n[t]=e>>>((3&t)<<3)&255;return n[6]=15&n[6]|64,n[8]=63&n[8]|128,(r[n[t+0]]+r[n[t+1]]+r[n[t+2]]+r[n[t+3]]+"-"+r[n[t+4]]+r[n[t+5]]+"-"+r[n[t+6]]+r[n[t+7]]+"-"+r[n[t+8]]+r[n[t+9]]+"-"+r[n[t+10]]+r[n[t+11]]+r[n[t+12]]+r[n[t+13]]+r[n[t+14]]+r[n[t+15]]).toLowerCase()};class c{constructor(t,e,n){this.identities=[],this.messageQueue=[],this.storage=e,this.request=t,this.sdkOptions=n,this.loadIdentities(),this.startTimer()}sendMessages(t){this.messageQueue=[...this.messageQueue,...t],this.messageQueue.length>=50&&this.flush()}sendMessage(t){this.messageQueue.push(t),this.messageQueue.length>=50&&this.flush()}setIdentities(t){let e=0===this.identities.length;if(1===this.identities.length&&"anonymousId"===this.identities[0].type)e=!0;else if(!e)for(let n of t)if(this.hasIdentity(n)){e=!0;break}!1===e&&(this.identities=[],this.messageQueue=[]),this.addIdentities(t)}addIdentities(t){const n=[];for(let e of t)!1===this.hasIdentity(e)&&(this.identities.push(e),e.type===this.sdkOptions.channelIdType&&n.push(e));this.persistIdentities(),0===n.length&&n.push({}),n.forEach((t=>{this.messageQueue.push({message_id:a(),domain:e.MessageDomain.Identity,action:e.MessageIdentityAction.Identify,ts:(new Date).getTime(),data:t})}))}cleanIdentities(){this.identities=[],this.messageQueue=[],this.persistIdentities()}flush(){if(0===this.messageQueue.length)return;if(0===this.identities.length){if(!1===this.sdkOptions.auto_anonymous_id)return void(this.messageQueue.length>100&&this.messageQueue.splice(0,this.messageQueue.length-50));this.identities.push(this.generateAnonymousId()),this.persistIdentities()}let t={client_id:this.getClientId(),request_id:a(),write_key:this.sdkOptions.token,send_ts:Date.now(),sdk_type:n.TYPE,sdk_version:n.VERSION,identities:this.identities,messages:this.messageQueue};this.messageQueue=[],this.request.post("/api/sdk/v2/batch_messages",t,{clientId:this.getClientId()}).catch()}startTimer(){this.flushTimer||(this.flushTimer=setInterval(this.flush.bind(this),5e3))}stopTimer(){this.flushTimer&&(clearInterval(this.flushTimer),this.flushTimer=null)}generateAnonymousId(){return{id:a(),type:"anonymousId"}}hasIdentity(t){for(let e of this.identities)if(e.id===t.id&&e.type===t.type)return!0;return!1}loadIdentities(){this.identities=this.storage.get("nz.analytics.identities")||[]}persistIdentities(){this.storage.set("nz.analytics.identities",this.identities)}getClientId(){return this.clientId||(this.clientId=this.storage.get("nz.analytics.client_id"),this.clientId||(this.clientId=a(),this.storage.set("nz.analytics.client_id",this.clientId))),this.clientId}}
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.
  	Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.
  	THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */var u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{},p={exports:{}};!function(t,e){var n,s,i,o="[object Map]",r="[object Promise]",a="[object Set]",c="[object WeakMap]",p="[object DataView]",l=/^\[object .+?Constructor\]$/,h="object"==typeof u&&u&&u.Object===Object&&u,d="object"==typeof self&&self&&self.Object===Object&&self,g=h||d||Function("return this")(),f=e&&!e.nodeType&&e,m=f&&t&&!t.nodeType&&t,_=m&&m.exports===f,y=Function.prototype,v=Object.prototype,I=g["__core-js_shared__"],w=(n=/[^.]+$/.exec(I&&I.keys&&I.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",x=y.toString,O=v.hasOwnProperty,b=v.toString,S=RegExp("^"+x.call(O).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),P=_?g.Buffer:void 0,k=v.propertyIsEnumerable,A=P?P.isBuffer:void 0,j=(s=Object.keys,i=Object,function(t){return s(i(t))}),T=q(g,"DataView"),M=q(g,"Map"),C=q(g,"Promise"),E=q(g,"Set"),D=q(g,"WeakMap"),W=!k.call({valueOf:1},"valueOf"),N=V(T),H=V(M),U=V(C),R=V(E),$=V(D);function q(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return function(t){if(!L(t)||function(t){return!!w&&w in t}(t))return!1;var e=Y(t)||function(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}(t)?S:l;return e.test(V(t))}(n)?n:void 0}var Q=function(t){return b.call(t)};function V(t){if(null!=t){try{return x.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(T&&Q(new T(new ArrayBuffer(1)))!=p||M&&Q(new M)!=o||C&&Q(C.resolve())!=r||E&&Q(new E)!=a||D&&Q(new D)!=c)&&(Q=function(t){var e=b.call(t),n="[object Object]"==e?t.constructor:void 0,s=n?V(n):void 0;if(s)switch(s){case N:return p;case H:return o;case U:return r;case R:return a;case $:return c}return e});var F=Array.isArray;function z(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}(t.length)&&!Y(t)}var K=A||function(){return!1};function Y(t){var e=L(t)?b.call(t):"";return"[object Function]"==e||"[object GeneratorFunction]"==e}function L(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}t.exports=function(t){if(z(t)&&(F(t)||"string"==typeof t||"function"==typeof t.splice||K(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&z(t)}(t)&&O.call(t,"callee")&&(!k.call(t,"callee")||"[object Arguments]"==b.call(t))}(t)))return!t.length;var e=Q(t);if(e==o||e==a)return!t.size;if(W||function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||v)}(t))return!j(t).length;for(var n in t)if(O.call(t,n))return!1;return!0}}(p,p.exports);var l=p.exports;e.CONTACT_PERSIS_PROPS=s,e.MiniAppDummySDK=class{constructor(t){this.TYPE=n.TYPE,this.VERSION=n.VERSION,this.utm={},this.options={},this.options=t}setProfile(t){}setOnceProfile(t){}unsetProfile(t){}increaseProfile(t){}decreaseProfile(t){}appendProfile(t){}track(t){}setIdentities(t){}addIdentities(t){}cleanIdentities(){}setUtm(t){}flush(){}},e.MpLifecycle=class{constructor(t,e){this.storage=t,this.sdk=e,this.loadContext()}mergeContext(t){this.context=Object.assign(Object.assign({},this.context),t),this.persistContext()}loadContext(){this.context=this.storage.get("nz.analytics.lifecycle.context"),this.context||(this.context={total_page_list:[]})}persistContext(){this.storage.set("nz.analytics.lifecycle.context",this.context)}extractUtm(t){let e={};return(null==t?void 0:t.qrcodeId)?e={qrcodeId:t.qrcodeId}:(e={},(null==t?void 0:t.utm_source)&&(e.source=t.utm_source),(null==t?void 0:t.utm_medium)&&(e.medium=t.utm_medium),(null==t?void 0:t.utm_campaign)&&(e.campaign=t.utm_campaign),(null==t?void 0:t.utm_content)&&(e.content=t.utm_content),(null==t?void 0:t.utm_term)&&(e.term=t.utm_term)),(null==t?void 0:t.lf_code)&&(e.lfCode=t.lf_code),e}renewUtms(t){let e=this.extractUtm(t);l(e)||this.sdk.setUtm(e)}},e.SDKIntegration=c,e.SDK_INFO=n,e.getSDKInstance=function(t,i,o,r){const u=new c(t,i,r);return new class{constructor(t){this.TYPE=n.TYPE,this.VERSION=n.VERSION,this.options=t,this.loadUtm()}setProfile(t){let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.Set,ts:(new Date).getTime(),data:t};u.sendMessage(n)}setOnceProfile(t){let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.SetOnce,ts:(new Date).getTime(),data:t};u.sendMessage(n)}unsetProfile(t){t=t.filter((t=>!s.includes(t)));let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.Unset,ts:(new Date).getTime(),data:t};u.sendMessage(n)}increaseProfile(t){let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.Increase,ts:(new Date).getTime(),data:t};u.sendMessage(n)}decreaseProfile(t){let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.Decrease,ts:(new Date).getTime(),data:t};u.sendMessage(n)}appendProfile(t){let n={message_id:a(),domain:e.MessageDomain.Contact,action:e.MessageContactAction.Append,ts:(new Date).getTime(),data:t};u.sendMessage(n)}track(t){let n={sdkType:this.TYPE,sdkVersion:this.VERSION,utm:this.utm},s=t=>{if(t.items instanceof Array)for(let e=0;e<t.items.length;e++)t.items[e]=Object.assign(Object.assign({},n),t.items[e]);let s={message_id:a(),domain:e.MessageDomain.Event,action:e.MessageEventAction.Track,ts:(new Date).getTime(),data:t};u.sendMessage(s)};Promise.all([o.getOSInfo(),o.getNetworkType()]).then((([e,i])=>{n=Object.assign(Object.assign({},n),{os:e.os,platform:e.platform,osVersion:e.os_version,networkType:i,manufacturer:e.brand,deviceModel:e.model,screenWidth:e.screen_width,screenHeight:e.screen_height}),s(Object.assign(Object.assign({},n),t))})).catch((()=>{s(Object.assign(Object.assign({},n),t))}))}setIdentities(t){u.setIdentities(t)}addIdentities(t){u.addIdentities(t)}cleanIdentities(){u.cleanIdentities()}flush(){u.flush()}setUtm(t){if(t=t||{},this.utm=t,!l(t)){const e={};t.qrcodeId&&(e.qrcodeId=t.qrcodeId),t.source&&(e.source=t.source),t.medium&&(e.medium=t.medium),t.campaign&&(e.campaign=t.campaign),t.content&&(e.content=t.content),t.term&&(e.term=t.term),l(e)||this.setProfile({utm:e})}this.persisUtm()}loadUtm(){let t=i.get("nz.analytics.utm")||{},e=!1;"MAX_AGE"===r.utm_stractegy&&!l(t)&&t.ts&&t.ts+3600*r.utm_max_age*1e3<Date.now()?(e=!0,t={}):"SESSION"===r.utm_stractegy&&(l(t)||(e=!0),t={});const{ts:n}=t,s=function(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(s=Object.getOwnPropertySymbols(t);i<s.length;i++)e.indexOf(s[i])<0&&Object.prototype.propertyIsEnumerable.call(t,s[i])&&(n[s[i]]=t[s[i]])}return n}(t,["ts"]);this.utm=s,l(this.utm)||n||(e=!0),!0===e&&this.persisUtm()}persisUtm(){l(this.utm)?i.set("nz.analytics.utm",{}):i.set("nz.analytics.utm",Object.assign({ts:Date.now()},this.utm))}}(r)},e.mergeSDKOptions=t=>{const e={name:"LFAPP",token:null,defaultPath:null,server_url:null,utm_stractegy:"OVERWRITE",utm_max_age:720,auto_anonymous_id:!0,auto_track:{app_launch:!0,app_show:!0,app_hide:!0,page_show:!0,page_share:!0,page_leave:!1,page_leave_max_duration:1800}};let n=Object.assign({},e.auto_track,t.auto_track),{page_leave_max_duration:s}=n;(isNaN(s)||s<0)&&(s=1800),n.page_leave_max_duration=s;let i=Object.assign({},e,t);return i.auto_track=n,i},e.query2str=(t={})=>{const e=[];for(const n in t||{})if(t[n]instanceof Array)for(const s of t[n])e.push(`${encodeURIComponent(n.toString())}=${encodeURIComponent(s.toString())}`);else null!==t[n]&&void 0!==t[n]&&e.push(`${encodeURIComponent(n.toString())}=${encodeURIComponent(t[n].toString())}`);const n=e.join("&");return n?`?${n}`:""},e.uuidv4=a}(e);class n extends e.MpLifecycle{onAppLaunch(t,e){e[this.sdk.options.name]=this.sdk,setTimeout((()=>{this.mergeContext({scene:t.scene,currpage:t.path,firstpage:this.context.firstpage||t.path,is_first_open:!this.context.firstpage,app_launch_count:this.context.app_launch_count>0?this.context.app_launch_count+1:1}),this.renewUtms(t.query),this.sdk.options.auto_track.app_launch&&this.sdk.track({event:"WECHAT_WXA__LAUNCH",props:{s_launchCount:this.context.app_launch_count,s_isFirstVisit:this.context.is_first_open?"YES":"NO",s_entryPagePath:this.context.currpage,s_sourceAppId:t.referrerInfo&&t.referrerInfo.appId||"",s_originScene:this.context.scene}})}),0)}onAppShow(t,e){setTimeout((()=>{this.mergeContext({scene:t.scene,currpage:t.path,active_t:(new Date).getTime(),firstpage:this.context.firstpage||t.path,is_first_open:!this.context.firstpage,app_show_count:this.context.app_show_count>0?this.context.app_show_count+1:1}),this.renewUtms(t.query),this.sdk.options.auto_track.app_show&&this.sdk.track({event:"WECHAT_WXA__SHOW",props:{s_hideCount:this.context.app_hide_count,s_showCount:this.context.app_show_count,s_entryPagePath:this.context.firstpage,s_sourceAppId:t.referrerInfo&&t.referrerInfo.appId||"",s_originScene:this.context.scene}})}),0)}onAppHide(t,e){setTimeout((()=>{if(this.mergeContext({inactive_t:(new Date).getTime(),app_hide_count:this.context.app_hide_count>0?this.context.app_hide_count+1:1}),this.sdk.options.auto_track.app_hide){let t=Math.round((this.context.inactive_t-this.context.active_t)/1e3);t<0&&(t=0),this.sdk.track({event:"WECHAT_WXA__HIDE",props:{s_hideCount:this.context.app_hide_count,s_showCount:this.context.app_show_count,s_visitDepth:this.context.total_page_list.length,s_stayTime:t,s_entryPagePath:this.context.firstpage,s_exitPagePath:this.context.prevpage,s_originScene:this.context.scene}})}this.mergeContext({total_page_list:[]}),this.sdk.flush()}),0)}onPageShow(t,e){setTimeout((()=>{let t=e.route+JSON.stringify(e.options);if(this.context.total_page_list.indexOf(t)<0&&this.context.total_page_list.push(t),this.mergeContext({open_t:(new Date).getTime(),currpage:e.route,total_page_list:this.context.total_page_list}),this.sdk.options.auto_track.app_show){let t={event:"WECHAT_WXA__PAGE_SHOW",props:{s_pagePath:this.context.currpage||"",s_lastPagePath:this.context.prevpage||"",s_isEntryPage:this.context.currpage===this.context.firstpage?"YES":"NO",s_originScene:this.context.scene}};e.options.s_promoterOpenId&&(t.props.s_promoterOpenId=e.options.s_promoterOpenId),this.sdk.track(t)}}),0)}onPageHide(t,e){if(this.sdk.options.auto_track.page_leave){const t=1e3*this.sdk.options.auto_track.page_leave_max_duration;let n=(new Date).getTime()-this.context.open_t;n>t?n=t:n<0&&(n=0);let s={event:"WECHAT_WXA__PAGE_HIDE",props:{s_pagePath:this.context.currpage||"",s_lastPagePath:this.context.prevpage||"",s_isEntryPage:this.context.currpage===this.context.firstpage?"YES":"NO",s_originScene:this.context.scene,s_pageStayTime:~~(n/1e3)}};e.options.s_promoterOpenId&&(s.props.s_promoterOpenId=e.options.s_promoterOpenId),this.sdk.track(s)}this.mergeContext({prevpage:this.context.currpage})}onPageShareAppMessage(t,e){return this.sdk.options.auto_track.page_share&&this.sdk.track({event:"WECHAT_WXA__PAGE_SHARE",props:{s_pagePath:this.context.currpage||this.sdk.options.defaultPath}}),t[1]}}var s={exports:{}};!function(e,n){var s=9007199254740991,i="[object Map]",o="[object Promise]",r="[object Set]",a="[object WeakMap]",c="[object DataView]",u=/^\[object .+?Constructor\]$/,p="object"==typeof t&&t&&t.Object===Object&&t,l="object"==typeof self&&self&&self.Object===Object&&self,h=p||l||Function("return this")(),d=n&&!n.nodeType&&n,g=d&&e&&!e.nodeType&&e,f=g&&g.exports===d;var m,_,y,v=Function.prototype,I=Object.prototype,w=h["__core-js_shared__"],x=(m=/[^.]+$/.exec(w&&w.keys&&w.keys.IE_PROTO||""))?"Symbol(src)_1."+m:"",O=v.toString,b=I.hasOwnProperty,S=I.toString,P=RegExp("^"+O.call(b).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),k=f?h.Buffer:void 0,A=I.propertyIsEnumerable,j=k?k.isBuffer:void 0,T=(_=Object.keys,y=Object,function(t){return _(y(t))}),M=V(h,"DataView"),C=V(h,"Map"),E=V(h,"Promise"),D=V(h,"Set"),W=V(h,"WeakMap"),N=!A.call({valueOf:1},"valueOf"),H=z(M),U=z(C),R=z(E),$=z(D),q=z(W);function Q(t){if(!G(t)||function(t){return!!x&&x in t}(t))return!1;var e=B(t)||function(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}(t)?P:u;return e.test(z(t))}function V(t,e){var n=function(t,e){return null==t?void 0:t[e]}(t,e);return Q(n)?n:void 0}var F=function(t){return S.call(t)};function z(t){if(null!=t){try{return O.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function K(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&L(t)}(t)&&b.call(t,"callee")&&(!A.call(t,"callee")||"[object Arguments]"==S.call(t))}(M&&F(new M(new ArrayBuffer(1)))!=c||C&&F(new C)!=i||E&&F(E.resolve())!=o||D&&F(new D)!=r||W&&F(new W)!=a)&&(F=function(t){var e=S.call(t),n="[object Object]"==e?t.constructor:void 0,s=n?z(n):void 0;if(s)switch(s){case H:return c;case U:return i;case R:return o;case $:return r;case q:return a}return e});var Y=Array.isArray;function L(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=s}(t.length)&&!B(t)}var X=j||function(){return!1};function B(t){var e=G(t)?S.call(t):"";return"[object Function]"==e||"[object GeneratorFunction]"==e}function G(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}e.exports=function(t){if(L(t)&&(Y(t)||"string"==typeof t||"function"==typeof t.splice||X(t)||K(t)))return!t.length;var e=F(t);if(e==i||e==r)return!t.size;if(N||function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||I)}(t))return!T(t).length;for(var n in t)if(b.call(t,n))return!1;return!0}}(s,s.exports);var i=s.exports;class o{constructor(t,e){this.endpoint=t,this.token=e}post(t,n={},s={},o={}){return new Promise(((r,a)=>{(s=s||{}).token=this.token;const c={url:`${this.endpoint}${t}${e.query2str(s)}`,data:n,method:"POST",success:function(t){t.statusCode<400?r(t.data):a("request error")},fail:function(){a("request error")},header:{"content-type":"application/json"}};i(o)||(c.header=Object.assign(Object.assign({},c.header),o)),wx.request(c)}))}}class r{set(t,e){try{wx.setStorageSync(t,e)}catch(t){}}get(t){try{let e=wx.getStorageSync(t);if(e)return e}catch(t){return null}}del(t){try{wx.removeStorageSync(t)}catch(t){}}clear(){try{wx.clearStorageSync()}catch(t){}}}class a{getOSInfo(){return this.osinfo?Promise.resolve(this.osinfo):new Promise(((t,e)=>{try{const e=wx.getSystemInfoSync();this.osinfo={brand:e.brand,model:e.model,platform:e.platform,screen_width:e.screenWidth,screen_height:e.screenHeight};let n=e.system;if("string"==typeof n){let t=n.split(" ")[0],e=n.split(" ")[1];this.osinfo.os=t,this.osinfo.os_version=e}t(this.osinfo)}catch(t){e(t)}}))}getNetworkType(){return this.netType?Promise.resolve(this.netType):new Promise(((t,e)=>{wx.getNetworkType({success:e=>{this.netType=this.networkTypeStr(e.networkType),t(this.netType)},fail:e})}))}networkTypeStr(t){switch(t){case"wifi":return"WIFI";case"2g":case"3g":case"4g":case"5g":return"WWAN";case"none":return"OFFLINE";default:return"UNKNOWN"}}}var c={setup:function(t){const s=new o(t.server_url,t.token),i=new r,c=new a,u=e.mergeSDKOptions(t);if(!u.token||!u.server_url)return console.error("SDK initialize failed, token or server_url is empty"),new e.MiniAppDummySDK(u);const p=e.getSDKInstance(s,i,c,Object.assign(Object.assign({},u),{channelIdType:"wxa.openId"})),l=App,h=Page,d=Component;function g(t,e,n,s){if(t[e]){let i=t[e];t[e]=function(t){n.call(s,t,this),i.call(this,t,this)}}else t[e]=function(t){n.call(s,t,this)}}function f(t,e,n,s){if(t[e]){let i=t[e];t[e]=function(t){let e=i.call(this,t);return n.call(s,t,this),e}}else t[e]=function(t){n.call(s,t,this)}}const m=new n(i,p);return App=function(t){return g(t,"onLaunch",m.onAppLaunch,m),g(t,"onShow",m.onAppShow,m),g(t,"onHide",m.onAppHide,m),l.call(this,t)},Page=function(t){return g(t,"onShow",m.onPageShow,m),g(t,"onHide",m.onPageHide,m),"function"==typeof t.onShareAppMessage&&f(t,"onShareAppMessage",m.onPageShareAppMessage,m),h.call(this,t)},Component=function(t){return Object.prototype.hasOwnProperty.call(t,"methods")&&(g(t.methods,"onShow",m.onPageShow,m),g(t.methods,"onHide",m.onPageHide,m),"function"==typeof t.methods.onShareAppMessage&&f(t.methods,"onShareAppMessage",m.onPageShareAppMessage,m)),d.call(this,t)},p}};module.exports=c;
