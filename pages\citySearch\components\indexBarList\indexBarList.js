Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    cityList: {
      type: Object,
      observer(val) {
        this.handleData(val);
      }
    },
    currentCity: {
      type: String,
      value: '',
    },
    type: {
      type: String,
      observer(val) {
        if (val === 'all') {
          this.setData({
            currentLetter: this.data.indexArray[0]
          })
        }
      }
    },
    currentSearchCities: {
      type: Array,
      value: []
    },
    currentScrollTop: {
      type: Number,
      value: 0,
      observer(val) {
        this.handleHighLightLetter(val);
      }
    }
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行

      // this.getElementPositions()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    indexArray: [],
    currentLetter: '',
    elementsPosition: {} // 用于存储每个元素的位置信息
  },
  /**
   * 组件的方法列表
   */
  methods: {
    getElementPositions() {
      const positions = {};
      const query = wx.createSelectorQuery().in(this);
      const {
        indexArray
      } = this.data
      indexArray.forEach(letter => {
        query.select(`#city_${letter}`).boundingClientRect(rect => {
          if (rect) {
            positions[letter] = rect;
          }
          this.data.elementsPosition = positions
          this.data.letters = Object.keys(positions).sort();

        }).exec();
      });
    },
    handleData(val) {
      const indexArray = Object.keys(val);
      this.setData({
        indexArray,
        currentLetter: indexArray[0],
      })
      this.getElementPositions()
    },
    clickLetter(e) {
      const letter = e.currentTarget.dataset.letter; // 获取点击的字母
      this.setData({
        currentLetter: letter
      })
      // 弹提示窗
      wx.showToast({
        title: letter,
        icon: 'none',
      })
      const query = wx.createSelectorQuery().in(this); // 确保在当前组件中执行
      query.select(`#city_${letter}`).boundingClientRect(rect => {
        if (rect) {
          // 控制父组件滚动
          this.triggerEvent('changeScroll', {
            top: rect.top
          });
        } else {
          console.log(`Element with id #city_${letter} not found.`);
        }
      }).exec();
    },
    handleChangeCity(e) {
      const city = e.currentTarget.dataset.city;
      this.triggerEvent("changeCity", {
        city
      });
    },
    handleHighLightLetter(val) {
      const {
        elementsPosition,
        letters
      } = this.data;
      if (!letters || letters.length == 0) {
        return
      }
      for (let l = this.data.letters.length - 1; l >= 0; l--) {
        const item = this.data.letters[l]
        if (val > elementsPosition[item].top - 200 && val < elementsPosition[item].top + elementsPosition[item].height + 200) {
          this.setData({
            currentLetter: item,
          })
          return true; // 返回 true 会停止循环
        }
      }
    }
  },
})
