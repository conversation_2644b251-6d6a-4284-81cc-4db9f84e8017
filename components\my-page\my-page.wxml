<wxs module="tools" src="./tools.wxs"></wxs>
<view bind:touchstart="start" bind:touchend="end">
  <slot></slot>
</view>
<!-- 隐私协议弹窗  overallModal.secret userInfo.policyVersion?content:firstContent-->
<my-popup show="{{ overallModal.secret&&userInfo.id }}" overlayBackground="{{autoPrincy?'rgba(0,0,0,.7)':'#f4e8d0'}}"
  zIndex="{{10000002}}" closeable="{{false}}" title="{{title}}" isCenter="{{false}}"
  content="{{userInfo.policyVersion?content:firstContent}}" showType="editor" bindclose="close" bindconfirm="confirm"
  bindcancel="cancel" data-key="secret">
  <view>
    <view class="checkbox">
      <view class="checkbox-item" data-key="selectAll" bind:tap="onCheckboxChange">
        <view class="checkbox-quadrate">
          <view class="checkbox-quadrate-box {{selectAll?'active':''}}">
            <view wx:if="{{selectAll}}" class="iconfont icon-Mark iconfontColor"></view>
          </view>
          <view class="quadrate-text all" style="display: flex; align-items: center;">
            下述内容，本人均已理解并同意
          </view>
        </view>
      </view>
      <!-- 第一次 !userInfo.policyVersion-->
      <block wx:if="{{!userInfo.policyVersion}}">
        <view class="checkbox-item" data-key="allAgreements" bind:tap="onCheckboxChange">
          <view class="checkbox-round" style="align-items: start;">
            <view style="padding-top: 2rpx;margin-top: 2rpx;" class="checkbox-round-box {{allAgreements?'active':''}}">
              <view wx:if="{{allAgreements}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text" style="flex: 1;">本人已仔细阅读，理解并同意<text class="underline-text" catchtap="Privacy"
                data-type="{{1}}">《服务使用协议》</text>、<text catchtap="Privacy" data-type="{{2}}"
                class="underline-text">《隐私与个人信息保护政策》</text>与<text catchtap="Privacy" data-type="{{3}}"
                class="underline-text">《无印良品MUJI会员小程序隐私保护指引》</text></view>
          </view>
        </view>
        <view class="checkbox-item" data-key="ageChecked" bind:tap="onCheckboxChange">
          <view class="checkbox-round">
            <view class="checkbox-round-box {{ageChecked?'active':''}}">
              <view wx:if="{{ageChecked}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text">本人已满14岁，可自行授权个人信息处理</view>
          </view>
        </view>

        <view class="checkbox-item" wx:if="{{isShowAgreeBrand}}" data-key="isAgreeBrand" bind:tap="onCheckboxChange">
          <view class="checkbox-round">
            <view class="checkbox-round-box {{isAgreeBrand?'active':''}}">
              <view wx:if="{{isAgreeBrand}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text">本人同意接收MUJI发送的营销短信、电话通知</view>
          </view>
        </view>
        <view class="checkbox-item" data-key="personalPrivacyPolicy" bind:tap="onCheckboxChange">
          <view class="checkbox-round" style="align-items: start;">
            <view style="padding-top: 2rpx;margin-top: 2rpx;"
              class="checkbox-round-box {{personalPrivacyPolicy?'active':''}}">
              <view wx:if="{{personalPrivacyPolicy}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text" style="flex: 1;">本人理解并同意MUJI为了提供更好的服务，将按照<text catchtap="Privacy"
                data-type="{{2}}"
                style="text-decoration: underline;font-weight:600">《隐私与个人信息保护政策》</text>的记述内容委托处理、共享个人信息
            </view>
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="checkbox-item" data-key="agree1" bind:tap="onCheckboxChange"
          wx:if="{{tools.includes(privacyData.versionUpdContent,'1')}}">
          <view class="checkbox-round" style="align-items: start;">
            <view style="padding-top: 2rpx;margin-top: 2rpx;" class="checkbox-round-box {{agree1?'active':''}}">
              <view wx:if="{{agree1}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text" style="flex: 1;">本人已仔细阅读，理解并同意<text class="underline-text" catchtap="Privacy"
                data-type="{{1}}">《服务使用协议》</text></view>
          </view>
        </view>
        <view class="checkbox-item" data-key="agree2" bind:tap="onCheckboxChange"
          wx:if="{{tools.includes(privacyData.versionUpdContent,'2')}}">
          <view class="checkbox-round" style="align-items: start;">
            <view style="padding-top: 2rpx;margin-top: 2rpx;" class="checkbox-round-box {{agree2?'active':''}}">
              <view wx:if="{{agree2}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text" style="flex: 1;">本人已仔细阅读，理解并同意<text catchtap="Privacy" data-type="{{3}}"
                class="underline-text">《无印良品MUJI会员小程序隐私保护指引》</text></view>
          </view>
        </view>
        <view class="checkbox-item" data-key="agree3" bind:tap="onCheckboxChange"
          wx:if="{{tools.includes(privacyData.versionUpdContent,'3')}}">
          <view class="checkbox-round" style="align-items: start;">
            <view style="padding-top: 2rpx;margin-top: 2rpx;" class="checkbox-round-box {{agree3?'active':''}}">
              <view wx:if="{{agree3}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text" style="flex: 1;">本人理解并同意MUJI为了提供更好的服务，将按照<text catchtap="Privacy"
                data-type="{{2}}"
                style="text-decoration: underline;font-weight:600">《隐私与个人信息保护政策》</text>的记述内容委托处理、共享个人信息
            </view>
          </view>
        </view>
        <view class="checkbox-item" wx:if="{{isShowAgreeBrand}}" data-key="isAgreeBrand" bind:tap="onCheckboxChange">
          <view class="checkbox-round">
            <view class="checkbox-round-box {{isAgreeBrand?'active':''}}">
              <view wx:if="{{isAgreeBrand}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text">本人同意接收MUJI发送的营销短信、电话通知</view>
          </view>
        </view>
        <view class="checkbox-item" data-key="ageChecked" bind:tap="onCheckboxChange">
          <view class="checkbox-round">
            <view class="checkbox-round-box {{ageChecked?'active':''}}">
              <view wx:if="{{ageChecked}}" class="iconfont icon-Mark iconfontColor"></view>
            </view>
            <view class="quadrate-text">本人已满14岁，可自行授权个人信息处理</view>
          </view>
        </view>
      </block>
    </view>
    <view class="page-btn">
      <button id="agree-btn" class="page-button confirm" open-type="agreePrivacyAuthorization"
        bindagreeprivacyauthorization="confirm" bindtap="confirm" data-key="secret">同意</button>
      <view class="page-button cancel" bindtap="cancel" data-key="secret">不同意,仅浏览</view>
    </view>
  </view>
</my-popup>

<!-- 接口报错 -->
<!-- <my-popup show="{{ overallModal.fail }}" closeable="{{false}}" title="报错标题" confirmText="我知道了" content="接口报错啦"
  bindclose="close" bindconfirm="confirm" bindcancel="cancel" data-key="fail" showType="normal">
</my-popup> -->

<!-- 门店定位 -->
<my-popup show="{{ overallModal.store }}" closeable="{{false}}" title="开启位置未授权" confirmText="开启" cancelText="暂不开启"
  content="请点击开启并在设置页允许“使用我的地理位置”，开启授权后，将为您匹配附近的门店及专属顾问" bindclose="close" bindconfirm="confirm" bindcancel="cancel"
  data-key="store" showType="normal">
</my-popup>

<!-- 订阅消息开启 -->
<my-popup show="{{ overallModal.subscribe }}" closeable="{{false}}" title="温馨提示" confirmText="前往开启"
  content="开启小程序通知，及时获取更多咨询。" bindclose="close" bindconfirm="confirm" bindcancel="cancel" data-key="subscribe"
  showType="normal">
  <image src="{{$cdn}}/subscribe.png" style="width:508rpx;height:397rpx;;margin-bottom:36rpx;margin-top:52rpx;"></image>
</my-popup>


<!-- 冻结用户 -->
<my-popup show="{{ overallModal.freeze }}" overlay="{{true}}" closeable="{{false}}" title="提示" confirmText="我知道了"
  overlayClick="{{true}}" content="您的会员账号存在异常，已暂停会员服务。若有疑问请联系客服：************（周一至周日，9:00-22:00，国家法定节假日除外）。"
  bindconfirm="confirm" bindclose="close" data-key="freeze" showType="normal">
</my-popup>

<!-- 未绑定手机号 -->
<my-popup show="{{ overallModal.phone }}" overlay="{{true}}" closeable="{{false}}" title="您还未绑定手机号" confirmText="我知道了"
  overlayClick="{{true}}" content="邀请您绑定会员手机号，绑定后可为您同步会员权益信息" bindconfirm="confirm" bindclose="close" data-key="phone"
  showType="normal">
</my-popup>


<!-- 全局图片弹窗  带一个热区 -->
<jumpModal show="{{overallModal.jumpData}}" jumpData="{{overallModal.jumpData}}" bindclose="close" bindconfirm="confirm"
  bindcancel="cancel" data-key="jumpData"></jumpModal>


<view class="tips" wx:if="{{expirePointsNum && !tipsShow}}">
  本月{{expirePointsNum}}积分即将过期
  <image class="tips-image" bindtap="closeTips" src="{{$cdn}}/close.png" mode="" />
</view>

<!-- 白色背景 -->
<view class="selfModal" wx:if="{{overallModal.white}}" style="background:#fff">
  <image src="{{$cdn}}/loading.png" style="width:150rpx;height:150rpx;"></image>
</view>

<!-- 黄色背景 -->
<view class="selfModal" wx:if="{{overallModal.yellow}}" style="background:#f4e8d0"></view>
<!-- 全局覆盖层 可以滑动 禁止穿透 点击弹窗隐私协议 -->
<!-- <view class="selfModal" bindtap="reveal" bind:touchstart="start" bind:touchend="end" style="pointer-events: {{move?'none':'auto'}};" wx:if="{{showModal}}"></view> -->

<!-- <view class="selfModal" bindtap="reveal" wx:if="{{showModal}}" bindtouchmove="noTouch"></view> -->

<!-- 全局loading -->
<van-popup style="--popup-background-color: transparent;--overlay-background-color:transparent" lock-scroll
  closeable="{{false}}" zIndex="10000006" wx:if="{{ loading }}" show="{{ loading }}">
  <image src="{{loadingInfo.loading}}" wx:if="{{realLoading}}"
    style="width:{{loadingInfo.loadingWidth}}rpx;height:{{loadingInfo.loadingHeight}}rpx;"></image>
</van-popup>
<!-- 底部tabBar安全区域 -->
<block wx:if="{{isTabBarPage}}">
  <view class="my-page-safe-tab-bar"></view>
  <!-- iPhoneX底部安全区域 -->
  <view class="my-page-safe-bottom"></view>
</block>