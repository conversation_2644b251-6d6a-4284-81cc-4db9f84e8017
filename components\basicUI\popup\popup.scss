.mask-pop {
  position: fixed;
  right: 0;
  left: 0;
  height: 100%;
  background-color: rgba(0, 0, 0, .7);
  z-index: 999999;
  overscroll-behavior: contain;
}

.basic-popup {
  left: 0;
  position: fixed;
  box-sizing: border-box;
  max-height: 100%;
  overflow-y: hidden;
  background-color: #fff;
  width: 100%;
  display: block;
  // -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.block {
  width: 120px;
  height: 120px;
  background-color: #fff;
}
