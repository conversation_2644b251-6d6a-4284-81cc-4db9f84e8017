// pages/exchangeGifts/exchangeGifts.js
const app = getApp()

import {
  getProductList,
  getBannerList,
  getUserPoint,
} from '../../api/index.js'

Page({
  /**
   * 页面的初始数据
   */
  data: {
    pageSize: 20,
    pageNum: 1,
    promotionList: [], // 推广位数据
    productList: [],
    currentShelfId: undefined,
    bannerContent: '',
    showFilterDialog: false, // 展示筛选框
    topHeight: app.globalData.navBarHeight, // 包括导航条高度
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight,
    menuButtonBottom: app.globalData.menuButtonBottom,
    filterData: {}, // 筛选数据
    currentPoint: 0,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getPoint();
    const {
      pageSize,
      pageNum,
      navHeight
    } = this.data;
    this.getList({
      pageSize,
      pageNum,
    });
    const that = this; // 保存当前页面实例
    wx.createSelectorQuery().select('#fixedTop').boundingClientRect(function (rect) {
      console.log('rect.height', rect);
      that.setData({
        topHeight: rect.bottom,
      })
    }).exec();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  getList(params) {
    const {
      pageSize
    } = this.data;
    getProductList({
      pageSize,
      ...params
    }).then((res) => {
      const {
        data
      } = res;
      this.setData({
        productList: data.list,
        currentShelfId: data.list[0].shelfId,
      })
      getBannerList({
        shelfId: data.list[0].shelfId,
      }).then(v => {
        if (v.data) {
          this.setData({
            bannerContent: v.data.content,
          })
        }

      })
    })
  },
  handleFilter(e) {
    console.log('处理筛选e', e)
    const {
      filterValue
    } = e.detail;
    this.setData({
      pageNum: 1,
      filterData: filterValue,
    })
    this.getList({
      pageNum: 1,
      ...filterValue,
    })
  },
  async getPoint() {
    const res = await getUserPoint();
    if (res.code === 0) {
      this.setData({
        currentPoint: res.data.pointsNum,
      })
    }
  }
})
