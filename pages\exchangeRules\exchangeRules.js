import {
  getTemplateByType,
} from '../../api/index.js'
Page({
  data: {
    loading: false,
    info: {

    }
  },

  async onLoad(options) {
    await this.getInfo()
    let {
      info: {
        pageSetting
      }
    } = this.data
    // 禁止分享
    if (!pageSetting.isShare) {
      wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },
  onShow() {

  },
  // 获取模板数据
  getInfo() {
    this.setData({
      loading: true
    })
    return getTemplateByType({
      //  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
      templateType: 2,
      // pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多 9-会员规则  12-无印列表
      pageType: 16,
    }).then(res => {
      res.data.content = res.data?.content ? JSON.parse(res.data.content) : {}
      res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
      res.data.navSetting = res.data.content.navSetting; // 导航
      res.data.pageSetting = res.data.content.pageSetting; // 页面设置
      res.data.componentSetting = res.data.content.componentSetting; // 组件设置
      this.setData({
        info: res.data
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  // 分享
  goShare(e) {
    let {
      shareTitle,
      shareImg,
      sharePath
    } = e.detail
    this.data.shareInfo = {
      shareTitle,
      shareImg,
      sharePath
    }
  },
  onShareAppMessage() {
    let {
      info: {
        pageSetting
      },
      options
    } = this.data
    // 按钮分享
    if (this.data.shareInfo) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = this.data.shareInfo;
      this.data.shareInfo = ''
      return {
        title: shareTitle || '',
        imageUrl: shareImg || '',
        path: sharePath || ''
      }
    }
    // 页面分享
    return {
      title: pageSetting.shareTitle || '',
      imageUrl: pageSetting.shareImg || '',
      query: {
        id: options.id
      },
      path: pageSetting.sharePath || ''
    }
  }
})
