<!--pages/couponDetail/couponDetail.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>礼券详情</text>
      </view>
    </custom-header>
    <view class="page-coupon">
      <view class="page-coupon-top"
        style="background-image: url({{$cdn}}/coupon/coupon-detail-background.png);background-size: 100%;z-index: 99;">
        <view class="coupon-title">{{couponObj.couponName}}</view>
        <view class="coupon-des">{{couponObj.couponDesc}}</view>
        <view class="coupon-date">有效期至：{{couponObj.couponEndTime}}</view>
        <view style="width: 100%;height: 60rpx;z-index: 1;"></view>
        <view class="coupon-image {{ couponObj.couponStatus != '1'?'active':''}}">
          <image style="width: 135px; height: 135px;" src="{{canvasImg}}" mode="" />
          <canvas class="coupon-qrCode" style="width: 135px; height: 135px;" canvas-id="myQrcode"></canvas>
        </view>
        <block wx:if="{{couponObj.couponId}}">
          <view class="coupon-tips" style="display: flex;align-items: center;justify-content:center;margin-top:0;"
            wx:if="{{couponObj.couponStatus == '1'}}" bindtap="getDetail">
            到店出示核销，每30秒自动刷新
            <image style="width: 22rpx; height: 22rpx;margin:20rpx 10rpx;" src="{{$cdn}}/store/refesh.png" />
          </view>
          <view class="coupon-tips" wx:else>
            {{couponObj.couponStatus == '1'?'到店出示核销，每30秒自动刷新':couponObj.couponStatus== '2'?'已使用':couponObj.couponStatus
            ==
            '3'?'已过期':'已作废'}}
          </view>
        </block>


        <!-- <view class="coupon-code" bindtap="copyText" data-key="{{couponObj.couponCode}}">
          <view class="coupon-code-txt">{{couponObj.couponCode}}
            <view class="copy">复制</view>
          </view>
        </view> -->
        <!-- <view class="coupon-time" wx:if="{{couponObj.couponStatus == '1'}}"><text>{{downTime}}s</text> 后自动刷新，截图使用无效</view> -->
      </view>
      <view class="coupon-BarCode ">
        <view class="coupon-BarCode-hearder">
          <view class="coupon-BarCode-text">
            条形码
          </view>
          <view class="coupon-BarCode-btn">
            <view class="coupon-BarCode-ti" bindtap="toggleContent">
              {{isExpanded ? '展开': '收起'}}
              <image class="toggle-icon" src="{{$cdn}}/{{isExpanded?'unfold.png':'pack-up.png'}} " mode=""></image>
            </view>
          </view>

        </view>
        <view wx:if="{{!isExpanded}}" class="coupon-BarCode-code {{ couponObj.couponStatus != '1'?'active':''}}">
          <!-- wx:if="{{ couponObj.couponStatus == '1'}}"  wx:else-->
          <image class="barcode-img" src="{{barcodeImg}}" />
          <canvas class="code" id="barcodeCoupon" type="2d" />

        </view>
      </view>
      <view class="coupon-rule">
        <ruleComponent ruleDesc="{{couponObj.ruleDesc||'暂无规则'}}"></ruleComponent>
      </view>
    </view>
  </view>
</my-page>