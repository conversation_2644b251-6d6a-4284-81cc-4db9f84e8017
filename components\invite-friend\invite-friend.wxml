<van-popup style="--popup-background-color: transparent; --overlay-background-color:{{overlayBackground}}" lock-scroll closeable="{{false}}" zIndex="{{zIndex}}" show="{{ show }}" safe-area-inset-bottom="{{true}}" custom-style="background-color: transparent">
	<view class="inviteFriend">
		<view style="min-height:200rpx;flex-grow:1;width:100%"></view>
		<view class="inviteFriend-content">
			<image class="inviteFriend-img" src="{{src}}" mode="widthFix" />
		</view>
		<view style="min-height:450rpx;max-height:580rpx;flex-grow:10;width:100%"></view>
		<view class="inviteFriend-action">
			<!-- 操作按钮区域 -->
			<view class="inviteFriend-bottom">
				<!-- 分享好友按钮 -->
				<button class="inviteFriend-button" open-type="share" bindtap="onShare">
					<image class="inviteFriend-button-icon" src="{{$cdn}}/firendShip/shareFriend.png"></image>
					<text class="inviteFriend-button-text">分享好友</text>
				</button>

				<!-- 下载海报按钮 -->
				<button class="inviteFriend-button" bindtap="onDownload">
					<image class="inviteFriend-button-icon" src="{{$cdn}}/firendShip/downPoster.png"></image>
					<text class="inviteFriend-button-text">下载海报</text>
				</button>
			</view>
			<!-- 取消按钮 -->
			<view class="inviteFriend-cancel">
				<button class="inviteFriend-cancel-btn" bindtap="close">取消</button>
			</view>
		</view>
	</view>
</van-popup>

<!-- 相册权限开启 -->
<my-popup show="{{ tip }}" closeable="{{false}}" title="温馨提示" confirmText="前往开启" cancelText="暂不开启"
  content="该功能需要获取您保存到相册的权限，请前往开启。" bindcancel="cancel" bindconfirm="confirm">
</my-popup>