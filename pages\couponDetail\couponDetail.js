import { usercoupondetail } from "../../api/index";
// import drawQrcode from '../../utils/weapp.qrcode'
const drawQrcode = require("../../utils/weapp.qrcode");
import { CODE128 } from "wxapp-barcode";
const dayjs = require('../../utils/dayjs.min')
const app = getApp();
Page({
  /**
   * 页面的初始数据
   */
  data: {
    loading: false,
    canvasImg: null,
    isExpanded: true,
    couponObj: {},
    barcodeImg: null,
    downTime: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) { },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow(options) {
    this.setData({
      loading: true,
    });
    this.getDetail();
  },
  toggleContent: app.debounce(async function (e) {
    this.setData({
      isExpanded: !this.data.isExpanded,
    });
    this.getCode();
  }, 100),
  getCode() {
    const { couponCode, couponStatus, dynamicCouponCode } = this.data.couponObj;
    CODE128(
      "#barcodeCoupon",
      couponStatus == 1 ? dynamicCouponCode : couponCode,
      {
        canvasType: "2d",
        width: 1054, // 增加宽度提高清晰度
        height: 250, // 增加高度提高清晰度
      }
    );

    const query = wx.createSelectorQuery();
    query
      .select("#barcodeCoupon")
      .fields({
        node: true,
        size: true,
      })
      .exec((res) => {
        if (res[0]) {
          const canvas = res[0].node;
          // 设置canvas大小
          let time = setTimeout(() => {
            clearTimeout(time)
            wx.canvasToTempFilePath({
              canvas: canvas,
              width: 550,
              height: 48,
              fileType: "png",
              type: "2d",
              quality: 1, // 设置最高质量
              success: (res) => {
                this.setData({
                  barcodeImg: res.tempFilePath,
                });
              },
              fail: (err) => {
                console.error(err);
              },
            });
          }, 300);
        }
      });
  },
  getDetail() {
    let couponCode = this.data.options.couponCode
    let couponId = this.data.options.couponId
    usercoupondetail({
      couponId,
      couponCode,
    })
      .then(async (res) => {
        this.setData({
          couponObj: res.data,
        });
        this.drawQrcode();
        // await this.getCode()
        if (this.time) {
          clearTimeout(this.time)
          this.time = null
        }
        if (this.down) {
          clearInterval(this.down)
          this.down = null
        }
        //  倒计时自动刷新二维码
        if (this.data.couponObj.couponStatus == 1) {
          // let downTime = Math.floor(this.data.couponObj.dynamicCouponTimestamp - dayjs().unix());
          let downTime = this.data.couponObj.expirationTimestamp || 0
          // this.setData({ downTime: downTime + 1 })
          // this.down = setInterval(() => {
          // 	this.setData({
          // 		downTime: this.data.downTime - 1
          // 	})
          // }, 1000);
          console.log(downTime,'downTimedownTimedownTimedownTime');
          if (downTime > 0) {
            this.time = setTimeout(() => {
              this.getDetail()
            }, downTime * 1000);
          }

        }
      })
      .finally(() => {
        this.setData({
          loading: false,
        });
      });
  },
  drawQrcode() {
    const { couponCode, couponStatus, dynamicCouponCode } = this.data.couponObj;
    drawQrcode({
      width: 135,
      height: 135,
      canvasId: "myQrcode",
      text: couponStatus == 1 ? dynamicCouponCode : couponCode,
    });
    const that = this;
    let time = setTimeout(() => {
      clearTimeout(time)
      wx.canvasToTempFilePath({
        x: 0,
        y: 0,
        width: 135,
        height: 135,
        fileType: "png",
        quality: 4, // 设置最高质量
        canvasId: "myQrcode",
        success: function (res) {
          that.setData({
            canvasImg: res.tempFilePath,
          });
        },
      });
    }, 300);
  },
  copyText(e) {
    const key = e.currentTarget.dataset.key;
    wx.setClipboardData({
      data: key,
      success: (res) => {
        console.log(res);
        wx.showToast({
          title: "复制成功",
        });
        // wx.getClipboardData({
        //   success: (success) => {

        //   }
        // })
      },
    });
  },

  onHide() {
    if (this.time) {
      clearTimeout(this.time)
      this.time = null
    }
    if (this.down) {
      clearInterval(this.down)
      this.down = null
    }

  },

  onUnload() {
    if (this.time) {
      clearTimeout(this.time)
      this.time = null
    }
    if (this.down) {
      clearInterval(this.down)
      this.down = null
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() { },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() { },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() { },
});
