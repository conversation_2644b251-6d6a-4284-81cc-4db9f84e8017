const app = getApp()
Component({
  properties: {
    title: {
      type: String,
      value: ''
    },
    showBack: {
      type: Boolean,
      value: true
    }
  },
  data: {
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    $cdn: wx.$config.ossImg
  },

  methods: {
    // 返回上一页 首页
    goBack: app.debounce(async function () {
      wx.navigateBack({
        fail: () => {
          wx.switchTab({ url: '/pages/index/index' })
        }
      })
    })
  }
})
