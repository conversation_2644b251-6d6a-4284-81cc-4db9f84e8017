const app = getApp()
import {
  lotteryUserGet_prizes,
  lotteryList
} from "../../api/index";
import {
  BlindBox,
  PrizeDraw,
  BlindBoxPrizeDraw,
  prizeTypeP,
  ShareTitle
} from "../../utils/lotteryCode"

Page({
  data: {
    distance: 0,
    pageSize: 10,
    pageNum: 1,
    total: 1,
    list: [],
    lotteryCodeStr: null
  },

  onLoad(options) {},



  onShow() {
    const isRegistered = app.globalData.userInfo.isMember > 0
    if (!isRegistered) { //增加逻辑如果未注册就去活动页面走注册的逻辑
      wx.$mp.redirectTo({
        url: `/member/pages/transferPage/transferPage`,
      })
    }
    this.data.pageNum = 1
    this.data.total = 1;
    this.data.pageSize = this.data.list.length >= 10 ? this.data.list.length : 10
    this.data.list = []
    this.getList()
  },
  onTapMore: app.debounce(function () {
    const isRegistered = app.globalData.userInfo.isMember > 0
    if (isRegistered) {
      wx.$mp.navigateTo({
        url: '/member/pages/lottery/lottery',
      })
    } else {
      wx.$mp.redirectTo({
        url: `/member/pages/transferPage/transferPage`,
      })
    }

  }),
  // 获取数据
  getList() {
    let {
      loading,
      pageNum,
      pageSize,
      total,
      list
    } = this.data;
    if (loading) return;
    if (list.length >= total) return;
    this.setData({
      loading: true
    });
    // lotteryList().then(response => {//获取lotteryCodeStr字段
    //   let arrCode = response.data.filter(item => item.lotteryCode).map(item => item.lotteryCode);
    //   let lotteryCodeStr = arrCode.join(',');
    //   this.setData({
    //     lotteryCodeStr: lotteryCodeStr
    //   });
    // console.log(response, 'ssss', arrCode, this.data.lotteryCodeStr);
    lotteryUserGet_prizes({
      pageNum,
      pageSize,
      lotteryCode: BlindBoxPrizeDraw,
      prizeType: prizeTypeP
    }).then(res => {
      if (res.code === 0) {
        res.data.list.forEach(item => {
          if (item.expireTime) {
            item.expireTimeA = item.expireTime.split(' ')[0]
          }
          // 1实物商品 2电子券 3积分 4优惠券 99空
          //  @ApiModelProperty(value = "状态 1:可使用 2:已使用 3:已过期 4:已作废")
          // private Integer couponState;
          // @ApiModelProperty(value = "奖品类型 1实物商品 2电子券 3积分 4优惠券 99空")
          // private Integer prizesType;
          if (item.couponState) {
            switch (item.couponState) {
              case 1:
                item.couponStateTxt = item.prizesType === 2 || item.prizesType === 4 ? '去使用' : '去核销';
                break;
              case 2:
                item.couponStateTxt = item.prizesType === 2 || item.prizesType === 4 ? '已使用' : '已核销';
                break;
              case 3:
                item.couponStateTxt = '已过期';
                break;
              case 4:
                item.couponStateTxt = '已作废';
                break;
            }
          } else {
            if (item.prizesType === 2 || item.prizesType === 4) {
              item.couponStateTxt = '去使用'
            } else {
              item.couponStateTxt = '去核销';
            }

          }
        });
      }
      console.log(res.data.list);
      if (pageNum == 1) {
        this.setData({
          list: res.data.list,
          total: res.data.count,
          pageNum: Math.ceil(res.data.list.length / 10) + 1,
          pageSize: 10
        });
      } else {
        this.setData({
          list: [...list, ...res.data.list],
          total: res.data.count,
          pageNum: pageNum + 1,
          pageSize: 10
        });
      }
    }).finally(() => {
      this.setData({
        loading: false
      });
    });
    // });
  },
  scroll(e) {
    let {
      distance
    } = this.data;
    let {
      scrollTop
    } = e.detail;
    if ((distance > 20) !== (scrollTop > 20)) {
      // 一个大于20，一个小于20
      this.setData({
        distance: scrollTop
      })
    }
  },
  GoAndUse: app.debounce(async function (e) {
    const {
      item
    } = e.currentTarget.dataset
    if (app.ifRegister()) {
      // app.subscribe('coupon').then(() => {
      wx.$mp.navigateTo({
        url: `/pages/couponDetail/couponDetail?couponId=${item.stockId}&couponCode=${item.couponCode}`,
        success: (result) => {},
        fail: (res) => {
          console.log(res);
        },
        complete: (res) => {},
      })
      // })
    }
  }),
  GoWriteoff: app.debounce(async function (e) {
    const {
      item
    } = e.currentTarget.dataset
    if (app.ifRegister()) {
      // app.subscribe('coupon').then(() => {
      wx.$mp.navigateTo({
        url: `/pages/couponDetail/couponDetail?couponId=${item.stockId}&couponCode=${item.couponCode}`,
        success: (result) => {},
        fail: (res) => {
          console.log(res);
        },
        complete: (res) => {},
      })
      // })
    }
  }),
  onReachBottom() {
    console.log('到底')
    this.getList()
  },
  onShareAppMessage(options) {
    if (options.from === 'button') {
      return {
        title: ShareTitle,
        imageUrl: this.data.$cdn + '/firendShip/share.png',
        // path: '/pages/index/index'
        path: 'member/pages/transferPage/transferPage'
      }
    } else {
      return {
        title: ShareTitle,
        imageUrl: this.data.$cdn + '/firendShip/share.png',
        // path: '/pages/index/index'
        path: 'member/pages/transferPage/transferPage'
      }
    }
  }
})
