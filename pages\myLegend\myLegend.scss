/* pages/myLegend/myLegend.wxss */

// page {
//   overflow: auto;
//   /* box-sizing: inherit; */
// }

.page-list {
  // padding-left:  var(--page-margin);

  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
  height: 100vh;
}

.list-content {

  flex: 1;
  height: 0;
  width: 100%;
  padding-bottom: var(--bottom);
}

.page-content {

  margin-left: var(--page-margin);
  margin-right: var(--page-margin);

  .legend-list {

    margin-top: 47rpx;


    // position: relative;


    .box-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;

      .title-txt {
        height: 45rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 32rpx;
        color: var(--text-black-color);
        line-height: 45rpx;
        letter-spacing: 1px;
        font-style: normal;
      }

      .box-btn {
        width: 160rpx;
        height: 58rpx;
        background: var(--text-black-color);
        border-radius: 5rpx;
        text-align: center;
        color: #fff;
        line-height: 58rpx;
        font-weight: 500;
        font-size: 24rpx;

        .box-btn-up {
          width: 24rpx;
          height: 24rpx;
          margin-left: 10rpx;
        }
      }
    }

    .box-line {
      width: 100%;
      height: 1rpx;
      border-top: 1rpx solid #EEEEEE;
    }
  }

}
