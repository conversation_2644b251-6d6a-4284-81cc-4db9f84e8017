/* pages/citySearch/citySearch.wxss */
.page-content {
  padding: 40rpx;
  display: flex;
  flex-direction: column;

  .search-box {
    width: 670rpx;
    height: 80rpx;
    background: #F5F5F5;
    border-radius: 5rpx;
    margin: 0 auto;
    box-sizing: border-box;
    padding: 0 20rpx;
    margin-bottom: 40rpx;
  }

  // .list-container {
  //   border: 10rpx solid red;
  // }

  .list-block {
    .title {
      height: 40rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #888888;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      margin-bottom: 20rpx;
    }
  }

  .divided {
    width: 620rpx;
    height: 2rpx;
    background-color: #eee;
    margin-bottom: 20rpx;
  }

  .unauthorized-box {
    margin-bottom: 40rpx;
  }

  .current-city {
    // border-bottom: 2rpx solid #eee;
    margin-bottom: 20rpx;
    position: relative;

    // &::after {
    //   content: '';
    //   display: block;
    //   width: 620rpx;
    //   position: absolute;
    //   left: 0;
    //   bottom: -20rpx;
    //   height: 2rpx;
    //   background-color: #eee;
    // }

    .name {
      height: 40rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN;
      font-weight: bold;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      display: flex;
      margin-bottom: 36rpx;

      .iconfont {
        font-size: 48rpx;
        font-weight: 400;
      }
    }
  }
}
