/**
 * 初始化canvas
 * @param context 创建选择器的上下文
 * @param selector 选择器
 */
export async function initCanvas(context, selector) {
  return new Promise((resolve, reject) => {
    context
      .createSelectorQuery()
      .select(selector)
      .fields({ node: true, size: true })
      .exec(([res]) => {
        try {
          const canvas = res.node
          const ctx = canvas.getContext('2d')
          const { width, height } = res
          const { pixelRatio, windowWidth } = wx.getWindowInfo()
          canvas.width = width * pixelRatio
          canvas.height = height * pixelRatio
          ctx.scale(pixelRatio, pixelRatio)
          const transValue = windowWidth / 750
          resolve({ canvas, ctx, transValue, width, height })
        } catch (err) {
          console.log(err)
          reject()
        }
      })
  })
}
/**
 * 创建canvas图片
 */
export async function createImage(canvas, src) {
  return new Promise((resolve, reject) => {
    const image = canvas.createImage()
    image.src = src
    image.onload = () => {
      resolve(image)
    }
    image.onerror = () => {
      reject()
    }
  })
}

/**
 * 打开微信设置
 * @param {string} content 提示内容
 * @param {boolean} withSubscriptions 是否订阅
 * @returns {Promise<boolean>} 返回值为false时，表示用户拒绝打开设置，否则返回授权结果
 */
export function openSetting(content, withSubscriptions = false) {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: '提示',
      content,
      success: res => {
        if (res.confirm) {
          wx.openSetting({ withSubscriptions, success: res => resolve(res) })
        } else {
          resolve(false)
        }
      }
    })
  })
}
/**
 * 返回活动首页
 */
export function goActivityHome() {
  const pages = getCurrentPages()
  const homePagePath = '/dress/pages/index/index'
  let delta = 0
  for (let i = pages.length - 1; i >= 0; i--) {
    if (pages[i].path === homePagePath) {
      delta = i
      break
    }
  }
  if (delta) {
    wx.navigateBack({ delta })
  } else {
    wx.redirectTo({ url: homePagePath })
  }
}
