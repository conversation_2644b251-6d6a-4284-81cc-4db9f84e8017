// components/coupon-item/coupon-item.js
import { usercouponreceive } from '../../api/index'
const app = getApp()
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {}
    },
    couponType: {
      type: [String, Number],
      value: null
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    isReceiving: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    toCoupon: app.debounce(async function (e) {
      const currentPage = wx.$mp.getCurrentPage();

      const { item } = e.currentTarget.dataset
      console.log(item, '0000000');
      // 埋点
      if (item.receiveStatus == 1) {// 去使用
        wx.$mp.track({
          event: 'myCoupon_goUse_click',
          props: {
            couponId: item.couponId,
          }
        })
      } else if (item.receiveStatus == 2) { // 领取并使用
        wx.$mp.track({
          event: 'myCoupon_getUse_click',
          props: {
            activityId: item.activityId,
          }
        })
      }
      if (item.receiveStatus == 1) {
        wx.$mp.navigateTo({
          url: `/pages/couponDetail/couponDetail?couponId=${item.couponId}&couponCode=${item.couponCode}`,
        })
      } else if (item.receiveStatus == 2 && !this.data.isReceiving) {
        this.setData({
          isReceiving: true
        })
        try {
          currentPage.setData({ loading: true })
          const res = await usercouponreceive({ activityId: item.activityId, receiveType: item.receiveType, couponId: item.couponId })
          await this.triggerEvent('change')
          await wx.showToast({
            title: '领券成功',
            icon: 'none',
          });
          await wx.nextTick(() => {
            wx.$mp.navigateTo({
              url: `/pages/couponDetail/couponDetail?couponId=${res.data.stockId}&couponCode=${res.data.couponCode}`,
            })
          })
        } catch (err) {

        }
        this.setData({
          isReceiving: false
        })
        currentPage.setData({ loading: false })
      }
      if (item.isUsed) {
        wx.$mp.navigateTo({
          url: `/pages/couponDetail/couponDetail?couponId=${item.couponId}&couponCode=${item.couponCode}`,
        })
      }
    }),

    toMeony: app.debounce(async function (e) {
      const { item } = e.currentTarget.dataset
      console.log(item, '0000000');
      if (item.receiveStatus == 1) {
        wx.$mp.navigateTo({
          url: `/pages/couponDetail/couponDetail?couponId=${item.couponId}&couponCode=${item.couponCode}`,
        })
      } else if (item.receiveStatus == 2 && !this.data.isReceiving) {
        this.setData({
          isReceiving: true
        })
        try {
          const res = await usercouponreceive({ activityId: item.activityId, receiveType: item.receiveType, couponId: item.couponId })
          await this.triggerEvent('change')
          await wx.showToast({
            title: '领券成功',
            icon: 'none',
          });
          await wx.nextTick(() => {
            wx.$mp.navigateTo({
              url: `/pages/couponDetail/couponDetail?couponId=${res.data.stockId}&couponCode=${res.data.couponCode}`,
            })
          })
        } finally {
          this.setData({
            isReceiving: false
          })
        }
      }
    })
  }
})
