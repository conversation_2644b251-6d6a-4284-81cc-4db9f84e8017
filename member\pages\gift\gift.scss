.lottery-header-more {
  margin-left: 20rpx;
  pointer-events: all;
  opacity: 1;
  width: 100%;
}

.lottery-header-more-img {
  width: 178rpx;
  height: 60rpx;
}

.page {
  width: 750rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;

  &-bg {
    position: sticky;
    z-index: 1;
    top: 0;
    width: 750rpx;
    height: 701rpx;
    // padding-bottom: 25rpx;
    background: #fff;
  }

  &-box {
    width: 100%;
    box-sizing: border-box;
    padding: 0 40rpx;
    display: flex;
    flex-wrap: wrap;



    &-item {
      position: relative;
      margin-bottom: 30rpx;

      &:nth-child(2n-1) {
        margin-right: 10rpx;
      }
    }

    &-bg {
      width: 330rpx;
      height: 514rpx;
      display: block;
    }

    &-content {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &-gift-img {
      text-align: center;
      margin-top: 53rpx;
      width: 240rpx;
      height: 267rpx;

      image {
        box-sizing: border-box;
        width: 240rpx;
        height: 267rpx;
      }
    }

    &-img {
      margin-top: 53rpx;
      width: 162rpx;
      height: 162rpx;
      background-color: #FAF9F6;
      box-sizing: border-box;
      border-radius: 50%;

      image {
        border: 2rpx solid #94243A;
        border-radius: 50%;
        z-index: 99;
        width: 162rpx;
        height: 162rpx;
        box-sizing: border-box;
      }
    }

    &-title {
      margin-top: 29rpx;
      text-align: center;
      width: 240rpx;
      height: 76rpx;
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 500;
      font-size: 26rpx;
      line-height: 38rpx;
      color: #231815;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    &-subTitle {
      &-time {
        font-family: SourceHanSansCN, MUJIFont2020;
        font-weight: 400;
        font-size: 20rpx;
        color: #231815;
        line-height: 28rpx;
      }

      width: 300rpx;
      height: 29rpx;
      text-align: center;
      margin-top: 14rpx;
      font-family: SourceHanSansCN,
      MUJIFont2020;
      font-weight: 350;
      font-size: 20rpx;
      color: #231815;
      line-height: 28rpx;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &-btn {
      margin-top: 55rpx;
      width: 300rpx;
      height: 87rpx;
      // background: rgba(255, 0, 0, .3);
      font-family: SourceHanSansCN;
      font-weight: 500;
      font-size: 30rpx;
      color: #231815;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &-opacity {
    position: fixed;
    left: 0;
    // top: 725rpx;
    top: 700rpx;
    right: 0;
    height: 135rpx;
    background: linear-gradient(180deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);
  }
}

.page-data-no {
  margin-top: 190rpx;
  width: 100%;
  text-align: center;

  &-img {
    width: 120rpx;
    height: 120rpx;
  }

  &-text {
    font-family: SourceHanSansCN, MUJIFont2020;
    font-weight: 400;
    font-size: 33rpx;
    color: #888888;
    line-height: 50rpx;
    letter-spacing: 1px;
    text-align: center;
    font-style: normal;
  }
}
