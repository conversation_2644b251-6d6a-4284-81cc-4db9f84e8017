.page-container {
  width: 100%;
  // min-height: 100vh;

  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;

  .page-content {
    // position: relative;
    // display: flex;
    // flex-direction: column;
    // height: 100%;
    // flex: 1;
    // height: 0;
  }

  .top-content {
    background: var(--homeVipBg);
    position: relative;
    width: 100%;
    height: 370rpx;
    z-index: 0;
    padding-top: 63rpx;
    box-sizing: border-box;
  }

  .right-jian {
    width: 18rpx;
    height: 18rpx;
    margin-left: 5rpx;
  }

  .card-solt {
    z-index: 4;
    position: absolute;
    height: 70rpx;
    width: 750rpx;
    left: 0;
    bottom: 0;
    background-size: 100%;
    pointer-events: none;
  }

  .rights-box {

    background-color: #fff;
    box-sizing: border-box;
    padding: 60rpx 40rpx 0 40rpx;
    position: relative;
    z-index: 1;
    // flex: 1;

    .title {
      height: 50rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #3C3C43;
      line-height: 50rpx;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }

    .row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 60rpx;
      margin-top: 10rpx;

      .col-left {
        height: 33rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        line-height: 33rpx;
        text-align: left;
        font-style: normal;
      }

      .col-right {
        display: flex;
        align-items: center;
        height: 33rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 33rpx;
        text-align: left;
        font-style: normal;

        .go-btn {
          &:first-child {
            margin-right: 40rpx;
          }
        }
      }
    }


  }
}

.rights-content {
  flex: 1;
  height: 0;
  // width: 100%;
  // margin-top: 100rpx;
  padding: 100rpx 40rpx 40rpx 40rpx;
  background-color: #fff;
  overflow-y: scroll;
  .rights-list {
    display: grid;
    grid-template-columns: repeat(3, 180rpx);
    grid-row-gap: 80rpx;
    justify-content: space-between;

    .rights-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .icon-box {
        width: 90rpx;
        height: 90rpx;
        // background: #F4EEDE;
        border-radius: 50%;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .item-name {
        // width: 168rpx;
        // height: 33rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 33rpx;
        text-align: center;
        font-style: normal;
        margin-top: 20rpx;
      }
    }

    .unlock {
      // filter: grayscale(100%);

      .item-name {
        color: #BBB;
      }
    }
  }
}