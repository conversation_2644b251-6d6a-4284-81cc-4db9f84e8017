 .map-content {
   width: 100%;
   height: 100%;
   position: relative;
   box-sizing: border-box;

   .list-content {
     display: flex;
     gap: 30rpx;
   }

   .list-item {
     margin-left: 30rpx;
   }

   .swiper {
     width: 100%;
     height: 318rpx;
   }

   .bottom-position {
     position: absolute;
     bottom: 80rpx;
   }

   .customer-pop {
     width: auto;
     padding: 0 10rpx;
     display: inline-block;
     height: 39rpx;
     background: #FFFFFF;
     box-shadow: 0rpx 0rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
     text-align: center;
     font-family: PingFangSC,
       PingFang SC,
       MUJIFont2020,
       SourceHanSansCN;
     font-weight: 400;
     font-size: 22rpx;
     color: #3C3C43;
     line-height: 39rpx;
     text-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
     position: relative;
     letter-spacing: 2rpx;
     //  font-style: normal;
   }
 }
