<!--signUp/pages/activateCheckIn/activateCheckIn.wxml-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container">
    <custom-header isShare="{{false}}" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <view class="activateCheckIn">
      <view style="margin-top: {{ menuButtonBottom }}px" style="position: relative">
        <view class="activeRules" bindtap="activeRules">
          <!--<view class="top-right">
            <view class="text">使用建议</view>
            <view class="picture" style="background-image: url({{$cdn}}/signUp/openIcon1.png);"></view>
          </view>-->

          <view class="title">欢迎开启，7天极简护肤</view>
          <view class="rules">
            <view class="text">1</view>
            <view class="text1">坚持每日使用敏感肌用基础补水系列产品(配合纸膜湿敷使用更佳)。</view>
          </view>
          <view class="rules">
            <view class="text">2</view>
            <view class="text1">连续记录7天肌肤状态，获肌肤体验报告。</view>
          </view>
          <view class="rules" style="padding-bottom: 37rpx;">
            <view class="text">3</view>
            <view class="text1">打卡成功获商品折扣券+惊喜好礼抽奖机会。</view>
          </view>
        </view>
        <view class="bottom">
          <!--<view class="bottom-right">
            <view class="text">打卡准备</view>
            <view class="picture" style="background-image: url({{$cdn}}/signUp/openIcon2.png);"></view>
          </view>-->
          <view class="upload_file">
            <block wx:for="{{questionList}}">
              <text class="title">{{item.title}}</text>
              <!-- 图片上传 -->
              <block wx:if="{{item.questionType==5}}">
                <view class="btn" bind:tap="clickUploader">
                  <van-uploader class="uploader" file-list="{{materialUrl}}" max-count="{{item.maxPhotoNum}}"
                    preview-full-image="{{false}}" preview-size="213rpx" image-fit="aspectFill"
                    use-before-read="{{true}}" bind:after-read="afterRead" bind:before-read="beforeRead"
                    bind:delete="del" disabled="{{!isClick}}" deletable="{{!isClick}}">
                    <view class="uploader-content">
                      <view class="plus"></view>
                      <view class="text">添加照片</view>
                    </view>
                  </van-uploader>
                </view>
              </block>
              <!-- 选择题 -->
              <block wx:if="{{item.questionType==2}}">
                <scroll-view enhanced="{{true}}" show-scrollbar="{{false}}" class="type" scroll-x>
                  <view class="type-wrap {{items.isClick?'active':''}}" wx:for="{{typeList}}" wx:for-index="indexs"
                    wx:for-item="items" bind:tap="clickTap" data-query="{{indexs}}">
                    <view class="type-item">{{items.label}}</view>
                  </view>
                </scroll-view>
                <view class="tips" wx:if="{{item.questionTips}}">{{item.questionTips}}</view>
              </block>
            </block>
          </view>
        </view>
      </view>
    </view>
    <!-- <view bindtap="onTapPrivacyCheck" class="check">
      <view wx:if="{{!info.agree}}" class=" iconfont icon-danxuan-weigouxuan radio"></view>
      <view wx:else class="iconfont icon-danxuan-yigouxuan radio"></view>
      <view class="text">我已阅读并同意<text catchtap="go"><text class="link">《信息使用说明》</text></text>
      </view>
    </view> -->
    <view class="bottom-box">
      <basic-button disabled="{{!isComplate}}" width="{{670}}" size="large" bind:click="submit">
        开始打卡
      </basic-button>
    </view>
  </view>
  <Popup isShow="{{showPopup}}" info="{{infoLottery}}" bindclose="closePopup"></Popup>
</my-page>