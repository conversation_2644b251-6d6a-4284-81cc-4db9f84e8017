const app = getApp()
Component({
  externalClasses: ['floatStyle'],
  properties: {
    // 组件数据
    data: {
      type: null,
      value() {
        return {}
      }
    },
    width: {
      type: Number,
      value: 750,
    }

  },
  data: {
    x: 1000,
    y: 1000,
    opacity: 0
  },
  ready() {
    let {
      horizontalType,
      left,
      right,
      bottom
    } = this.data.data;
    let time = setTimeout(() => {
      clearTimeout(time)
      time = null
      Promise.all([this.getHeight('box'), this.getHeight('box-view')]).then(res => {
        let y = (res[0] - res[1]) / app.globalData.rpx - bottom
        let x;
        if (horizontalType == 1) {
          x = this.data.width - right - this.data.data.width
        } else {
          x = left
        }
        this.setData({
          y,
          x
        }, () => {
          let time = setTimeout(() => {
            clearTimeout(time)
            time = null
            this.setData({
              opacity: 1
            })
          }, 500);
        })
      })
    }, 500);
  },
  methods: {
    getHeight(id) {
      return new Promise(resolve => {
        var query = wx.createSelectorQuery().in(this);
        query.select('#' + id).boundingClientRect(rect => {
          // 元素的高度可以通过rect.height获取
          if (rect?.height) {
            resolve(rect.height)
          }
        }).exec();
      })
    },
    // 返回顶部
    goTop() {
      this.triggerEvent('goTop')
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      console.log(e)
      this.triggerEvent('goAchor', e.detail)
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent('goModal', e.detail)
    },
    // 分享
    goShare(e) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = e.detail
      this.triggerEvent('goShare', {
        shareTitle,
        shareImg,
        sharePath
      })
    },
  }
})
