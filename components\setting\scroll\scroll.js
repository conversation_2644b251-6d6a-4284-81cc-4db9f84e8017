const app = getApp();
Component({
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {};
      },
      observer() {
        this.calcHeight();
      },
    },
    // 游客模式
    visitor: {
      type: <PERSON><PERSON>an,
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750,
    },
  },
  data: {
    list: [],
    rpx: app.globalData.rpx,
  },
  methods: {
    calcHeight() {
      let width = this.data.width;
      let { list, paddingLeft, paddingRight } = this.data.data;
      let newList = list.map((item) => {
        let { imgWidth, imgHeight } = item;
        let posterWidth = width - paddingLeft - paddingRight;
        let posterHeight = parseInt((posterWidth * imgHeight) / imgWidth);
        return {
          ...item,
          posterWidth,
          posterHeight,
        };
      });
      this.setData({
        list: newList,
      });
    },
    // 返回顶部
    goTop() {
      this.triggerEvent("goTop");
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent("goAchor", e.detail);
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent("goModal", e.detail);
    },
    // 分享
    goShare(e) {
      let { shareTitle, shareImg, sharePath } = e.detail;
      this.triggerEvent("goShare", {
        shareTitle,
        shareImg,
        sharePath,
      });
    },
  },
});
