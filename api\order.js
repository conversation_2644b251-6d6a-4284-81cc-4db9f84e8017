// 订单创建
exports.postOrderCreate = (data) =>
  wx.$request({
    url: "/app/order/order/create",
    data,
    method: "post",
    timeout: 40 * 1000,
  });

// 订单删除
exports.postOrderDelete = (data) =>
  wx.$request({
    url: "/app/order/order/delete",
    data,
    method: "post",
  });

// 订单预览
exports.postOrderPreview = (data) =>
  wx.$request({
    url: "/app/order/order/preview",
    data,
    method: "post",
  });

// 订单列表
exports.postOrderList = (data) =>
  wx.$request({
    url: "/app/order/order/list",
    data,
    method: "post",
  });

// 获取订单详情
exports.getOrderDetail = (data) =>
  wx.$request({
    url: "/app/order/order/info",
    data,
    method: "get",
  });

// 获取线上订单详情
exports.getOnLineOrderDetail = (data) =>
  wx.$request({
    url: "/app/user/muji_order/info",
    data,
    method: "get",
  });
