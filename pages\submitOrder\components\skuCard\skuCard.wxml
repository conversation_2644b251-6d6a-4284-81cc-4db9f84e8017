<view class="card-wrapper">
  <view class="cart-card">
    <view class="cart-img">
      <!-- 角标 -->
      <view wx:if="{{tagData}}" style="position: absolute; z-index: 2; {{tagData.position === 'top' ? 'top: 0; left: 0;' : 'bottom: 5rpx; left: 8rpx;'}}">
        <image src="{{tagData.imgUrl}}" style="width: {{tagData.width * 0.79}}rpx; height: {{tagData.height * 0.79}}rpx;" />
      </view>

      <image class="img" mode="aspectFill" src="{{skuInfo.imgUrl}}" />
    </view>
    <view class="cart-info">
      <view class="cart-title">
        {{skuInfo.productName}}
      </view>
      <view class="cart-price">
        {{skuInfo.costPoint}}积分<block wx:if="{{skuInfo.costPrice}}">+{{skuInfo.costPrice}}元</block>
      </view>
      <view class="cart-btn">

        <view class="sku-num">
          数量：{{skuInfo.number}}
        </view>

      </view>
    </view>
  </view>
</view>