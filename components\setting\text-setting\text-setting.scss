/* components/setting/text-setting/text-setting.wxss */
.textSetting {
  box-sizing: border-box;
  position: relative;

  &-content {
    text-underline-offset: 8rpx;

    &.lineType1 {
      height: auto;
    }

    // 超出省略  不超出不省略
    &.lineType2 {
      height: calc(var(--lines) * var(--height));
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: var(--lines);
    }

    // 超出省略  不超出不省略
    &.lineType3 {
      max-height: calc(var(--lines) * var(--height));
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: var(--lines);
    }

    // overflow: hidden;
    // text-overflow: ellipsis;
    // word-break: break-all;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: var(--lines);
  }

  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  &-text {
    position: relative;
    z-index: 100;
  }
}
