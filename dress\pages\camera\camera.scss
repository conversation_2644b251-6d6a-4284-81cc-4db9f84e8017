/* dress/pages/camera/camera.wxss */
.camera-page {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff9ec;
  &.black {
    --color: #3c3c43;
  }
  &.white {
    --color: #fff;
  }

  .camera-area {
    position: relative;
    margin-top: 68rpx;
    width: 100%;
    height: 1000rpx;

    background: #a5a5a5;
    .camera {
      width: 100%;
      height: 100%;
    }
    .mark-img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
    .retake-btn {
      position: absolute;
      top: 25rpx;
      right: 25rpx;
      width: 145rpx;
      height: 50rpx;
      background: #3c3c43;
      text-align: center;
      line-height: 50rpx;
      font-size: 20rpx;
      color: #fff;
    }
  }

  .camera-operate {
    position: absolute;
    bottom: 48rpx;
    left: 85rpx;
    right: 85rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .camera-operate-item {
      width: 82rpx;
      height: 82rpx;
      &.take {
        width: 112rpx;
        height: 112rpx;
      }
      &.taked {
        width: 138rpx;
        height: 138rpx;
      }
    }
  }

  .camera-operate-text {
    position: absolute;
    bottom: 89rpx;
    right: 82rpx;
    font-size: 26.4rpx;
    color: var(--color);
    letter-spacing: 1px;
  }

  .watermark-area {
    box-sizing: border-box;
    flex: 1;
    min-height: 0;
    padding: 0 60rpx;
    display: flex;
    align-items: center;

    .watermark-item {
      box-sizing: border-box;
      width: 134rpx;
      height: 196rpx;
      margin-right: 36rpx;
      &.active {
        padding: 3rpx;
        border: 4rpx solid #3c3c43;
      }
      .watermark-item-s {
        width: 100%;
        height: 100%;
      }
    }
  }

  .submit-area {
    margin: 136rpx 54rpx 40rpx;
    .submit-area-img {
      display: block;
      margin-bottom: 116rpx;
      width: 100%;
      height: 854rpx;
    }
    .primary-btn {
      margin: 0 auto 26rpx;
      width: 396rpx;
      height: 75rpx;
      background: #3c3c43;

      text-align: center;
      line-height: 75rpx;
      font-weight: bold;
      font-size: 26rpx;
      color: #fff;
    }
  }

  .input-area {
    position: absolute;
    bottom: 68rpx;
    left: 42rpx;
    box-sizing: border-box;
    width: 225rpx;
    padding: 12rpx 0 8rpx 26rpx;
    border: 1rpx solid;
    color: var(--color);
    font-size: 25rpx;
    line-height: 29rpx;
    .input {
      width: 100%;
      margin-top: 2rpx;
      font-weight: bold;
    }
    .input-placeholder {
      color: var(--color);
      opacity: 0.5;
      font-weight: 300;
    }
  }
}

.draw-canvas {
  position: absolute;
  top: 0;
  left: -1000px;
  width: 750px;
  height: 1000px;
}
