.card-cell-content {
  margin-top: 30rpx;
  padding: 60rpx 30rpx 0rpx 30rpx;
  background: #FFFFFF;

  .cell-item {
    padding-bottom: 60rpx;
    display: flex;
    justify-content: space-between;
  }

  .cell-title {
    flex: 1;
    font-family: 'MUJIFont2020', SourceHanSansCN;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
  }

  .cell-value {
    flex: 1;
    font-family: 'MUJIFont2020', SourceHanSansCN;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 36rpx;
    text-align: right;
    font-style: normal;
  }
}
