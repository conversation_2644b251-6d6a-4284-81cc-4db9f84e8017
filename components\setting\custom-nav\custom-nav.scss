.custom-nav {
  position: fixed;
  left: 0;
  top: 0;
  width: 750rpx;
  z-index: 1000;

  &-content {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;

    &.show {
      opacity: 1;
      pointer-events: auto;
    }

    &.hidden {
      opacity: 0;
      pointer-events: none;
    }
  }

  &-back {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    width: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 44rpx;
    color: var(--color);
    font-weight: 900;
  }

  .icon-back {
    width: 60rpx;
    height: 60rpx;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-title {
    font-family: 思源黑体;
    font-weight: 400;
    font-size: 36rpx;
    color: #000000;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    &.left {
      padding-left: 100rpx;
      width: 450rpx;
    }

    &.center {
      width: 750rpx;
      padding: 0 200rpx;
      box-sizing: border-box;
    }
  }

  &-titleUrl {
    height: 44px;
  }

  &-titleBox {
    display: inline-flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  &-link {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
  }
}

.bgStyle {
  width: 100%;
  height: 100%
}

.linkStyle {
  width: 100%;
  height: 100%
}
