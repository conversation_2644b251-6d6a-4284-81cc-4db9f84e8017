const app = getApp()
Page({
  data: {
    href: "",
  },
  onLoad() {
    let symbol = wx.$config.reportH5.includes('?') ? '&' : '?'
    let src = `${wx.$config.reportH5}${symbol}miniToken=${wx.getStorageSync('token')}&source=mini&chid=${app.globalData.channelOne || ''}&cpid=${app.globalData.channelTwo || ''}`
    if (wx.$config.env !== 'prod') src += '&showDevelopmentTool=true'
    let href = decodeURIComponent(src);
    console.log("src:", src);
    console.log("href:", href);
    this.setData({
      href,
    });
  },
  onShareAppMessage() {
    return {
      title: '探索20年和MUJI的旅途，赢经典商品',
      path: '/report/pages/index/index',
      imageUrl: this.data.$cdn + '/member/share.jpg',
    };
  },
});
