/* pages/rightsRecord/rightsRecord.wxss */
.rights-record{
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  // box-sizing: border-box;
}

.content-box {
  flex: 1;
  height: 0;
  margin-top: var(--page-margin);
  width: 100%;
  overflow: auto;
  .content-box-list{
    margin-left: var(--page-margin);
    margin-right: var(--page-margin);
    padding-bottom: 60rpx;
  }


}
page{
  background: #F5F5F5;
}