export const apiNewBooking = {
  async getBookingInfo (data) {
    return wx.$request({
      url: '/app/user/appointments/details',
      data,
      method: 'get'
    }).then(res => {
      res.data.userAppointment = res.data.userAppointment || {}
      // 场景模拟 ↓
      // res.data.expired = false
      // res.data.inWhitelist = true
      // res.data.alreadyBooked = false
      // 场景模拟 ↑
      return res
    })
  },
  async setBookingInfo (data) {
    return wx.$request({
      url: '/app/user/appointments/book',
      data,
      method: 'post'
    }).then(res => {
      return res
    })
  },
  async getBookingDates (data) {
    return wx.$request({
      url: '/app/user/appointments/dates_stats',
      data,
      method: 'get'
    }).then(res => {
      res.data.forEach((v, i) => {
        if (v.full) {
          v.disabled = true
          v.name = `${v.name}（已满）`
        }
      })
      return res
    })
  },
  async getBookingSlots (data) {
    return wx.$request({
      url: '/app/user/appointments/slots_stats',
      data,
      method: 'get'
    }).then(res => {
      res.data.forEach((v, i) => {
        if (v.full) {
          v.disabled = true
          v.name = `${v.name}（已满）`
        }
      })
      return res
    })
  }
}
