import BarCode from './../../utils/barCode'; // 引入上面创建的barCode.js
Component({
  properties: {
    width: {
      // 条形码宽度
      type: Number,
      default: 440,
    },
    height: {
      // 条形码高度
      type: Number,
      default: 180,
    },
    content: {
      // 需要转换成条形码的字符串
      type: String,
      default: '',
    },
  },
  data: {},
  attached() {
    console.log("组件已加载"); // 等同于页面的 onLoad
    this.createBarcode();
  },
  methods: {
    createBarcode() {
      if (!this.data.content) return;
      const query = wx.createSelectorQuery().in(this);
      query
        .select('#barcode')
        .fields({
          node: true,
          size: true,
        })
        .exec((res) => {
          const canvas = res[0]?.node;
          if (!canvas) return;
          /* 获取 canvas 实例 */
          const context = canvas.getContext('2d');
          const dpr = wx.getSystemInfoSync().pixelRatio;
          canvas.width = res[0].width * dpr;
          canvas.height = res[0].height * dpr;
          context.scale(dpr, dpr);
          // 生成条形码 宽度134，高度512，可通过props传入自定义，这里我就写死了
          BarCode.barcode(context, this.data.content, this.data.height, this.data.width);
        });
    },
  }
})
