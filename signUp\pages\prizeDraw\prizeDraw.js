// signUp/pages/prizeDraw/prizeDraw.js
const app = getApp()
import {
  getLotteryList,
  getLottery,
  getLotteryUser,
  getUserPrizes,
  getCampaignType
} from '../../api/index.js'
let timer = null,
  timer2 = null
Page({

  /**
   * 页面的初始数据
   */
  data: {
    giftList: [
      // {
      //   imageUrl: 'prizeAward1.png',
      //   id: 1
      // },
      // {
      //   imageUrl: 'prizeAward2.png',
      //   id: 2
      // },
      // {
      //   imageUrl: 'prizeAward3.png',
      //   id: 3
      // },
      // {
      //   imageUrl: 'prizeAward4.png',
      //   id: 4
      // },
      // {
      //   imageUrl: 'prizeAward5.png',
      //   id: 5
      // },
      // {
      //   imageUrl: 'prizeAward6.png',
      //   id: 6
      // },
      // {
      //   imageUrl: 'prizeAward7.png',
      //   id: 7
      // },
      // {
      //   imageUrl: 'prizeAward8.png',
      //   id: 8
      // },
      // {
      //   imageUrl: 'prizeAward9.png',
      //   id: 9
      // },
      // {
      //   imageUrl: 'prizeAward10.png'
      // },
    ],
    campaignCode: wx.getStorageSync('campaignCode') || 'sign_in',
    showPopup: false,
    infoLottery: {},
    checkIn: 0, // 选中的
    surplusCount: "", // 剩余抽奖机会
    totalCount: '', // 总抽奖机会
    list1: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getDatas()
  },
  getDatas() {
    getCampaignType().then(res => {
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      this.setData({
        CampaignData: res.data
      })
    })
  },
  // 获取抽奖列表
  async getLisdt() {
    let {
      data
    } = await getUserPrizes({
      campaignCode: this.data.campaignCode,
      pageNum: 1,
      pageSize: 10
    })
    console.log(data, 'data 抽奖记录');
    this.setData({
      list1: data.list
    })
  },
  hideShare() {
    // 禁止直接转发 需要点击补卡 进行转发
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },
  showShare() {
    wx.showShareMenu({
      menusmenus: ['shareAppMessage']
    })
  },
  getData() {
    getLotteryUser({
      campaignCode: this.data.campaignCode
    }).then(({
      data
    }) => {
      console.log('页面判断是否有剩余抽奖次数', data);
      this.setData({
        surplusCount: data.surplusCount,
        totalCount: data.totalCount,
        isShare: data.isShare
      })
      console.log(wx.getStorageSync('isPrizeDialog'), "wx.getStorageSync('isPrizeDialog') 打印");
      // 没有分享 弹弹框
      // if (!wx.getStorageSync('isPrizeDialog')) {
      //   wx.setStorageSync('isPrizeDialog', 1)
      //   this.openPopup()
      // }
    })
  },
  getList() {
    getLotteryList({
      campaignCode: this.data.campaignCode
    }).then(({
      data
    }) => {

      // 默认第一个和最后一个
      data = [{
          imageUrl: 'prizeAward1.png',
          id: 0
        },
        ...data,
        {
          imageUrl: 'prizeAward9.png',
          id: 8
        },

      ]
      console.log(data, '抽奖列表');
      this.setData({
        giftList: data
      })
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getData()
    this.getList()
    this.getLisdt()
  },
  openPopup() {
    this.setData({
      showPopup: true
    })
  },
  closePopup: app.debounce(async function () {
    this.setData({
      showPopup: false
    })
  }),
  openActive() {
    // let randomNum = Math.ceil(Math.random() * 7)
    // this.setData({
    //   checkIn: randomNum
    // })
  },
  printNumbers(n) {
    if (n > 8) this.setData({
      checkIn: 2
    }); // 终止条件，防止无限递归
    else {
      this.setData({
        checkIn: n + 1
      })
    }
    // 递归调用，n自增
  },
  closeActive() {

  },
  // 抽取惊喜礼品
  submit: app.debounce(async function () {
    wx.$mp.track({
      event: 'lucky_lottery_click',
    })
    let that = this
    let {
      totalCount,
      surplusCount
    } = that.data
    timer = setInterval(() => {
      that.printNumbers(this.data.checkIn)
    }, 350)
    let checkIn = ''
    timer2 = setTimeout(() => {
      getLottery({
        campaignCode: this.data.campaignCode
      }).then(({
        data
      }) => {
        console.log(data, '抽奖');
        checkIn = data.id
        wx.redirectTo({
          url: `/signUp/pages/Winning/Winning?id=${data.id}`,
        })
      }).then(() => {
        console.log(totalCount, 'totalCount 总共抽奖次数');
        console.log(surplusCount, 'surplusCount 剩余抽奖次数');
        this.getData()
        this.setData({
          checkIn: checkIn
        })

        clearInterval(timer)
      }).catch((err) => {
        clearInterval(timer)
      })
      clearTimeout(timer2)
    }, 3000)
  }),
  activeRules() {
    wx.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
