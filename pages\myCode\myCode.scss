/* pages/myCode/myCode.wxss */
.page-container {
  height: 100vh;
  // background-color: green;
  display: flex;
  background: rgba(224, 206, 170, 0.15);

  .page-content {
    box-sizing: border-box;
    margin: 0 var(--page-margin);
    flex: 1;



    .member-box {
      margin-top: 60rpx;
      position: relative;
      display: flex;
      align-items: center;
      flex-direction: column;
      z-index: 1;


      .member-bg {
        position: absolute;
        width: 670rpx;
        height: 230rpx;
        background: rgba(187, 187, 187, 0.2);
        border-radius: 30rpx;
        top: 160rpx;
        z-index: 1;
      }

      .member-info {
        padding: 50rpx 40rpx 40rpx 40rpx;
        // height: 260rpx;
        display: flex;
        justify-content: space-between;
        width: 610rpx;
        // height: 260rpx;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 20rpx 20rpx 0rpx 0rpx;
        border: 6rpx solid #FFFFFF;
        position: relative;
        z-index: 2;

        .avatar {
          flex-basis: 140rpx;
          width: 140rpx;
          height: 140rpx;
          border: 1rpx solid #EEEEEE;
          display: flex;
          align-items: center;

          image {
            width: 120rpx;
            height: 120rpx;
            margin: 0 auto;

          }
        }

        .member-txt {
          flex: 1;
          overflow: hidden;
        }

        .member-right {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }

        .member-time {
          margin-top: 25rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 16rpx;
          color: #888888;
        }

        .info-item {
          &:last-child {
            margin-top: 44rpx;
          }

          .title {
            height: 28rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 20rpx;
            color: #3C3C43;
            line-height: 28rpx;
            text-align: left;
            font-style: normal;
          }

          .name {
            // height: 40rpx;
            width: 340rpx;
            font-family: MUJIFont2020, SourceHanSansCN;
            font-weight: 600;
            font-size: 28rpx;
            color: #3C3C43;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow-x: hidden;
            // margin-top: 10rpx;
          }
        }
      }

      .code-box {
        position: relative;
        padding: 60rpx;
        padding-top: 80rpx;
        padding-bottom: 100rpx;
        box-sizing: border-box;
        box-shadow: 0rpx 4rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
        width: 670rpx;
        background: #FFFFFF;
        border-radius: 30rpx;
        z-index: 3;

        .qrCode {
          text-align: center;
          padding-bottom: 60rpx;

          .title {
            height: 33rpx;
            font-family: PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #3C3C43;
            line-height: 33rpx;
            text-align: center;
            font-style: normal;
          }

          .code-img {
            width: 340rpx;
            height: 340rpx;
            // background-color: pink;
            margin: 40rpx auto 42rpx;
            background-color: #fff;
            display: flex;
            align-items: center;

            .canvas-box {
              position: fixed;
              right: 9000px;
              z-index: -1 !important;
            }



            .code {
              margin: 0 auto;
            }

          }

          .tips-barcode {
            font-family: PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #bbb;
            line-height: 40rpx;
            text-align: center;
            font-style: normal;
          }
        }

        .custom-dashed-border {
          background: repeating-linear-gradient(to right, #ddd, #ddd 12rpx, transparent 1rpx, transparent 20rpx);
          height: 1rpx;
          /* 设置虚线的粗细 */
        }

        .code-txt {
          position: relative;
          font-family: MUJIFont2020, MUJIFont2020;
          font-weight: 400;
          font-size: 24rpx;
          color: #3C3C43;
          text-align: center;
          font-style: normal;
          padding: 40rpx 0;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10rpx;
        }

        .barCode {
          height: 310rpx;
        }
      }

      .code {

        &-tip {
          margin-top: 30rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &-icon {
          width: 24rpx;
          height: 24rpx;
          margin-right: 5rpx;
        }

        &-info {
          font-family: SourceHanSansCN, SourceHanSansCN;
          font-weight: 400;
          font-size: 24rpx;
          color: #BBBBBB;
        }
      }


    }
  }

  .bottom-box {
    padding: var(--page-margin);
    padding-bottom: max(env(safe-area-inset-bottom), 40rpx);
    /* 避免被系统底部遮挡 */
    display: flex;
    justify-content: center;
  }
}
