// components/header-search-input/header-search-input1.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {

  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    inputInfo(e) {
      let {
        value
      } = e.detail;
      console.log(value);
      this.setData({
        keyword: value
      })
      this.triggerEvent('change', this.data.keyword)
    },
    clearInput() {
      this.setData({
        keyword: ''
      })
      this.triggerEvent('change', this.data.keyword)
    },
  }
})