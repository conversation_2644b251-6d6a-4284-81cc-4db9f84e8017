<my-modal show="{{show}}" zIndex="{{zIndex}}" background="{{background}}" closeable="{{closeable}}" borderRadius="{{borderRadius}}" bindclose="close" overlayClick="{{overlayClick}}">
  <view class="ruleModal" id="ruleModal">
    <!-- 标题 -->
    <view class="ruleModal-title" catch:touchmove="touchmove1">{{title}}</view>
    <!-- 规则区域-->
    <view class="ruleModal-other">
      <scroll-view class="ruleModal-rules" style="height:{{height}}rpx" scroll-y bindscrolltolower="scrollEnd">
        <view class="ruleModal-rules-box">
          <image class="ruleModal-rules-item" wx:for="{{rules}}" wx:key="{{index}}" src="{{$cdn}}{{item}}" mode="widthFix" />
        </view>
      </scroll-view>
    </view>
    <view class="ruleModal-bottom" catch:touchmove="touchmove1">
      <!-- 按钮区域 -->
      <view class="ruleModal-button {{disabled?'disabled':''}}" bindtap="confirm" wx:if="{{confirmText}}">
        {{confirmText}}
      </view>
      <view style="height:60rpx;width:100%;"></view>
    </view>
  </view>
</my-modal>