<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>{{ '商品详情' }}</text>
      </view>
    </custom-header>
    <!-- <view> -->
    <!-- <my-header-top /> -->
    <!-- </view> -->
    <scroll-view class="shop-details" scroll-y="true" style="--bottom:180rpx" show-scrollbar="{{false}}">
      <view class="header-card">
        <slideshow-details list="{{imgList}}"></slideshow-details>
      </view>
      <view class="shop-content">
        <view class="product-tag-wrap">
          <view wx:if="{{currentTagData}}" class="product-tag" style="background-color: {{currentTagData.bgColor}}; color: {{currentTagData.fontColor}};">
            <view class="tag-txt">{{currentTagData.txt}}</view>
          </view>
          <view wx:if="{{detail.subTitle}}" class="product-tag is-grey-bg">
            <!--<view class="tag-txt">每人每月限兑{{detail.subTitle}}张</view>-->
            <view class="tag-txt">{{detail.subTitle}}</view>
          </view>
        </view>
        <view class="shop-header">
          <view class="shop-title ellipsis">
            {{ name }}
          </view>
        </view>
        <view class="points-box flex-end">
          <view class="points-num ">{{costPoint}}</view><text class="points-title points-red">积分</text>
          <view wx:if="{{prePoint}}" class="line-through">{{prePoint}}积分</view>
        </view>
        <view wx:if="{{costPrice || prePrice }}" class="tips-box">
          <block wx:if="{{costPrice}}">
            <view class="price-item">
              <view class="tips-item">
                需到店支付
              </view>
              <view class="tips-item red-font">
                ¥{{costPrice}}
              </view>
            </view>
          </block>
          <block wx:if="{{prePrice}}">
            <view class="price-item">
              <view class="tips-item" style="margin-right: 0;">
                吊牌价：
              </view>
              <view class="tips-item">
                ¥{{prePrice}}
              </view>
            </view>
          </block>
        </view>

        <!-- <view class="divided-line" /> -->


        <view class="cell-box">
          <productDetails list="{{cellOptions}}"></productDetails>
        </view>

      </view>

    </scroll-view>

    <view class="footer">
      <footer-cart detail="{{detail}}" isMember="{{userInfo.isMember}}" cartCount="{{cartCount}}" bind:tap-cart="onTapCart" bind:tap-cart-add="onTapCartAdd" bind:tap-cart-exchange="onTapCartExchange"></footer-cart>
      <!--bind:tap="onTapCartExchange"-->
    </view>

    <my-popup show="{{ err.show }}" closeable="{{false}}" borderRadius="{{0}}" title="温馨提示" confirmText="我知道了" cancelText="前往购物车" content="您的积分不足以兑换购物车的全部商品" bindclose="" bindconfirm="onPopConfirm" bindcancel="onPopCancel" data-key="fail" showType="normal"></my-popup>

  </view>
</my-page>