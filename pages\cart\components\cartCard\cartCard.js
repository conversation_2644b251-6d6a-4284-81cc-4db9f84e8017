const app = getApp()
import {
  productTagArray
} from '../../../../utils/contants.js'

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    type: {
      type: String,
      value: ''
    },
    skuInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleVal(val)
      }
    },
  },
  onLoad(options) {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {
    tagData: undefined
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onTapDetail() {
      this.triggerEvent('tap-detail', this.properties.skuInfo)
    },

    onConfirm(e) {
      this.triggerEvent('confirm', e.detail)
    },

    onTapCartDel() {
      this.triggerEvent('tap-cart-del', this.properties.skuInfo)
    },

    onTapCartAdd() {
      this.triggerEvent('tap-cart-add', this.properties.skuInfo)
    },
    handleVal(val) {
      const {
        superscriptNameList
      } = val;
      if (superscriptNameList && superscriptNameList.length > 0) {
        const v = productTagArray.find(item => item.value === superscriptNameList[0]);
        console.log('v', v);
        this.setData({
          tagData: v
        })
      }
    }
  }
})
