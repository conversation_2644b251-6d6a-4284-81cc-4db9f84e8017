<view class="product-card" bindtap="handleGoDetail" data-id="{{skuInfo.shelfProductId }}" data-name="{{skuInfo.productName}}">
  <view class="product-img">
    <view wx:if="{{!skuInfo.currentInventory}}" class="empty">
      <view class="text">已抢完</view>
    </view>
    <image class="image" src="{{skuInfo.shelfImg}}" />
    <view wx:if="{{tagData}}" style="position: absolute; z-index: 2; {{tagData.position === 'top' ? 'top: 0; left: 0;' : 'bottom: 5rpx; left: 10rpx;'}}">
      <image src="{{tagData.imgUrl}}" style="width: {{tagData.width}}rpx; height: {{tagData.height}}rpx;" />
    </view>
    <view class="active-tag" wx:elif="{{skuInfo.superscript[0] === '活动限定'}}">
      <image src="{{$cdn}}/product/active_tag.png" />
    </view>
    <view class="limit-tag" wx:elif="{{skuInfo.superscript[0] === '限时折扣 积分兑礼'}}">
      {{ skuInfo.superscript[0]}}
    </view>
  </view>
  <view class="product-info">
    <view class="product-title ellipsis">
      {{skuInfo.productName}}
    </view>
    <view class="product-price">
      <view class="price-item {{(skuInfo.showType===2&&skuInfo.prePoint) ? 'red-font' : '' }}">
        <view class="num">{{skuInfo.costPoint}}</view>
        <view class="unit">积分</view>
      </view>
      <!-- 展示积分加购价 根据是否展示货架做到店金额是否展示 -->
      <block wx:if="{{skuInfo.showType===1}}">
        <block wx:if="{{skuInfo.costPrice && skuInfo.costPriceOnShelf!==0}}">
          <view class="plus-icon">+</view>
          <view class="price-item">
            <view class="num">{{skuInfo.costPrice}}</view>
            <view class="unit">元</view>
          </view>
        </block>
      </block>
      <!-- 展示商品兑换价 如果有划线价展示划线价 如果没有则只展示积分价格-->
      <block wx:if="{{skuInfo.showType===2 && skuInfo.prePoint!==null}}">
        <view class="price-item small-font line">
          <view class="num">{{skuInfo.prePoint}}积分</view>
          <!-- <view class="unit" style="top: 8rpx;"></view> -->
        </view>
      </block>

      <!-- <block wx:if="{{skuInfo.costPrice}}">
        <view class="plus-icon">+</view>
        <view class="price-item">
          <view class="num">{{skuInfo.costPrice}}</view>
          <view class="unit">元</view>
        </view>
      </block> -->


    </view>
  </view>
</view>