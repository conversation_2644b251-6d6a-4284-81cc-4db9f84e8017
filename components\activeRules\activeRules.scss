.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-content {
    overflow: hidden;
    width: 630rpx;

    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 46rpx;
    padding-bottom: 63rpx;
    padding-right: 55rpx;
    padding-left: 31rpx;
    box-sizing: border-box;

    .clock {
      max-height: 1070rpx;

      .h1 {
        font-family: SourceHanSansCN;
        font-weight: 500;
        font-size: 36rpx;
        color: var(--text-black-color);
        line-height: 54rpx;
        letter-spacing: 1px;
        text-align: center;
        margin-top: 41rpx;
      }

      .h2 {
        font-weight: 500;
        font-size: 26rpx;
        color: var(--text-black-color);
        line-height: 45rpx;
        text-align: justify;
      }

      .h3 {
        font-weight: 300;
        font-size: 24rpx;
        color: var(--text-black-color);
        line-height: 45rpx;
        text-align: left;
      }

      .h4 {
        font-weight: 300;
        font-size: 20rpx;
        color: var(--text-black-color);
        line-height: 35rpx;
        text-align: justify;
        text-indent: -2em;

        padding-left: 64rpx;
      }

      .picture {
        display: flex;
        flex-wrap: wrap;
        padding-left: 33rpx;
        padding-top: 19rpx;


        .picture-item {
          margin-bottom: 19rpx;
          margin-left: 5rpx;
          margin-right: 5rpx;

          .img {
            width: 158rpx;
            height: 156rpx;
            background-color: blanchedalmond;
          }

          .name,
          .num {
            width: 161rpx;
            font-weight: 400;
            font-size: 18rpx;
            color: var(--text-black-color);
            line-height: 27rpx;
            text-align: center;
          }
        }
      }
    }
  }

  &-close {
    padding-top: 10rpx;
    height: 80rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
