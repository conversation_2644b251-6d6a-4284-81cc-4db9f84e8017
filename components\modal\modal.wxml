<van-popup style="--popup-background-color: transparent; --overlay-background-color:{{overlayBackground}}" lock-scroll
  closeable="{{false}}" zIndex="{{zIndex}}" show="{{ show }}" safe-area-inset-bottom="{{true}}"
  close-on-click-overlay="{{overlayClick}}" bindclose="close" custom-style="background-color: transparent">
  <view class="selfModal" style="padding:{{closeable?90:0}}rpx 0">
    <view class="selfModal-content" style="background:{{background}};--radius:{{borderRadius}}rpx;">
      <slot></slot>
    </view>
    <view class="selfModal-close" wx:if="{{closeable}}" catch:touchmove="touchmove1">
      <view class="selfModal-closeBox" bindtap="close">
        <text class="iconfont icon-a-Turnoff" style="color:#fff;font-size:50rpx;" catch:touchmove="touchmove1"></text>
      </view>
    </view>
  </view>
</van-popup>