.page-container {
  // overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
}

.result-wrapper {
  position: absolute;
  left: 0;
  right: 0;
  top: 384rpx;
  z-index: 10;
}

.result-header {
  margin-bottom: 64rpx;
  //padding-top: 208px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #3C3C43;
  line-height: 60rpx;
  text-align: center;
  font-style: normal;

  .result-icon {
    margin-bottom: 42rpx;
    width: 72rpx;
    height: 72rpx;
    font-size: 72rpx;
    color: #7F0019;
  }
}

.result-content {
  margin-bottom: 16rpx;
  font-family: MUJIFont2020, MUJIFont2020;
  font-weight: bold;
  font-size: 44rpx;
  color: #3C3C43;
  line-height: 80rpx;
  letter-spacing: 2px;
  text-align: center;
  font-style: normal;
}

.result-desc {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #3C3C43;
  line-height: 40rpx;
  text-align: center;
  font-style: normal;
}

.bottom-box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  padding: var(--page-margin);
  padding-bottom: 60rpx;
  /* 避免被系统底部遮挡 */
  //background-color: #fff;
  display: flex;
  justify-content: space-between;
}
