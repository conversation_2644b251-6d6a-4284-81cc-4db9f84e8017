<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>{{ type? '个人信息' : '会员注册' }}</text>
      </view>
    </custom-header>

    <scroll-view class="register" scroll-y enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}">
      <view style="margin-top: {{ menuButtonBottom }}px" style="position: relative">
        <view class="register-form">
          <view class="avatar-item">
            <button class="avatar-box" open-type="chooseAvatar" bindchooseavatar="chooseAvatar">
              <image class="avatar-img" wx:if="{{info.avatar}}" src="{{info.avatar}}" />
              <image class="avatar-img" wx:else src="{{$cdn}}/default-avatar.png" />
              <!-- 照相机icon -->
              <image class="auth-avatar" src="{{$cdn}}/Photos.png" />
            </button>
            <view class="avatar-desc" wx:if="{{!type}}">
              <text style="padding-right: 20rpx">绑定会员</text><text>尊享权限</text>
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              昵称*
            </view>
            <view class="item-content">
              <input type="nickname" class="item-input" placeholder="请输入昵称" value="{{ info.username }}"
                bindchange="changeName" maxlength="20" />
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              性别
            </view>
            <view class="item-content">
              <view class="radio-group">
                <view bindtap="changeGender" class="radio-item" wx:for="{{genderList}}" wx:key="value"
                  data-gender="{{item.value}}">
                  <view wx:if="{{info.gender !== item.value}}" class="iconfont icon-danxuan-weigouxuan radio" />
                  <view wx:else class="iconfont icon-xuanzhong radio"></view>
                  <view>{{item.label}}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="form-item" wx:if="{{!type}}">
            <view class="item-label">
              手机号*
            </view>
            <button disabled="{{type==='edit'}}" class="auth-phone" open-type="getPhoneNumber"
              bindgetphonenumber="authMobile" style="margin-top: 0rpx">
              <view class="item-text absolute-box">
                <text class="{{info.mobile ?'item-value' : 'without-data'}} {{type?'disabled':''}}">{{info.mobile
                  ||'获取手机号'}}</text>
              </view>
              <view class="item-button" wx:if="{{!type}}">
                <view class="auth-btn">授权手机号</view>
              </view>
            </button>
          </view>
          <view class="form-item" wx:else="{{type==='edit'}}">
            <view class="item-label">
              手机号*
            </view>
            <button class="auth-phone" bind:tap="showEditMobileTips" style="margin-top: 0rpx; flex-direction: row;">
              <view class="item-text relative-box">
                <text class="item-value disabled">{{info.mobile}}</text>
              </view>
              <view class="item-button">
                <view class="auth-btn">修改手机号</view>
              </view>
            </button>
          </view>
          <view class="form-item">
            <view class="item-label">
              生日（填写后不可更改）
            </view>
            <picker value="1980-01-01" end="{{ endDate }}" mode="date" bindchange="birthdayChange"
              disabled="{{!editBirthday}}">
              <view class="item-content flex-center">
                <view class="item-text">
                  <text
                    class="{{info.birthday ?'item-value' : 'without-data'}} {{editBirthday?'':'disabled'}}">{{info.birthday
                    ||
                    '生日月赠送惊喜生日礼哦'}}</text>
                </view>
                <view class="item-button" wx:if="{{editBirthday}}">
                  <text class="iconfont icon-View1"></text>
                </view>
              </view>
            </picker>
          </view>
          <view class="form-item">
            <view class="item-label">
              所在地
            </view>
            <picker mode="region" bindchange="regionChange">
              <view class="item-content flex-center">
                <view class="item-text">
                  <text class="{{info.province ?'item-value' : 'without-data'}}">
                    <text>{{info.province ? info.province + ' ' + info.city + ' ' + info.area :
                      '请选择您的所在地'}}</text>
                  </text>
                </view>
                <view class="item-button">
                  <text class="iconfont icon-View1"></text>
                </view>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="bottom-box">
      <basic-button disabled="{{!isComplate}}" width="{{670}}" loading="{{loading}}" size="large" bind:click="update"
        wx:if="{{type}}">
        提交
      </basic-button>
      <basic-button disabledTip="请完善信息" disabled="{{!isComplate}}" width="{{670}}" loading="{{loading}}" size="maxlarge"
        bind:click="register" wx:else>
        完成注册
      </basic-button>
    </view>
  </view>
  <!-- 加入会员信息异常 -->
  <my-popup show="{{error}}" borderRadius="{{0}}" isCenter="{{false}}" closeable="{{false}}" title="加入会员信息异常"
    confirmText="我知道了" content="{{errContent}}" bindclose="close" bindconfirm="close" bindcancel="close">
  </my-popup>

  <!-- 修改手机号提醒弹窗 -->
  <my-popup show="{{ isShowEditMobileTips }}" closeable="{{false}}" title="温馨提示" confirmText="我知道了" content=""
    bindconfirm="showEditMobileTips" data-key="store" borderRadius="{{0}}" showType="normal">
    <view class="tips-content">
      <view class="tips-txt">亲爱的会员：</view>
      <view class="tips-txt">您好！</view>
    </view>
    <view class="tips-content">
      <view class="tips-txt">因系统升级，会员账户更换手机号的功能将于2月28日开放，您可届时再进行自主修改。</view>
    </view>
    <view class="tips-content">
      <view class="tips-txt">如您有紧急需求，也可通过在线客服与我们进行联络。我们人工客服的服务时间为：9:00~22:00。</view>

    </view>
    <view class="tips-content">
      <view class="tips-txt">感谢您的理解与支持，祝您生活愉快。</view>
    </view>
  </my-popup>

  <!-- 注册成功弹窗 -->
  <custom-modal show="{{success}}" modalId="{{modalId}}" bindclose="closeSuccess"></custom-modal>
</my-page>