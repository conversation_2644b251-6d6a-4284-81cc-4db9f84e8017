.cell-li-box {

  padding-bottom: 80rpx;


  .cell-li {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .cell-label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #3C3C43;
    line-height: 28rpx;
    text-align: left;
    font-style: normal;
  }

  .cell-jia {
    // width: 18rpx;
    // height: 18rpx;
    // line-height: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18rpx;
  }

  .cell-content {
    margin-top: 40rpx;

    &-title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 18rpx;
      color: #3C3C43;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
    }
  }

  .cell-item {
    width: 100%;
    position: relative;

    image {
      width: 100%;
    }
  }
}

// .cell-li-box:last-child {

// padding-bottom: 0rpx;
// }
