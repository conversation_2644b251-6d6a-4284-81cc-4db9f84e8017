<!-- node 递归子组件 -->
<!-- #ifdef MP-WEIXIN || MP-QQ -->
<wxs module="isInline">
  // 行内标签列表
  var inlineTags = {
    abbr: true,
    b: true,
    big: true,
    code: true,
    del: true,
    em: true,
    i: true,
    ins: true,
    label: true,
    q: true,
    small: true,
    span: true,
    strong: true,
    sub: true,
    sup: true
  }
  /**
   * @description 判断是否为行内标签
   */
  module.exports = function (tagName, style) {
    return inlineTags[tagName] || (style || '').indexOf('inline') !== -1
  }
</wxs>
<!-- #endif -->
<template name="el">
  <!-- 图片 -->
  <block wx:if="{{n.name==='img'}}">
    <!-- 表格中的图片，使用 rich-text 防止大小不正确 -->
    <rich-text wx:if="{{n.t}}" style="display:{{n.t}}" nodes="<img class='_img' style='{{n.attrs.style}}' src='{{n.attrs.src}}'>" data-i="{{i}}" catchtap="imgTap" />
    <block wx:else>
      <!-- 占位图 -->
      <image wx:if="{{(opts[1]&&!ctrl[i])||ctrl[i]<0}}" class="_img" style="{{n.attrs.style}}" src="{{ctrl[i]<0?opts[2]:opts[1]}}" mode="widthFix" />
      <!-- 显示图片 -->
      <image id="{{n.attrs.id}}" class="_img {{n.attrs.class}}" style="{{ctrl[i]===-1?'display:none;':''}}width:{{ctrl[i]||1}}px;height:1px;{{n.attrs.style}}" src="{{n.attrs.src}}" mode="{{!n.h?'widthFix':(!n.w?'heightFix':(n.m||'scaleToFill'))}}" lazy-load="{{opts[0]}}" webp="{{n.webp}}" show-menu-by-longpress="{{opts[3]&&!n.attrs.ignore}}"  data-i="{{i}}" bindload="imgLoad" binderror="mediaError" catchtap="imgTap" bindlongpress="noop" />
    </block>
  </block>
  <!-- 文本 -->
  <!-- #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO -->
  <text wx:elif="{{n.text}}" user-select="{{opts[4]=='force'&&isiOS}}" decode>{{n.text}}</text>
  <!-- #endif -->
  <text wx:elif="{{n.name==='br'}}">\n</text>
  <!-- 链接 -->
  <view wx:elif="{{n.name==='a'}}" id="{{n.attrs.id}}" class="{{n.attrs.href?'_a ':''}}{{n.attrs.class}}" hover-class="_hover" style="display:inline;{{n.attrs.style}}" data-i="{{i}}" catchtap="linkTap">
    <!-- #ifdef MP-WEIXIN || MP-QQ -->
    <node childs="{{n.children}}" opts="{{opts}}" style="display:inherit" />
    <!-- #endif -->
    <!-- #ifdef MP-BAIDU --> 
                                                   
                                                                                        
             
    <!-- #endif -->
    <!-- #ifdef MP-ALIPAY || MP-TOUTIAO --> 
                                                                                                
    <!-- #endif -->
  </view>
  <!-- 视频 -->
  <video wx:elif="{{n.name==='video'}}" id="{{n.attrs.id}}" class="{{n.attrs.class}}" style="{{n.attrs.style}}" autoplay="{{n.attrs.autoplay}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" muted="{{n.attrs.muted}}" object-fit="{{n.attrs['object-fit']}}" poster="{{n.attrs.poster}}" src="{{n.src[ctrl[i]||0]}}" data-i="{{i}}" bindplay="play" binderror="mediaError" />
  <!-- #ifndef MP-TOUTIAO -->
  <!-- 音频 -->
  <audio wx:elif="{{n.name==='audio'}}" id="{{n.attrs.id}}" class="{{n.attrs.class}}" style="{{n.attrs.style}}" author="{{n.attrs.author}}" controls="{{n.attrs.controls}}" loop="{{n.attrs.loop}}" name="{{n.attrs.name}}" poster="{{n.attrs.poster}}" src="{{n.src[ctrl[i]||0]}}" data-i="{{i}}" bindplay="play" binderror="mediaError" />
  <!-- #endif -->
  
  <!-- 富文本 -->
  <rich-text wx:else id="{{n.attrs.id}}" style="{{n.f}}"   user-select="{{opts[4]}}" nodes="{{[n]}}" />
</template>
<!-- #ifdef MP-ALIPAY || MP-TOUTIAO --> 
                       
                                                                          
                                                                                      
                                                                                                       
                                                                                                       
            
           
            
                                                                                     
<!-- #endif -->
<!-- #ifndef MP-ALIPAY || MP-TOUTIAO -->
<!-- 第 1 层 -->
<block wx:for="{{childs}}" wx:for-item="n1" wx:for-index="i1" wx:key="i1">
  <template wx:if="{{!n1.c&&(!n1.children||n1.name==='a'||!isInline(n1.name,n1.attrs.style))}}"  is="el" data="{{n:n1,i:''+i1,opts:opts,ctrl:ctrl}}" />
  <view wx:else id="{{n1.attrs.id}}" class="_{{n1.name}} {{n1.attrs.class}}" style="{{n1.attrs.style}}">
    <!-- 第 2 层 -->
    <block wx:for="{{n1.children}}" wx:for-item="n2" wx:for-index="i2" wx:key="i2">
      <template wx:if="{{!n2.c&&(!n2.children||n2.name==='a'||!isInline(n2.name,n2.attrs.style))}}"  is="el" data="{{n:n2,i:i1+'_'+i2,opts:opts,ctrl:ctrl}}" />
      <view wx:else id="{{n2.attrs.id}}" class="_{{n2.name}} {{n2.attrs.class}}" style="{{n2.attrs.style}}">
        <!-- 第 3 层 -->
        <block wx:for="{{n2.children}}" wx:for-item="n3" wx:for-index="i3" wx:key="i3">
          <template wx:if="{{!n3.c&&(!n3.children||n3.name==='a'||!isInline(n3.name,n3.attrs.style))}}"  is="el" data="{{n:n3,i:i1+'_'+i2+'_'+i3,opts:opts,ctrl:ctrl}}" />
          <view wx:else id="{{n3.attrs.id}}" class="_{{n3.name}} {{n3.attrs.class}}" style="{{n3.attrs.style}}">
            <!-- 第 4 层 -->
            <block wx:for="{{n3.children}}" wx:for-item="n4" wx:for-index="i4" wx:key="i4">
              <template wx:if="{{!n4.c&&(!n4.children||n4.name==='a'||!isInline(n4.name,n4.attrs.style))}}"  is="el" data="{{n:n4,i:i1+'_'+i2+'_'+i3+'_'+i4,opts:opts,ctrl:ctrl}}" />
              <view wx:else id="{{n4.attrs.id}}" class="_{{n4.name}} {{n4.attrs.class}}" style="{{n4.attrs.style}}">
                <!-- 第 5 层 -->
                <block wx:for="{{n4.children}}" wx:for-item="n5" wx:for-index="i5" wx:key="i5">
                  <template wx:if="{{!n5.c&&(!n5.children||n5.name==='a'||!isInline(n5.name,n5.attrs.style))}}"  is="el" data="{{n:n5,i:i1+'_'+i2+'_'+i3+'_'+i4+'_'+i5,opts:opts,ctrl:ctrl}}" />
                  <node wx:else id="{{n5.attrs.id}}" class="_{{n5.name}} {{n5.attrs.class}}" style="{{n5.attrs.style}}" childs="{{n5.children}}" opts="{{opts}}" />
                </block>
              </view>
            </block>
          </view>
        </block>
      </view>
    </block>
  </view>
</block>
<!-- #endif -->