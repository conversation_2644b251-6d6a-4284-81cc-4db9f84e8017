const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
      observer(val) {
        if (val) {
          this.calcHeight()
        }
      }
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
    // 弹窗背景色
    background: {
      type: String,
      value: 'white'
    },
    // 弹窗圆角
    borderRadius: {
      type: Number,
      value: 0
    },
    // 标题
    title: {
      type: String
    },
    // 内容 可以是富文本
    content: {
      type: String,
    },
    // 确认按钮
    confirmText: {
      type: String
    },
    // 取消按钮
    cancelText: {
      type: String
    },
    // 内容是否居中
    isCenter: {
      type: Boolean,
      value: true
    },
    zIndex: {
      type: Number,
      value: 1000000
    },
    showType: {
      type: String, // 传过来的值为normal时则为普通内容，如果是editor则是富文本默认字体大小不同
      value: 'normal'
    },
    overlayBackground: {
      type: String,
      value: 'rgba(0,0,0,.7)',
    },
    // 是否可以点击遮罩层关闭
    overlayClick: {
      type: Boolean,
      value: false
    },
  },
  data: {
    height: 0,
    handelContent: ''
  },
  ready() {
    if (this.data.show) {
      this.calcHeight()
    }
  },
  methods: {
    calcHeight() {
      let time = setTimeout(() => {
        clearTimeout(time)
        var query = wx.createSelectorQuery().in(this);
        query.select('#popup').boundingClientRect(rect => {
          // 元素的高度可以通过rect.height获取
          if (rect?.height) {
            this.setData({
              height: 1070 - rect.height / app.globalData.rpx
            })
          }
        }).exec();
      }, 100);

    },
    // 禁止穿透
    touchmove() {
      return false
    },
    // 禁止穿透
    touchmove1() {
      return true
    },
    // 关闭按钮
    close() {
      this.triggerEvent('close')
    },
    // 确认按钮
    confirm() {
      this.triggerEvent('confirm')
    },
    // 取消按钮
    cancel() {
      this.triggerEvent('cancel')
    },
  }
})
