const app = getApp()
import {
  throttle
} from '../../../../utils/util';
import {
  couponNum
} from '../../../../api/index'

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    userInfo: {
      type: Object,
      value: {},
      observer: (val) => {

      }
    },
    levelsList: {
      type: Array,
      value: [],
    },
    expandState: {
      type: Boolean,
      value: false,
    }
  },
  data: {
    currentLevel: '',
    couponNumInfo: 0, // 券梳理
    // bottomValue: '-160rpx',
    // expandState: false,
  },
  pageLifetimes: {
    show() {
      this.getCouponNum()
    }
  },
  lifetimes: {
    attached() {
      this.getCouponNum()
    },
  },
  methods: {
    handleExpand: app.debounce(async function (e) {
      this.triggerEvent('expand');
    }, 500),
    toLegend: throttle(async function (e) {
      wx.$mp.track({
        event: 'member_legend_click'
      })
      if (app.ifRegister()) {
        wx.$mp.navigateTo({
          url: '/pages/myLegend/myLegend',
          success: (result) => {},
          fail: (res) => {
            console.log(res);
          },
          complete: (res) => {},
        })
      }
    }),
    // 获取券梳理
    getCouponNum() {
      couponNum().then(res => {
        this.setData({
          couponNumInfo: res.data?.couponNum,
        })
      })
    },
    toCoupon: throttle(async function (e) {
      wx.$mp.track({
        event: 'member_coupon_click'
      })
      if (app.ifRegister()) {
        app.subscribe('coupon').then(() => {
          wx.$mp.navigateTo({
            url: '/pages/myCoupon/myCoupon',
            success: (result) => {},
            fail: (res) => {
              console.log(res);
            },
            complete: (res) => {},
          })
        })
      }
    }),

    toPotion: throttle(async function (e) {
      wx.$mp.track({
        event: 'member_point_click'
      })
      if (app.ifRegister()) {
        app.subscribe('point').then(() => {
          wx.$mp.navigateTo({
            url: '/pages/myIntegral/myIntegral',
            success: (result) => {},
            fail: (res) => {
              console.log(res);
            },
            complete: (res) => {},
          })
        })
      }
    }),

  }
})
