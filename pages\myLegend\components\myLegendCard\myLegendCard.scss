.my-credit {
  // margin-top: 20rpx;
  border-bottom: 1rpx solid #EEEEEE;
  padding: 40rpx 0;

  .credit-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // height: 181rpx;
    // background-color: var(--secondary-color);

    box-sizing: border-box;
    // border-bottom: 4rpx solid var(--primary-color);
    // border-top: 8rpx solid var(--primary-color);
  }

  .title-txt {
    display: flex;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    line-height: 36rpx;
    text-align: center;
    font-style: normal;
    color: var(--text-black-color);
  }

  .number-tips {
    margin-top: 30rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;

    text {
      color: #7F0019;
    }
  }

  .txt-point {
    width: 28rpx;
    height: 28rpx;
    margin-right: 10rpx;
  }

  .number-text {
    // height: 54rpx;

    font-family: MUJ<PERSON>ont2020, MUJIFont2020;
    font-weight: bold;
    font-size: 48rpx;
    color: #3C3C43;
    // line-height: 96rpx;
    text-align: left;
    font-style: normal;

    // margin-top: 8rpx;
    p {
      padding: 0;
    }
  }

  .algin-right {
    text-align: right;
  }

  .border-ele {
    border-bottom: 1rpx solid var(--text-black-color);
  }

  .btn-item {
    // margin-bottom: 23rpx;

    &:last-child {
      margin-bottom: 0rpx;
    }
  }
}
