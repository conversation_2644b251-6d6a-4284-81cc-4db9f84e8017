<view class="video" style="width:{{width*rpx}}px;height:{{data.heightSet?'100%':height*rpx+'px'}}">
  <!-- 背景 -->
  <custom-bg bgSetting="{{data}}" class="bgStyle"></custom-bg>
  <view class="video-content"
    style="border-radius:{{data.borderRadius*rpx}}px;left:{{data.paddingLeft*rpx}}px;right:{{data.paddingRight*rpx}}px;top:{{data.paddingTop*rpx}}px;bottom:{{data.paddingBottom*rpx}}px">
    <!-- 视频 -->
    <video id="video{{data.id}}" src="{{data.imgUrl}}" poster="{{data.coverUrl}}" show-center-play-btn='{{false}}'
      show-play-btn="{{true}}" controls="{{false}}" autoplay="{{data.autoplay}}" muted="{{muted}}" loop="{{data.loop}}"
      show-bottom-progress="{{false}}" show-play-btn="{{false}}" show-fullscreen-btn="{{false}}" object-fit="fill"
      class="video-img"
      style="width:{{videoWidth*rpx}}px;height:{{data.heightSet?'100%':videoHeight*rpx+'px'}}"></video>
    <!-- 热区 -->
    <view class="video-link">
      <view wx:for="{{data.imgLinks}}" wx:key="id"
        style="position: absolute;top:{{item.position[1]}}%;left:{{item.position[0]}}%;width:{{item.width}}%;height:{{item.height}}%;pointer-events: auto;">
        <custom-link data="{{item}}" class="linkStyle" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal"
          bindgoShare="goShare">
        </custom-link>
      </view>
    </view>
    <!-- 其他操作按钮 -->
    <view class="video-play" bindtap="changePlay" wx:if="{{data.playBtn}}">
      <image class="video-icon" src="{{$cdn}}/mini-play.png" wx:if="{{playing}}"></image>
      <image class="video-icon" src="{{$cdn}}/mini-pause.png" wx:else></image>
    </view>
    <view class="video-muted" bindtap="changeMuted" wx:if="{{data.muteBtn}}">
      <image class="video-icon" src="{{$cdn}}/mini-mute.png" wx:if="{{muted}}"></image>
      <image class="video-icon" src="{{$cdn}}/mini-voice.png" wx:else></image>
    </view>
  </view>
</view>