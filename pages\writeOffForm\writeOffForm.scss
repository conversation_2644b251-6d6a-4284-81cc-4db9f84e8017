/* pages/wirteOffForm/writeOffForm.wxss */
.page-container {
  .page-content {
    padding: 40rpx;
    box-sizing: border-box;
    position: relative;

    .page-block {
      margin-bottom: 60rpx;
      margin-top: 60rpx;

      &:first-child {
        margin-top: 0;
      }

      .block-title {
        width: 670rpx;
        // height: 42rpx;
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #3C3C43;
        line-height: 42rpx;
        text-align: left;
        font-style: normal;
        margin-bottom: 30rpx;
      }

      .top-list {
        height: 230rpx;
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #888888;
        line-height: 46rpx;
        text-align: left;
        font-style: normal;
        margin-top: 10rpx;
      }

      .block-list {

        font-family: PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;

        .list-item {
          margin-bottom: 30rpx;
        }

        .code-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          position: relative;

          .code-input {
            display: flex;
            align-items: center;
          }

          .code-btn {
            height: 36rpx;
            font-family: PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #7F0019;
            line-height: 36rpx;
            text-align: right;
            font-style: normal;
            position: relative;

            &::after {
              content: '';
              display: block;
              height: 1rpx;
              width: 100%;
              background-color: #7F0019;
              position: absolute;
              bottom: 0;
            }
          }
        }
      }

      .check-box {
        margin-top: 30rpx;

        .check-item {
          display: flex;
          justify-content: space-between;
          margin-top: 60rpx;
          align-items: center;

          &:first-child {
            margin-top: 0;
          }

          .check-radio {
            width: 28rpx;
            height: 28rpx;
            border: 2rpx solid #888888;
          }

          .active {
            background: #3C3C43;
            color: #fff;
            font-size: 18rpx;
            line-height: 28rpx;
            text-align: center;
            border: 2rpx solid #3C3C43;
          }
        }

        .reason-txt {
          margin-top: 30rpx;
          width: 670rpx;
          height: 220rpx;
          background: #FAFAFA;
          box-sizing: border-box;
          position: relative;
          padding: 30rpx;
          line-height: 40rpx;
          font-size: 24rpx;
          resize: none;

          .size-tips {
            position: absolute;
            height: 33rpx;
            font-family: PingFangSC,
              PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #BBBBBB;
            line-height: 33rpx;
            text-align: right;
            font-style: normal;
            bottom: 30rpx;
            right: 30rpx;
          }
        }
      }
    }

    .divided {
      width: 100%;
      height: 1rpx;
      background: #EEEEEE;
    }

    .tips {
      width: 648rpx;
      height: 28rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 400;
      font-size: 20rpx;
      color: #888888;
      line-height: 28rpx;
      text-align: left;
      font-style: normal;
      margin-top: 30rpx;
    }

    .bottom-box {
      margin-top: 80rpx;
    }
  }
}
