.clockPopup {
  position: relative;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-content {
    overflow: hidden;
    width: 630rpx;
    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 46rpx;
    padding-bottom: 39rpx;
    // padding-right: 100rpx;
    // padding-left: 100rpx;
    box-sizing: border-box;

    .iconfont1 {
      width: 100rpx;
      height: 94rpx;
      background-size: 100% 100%;
    }

    .title {
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 36rpx;
      color: var(--text-black-color);
      line-height: 67rpx;
      letter-spacing: 3px;
      text-align: center;
      margin-top: 45rpx;
    }

    .text {
      width: 520rpx;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 45rpx;
      text-align: left;
      color: var(--text-black-color);
      margin-top: 30rpx;
      margin-left: 8rpx;
    }

    .clock-btn {
      margin-top: 83rpx;
    }
  }

  &-close {
    padding-top: 10rpx;
    height: 80rpx;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-closeBox {
    height: 80rpx;
    width: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
