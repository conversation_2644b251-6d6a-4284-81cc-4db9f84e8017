.card-container {
  width: 670rpx;
  height: 404rpx;
  background: #FFFFFF;
  border-radius: 5rpx;
  margin: 30rpx auto;
  box-sizing: border-box;
  padding: 40rpx;

  .card-header {
    height: 33rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 30rpx;
  }

  .card-content {
    margin-top: 30rpx;
    margin-bottom: 30rpx;
    box-sizing: border-box;

    .card-single {
      display: flex;
      height: 100%;
      position: relative;

      .single-info {
        margin-left: 20rpx;
        height: 150rpx;
        position: relative;

        .title {
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #3C3C43;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
        }

        .single-num {
          position: absolute;
          bottom: 10rpx;

          display: flex;
          align-items: center;

          height: 33rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #3C3C43;
          line-height: 33rpx;
          text-align: left;
          font-style: normal;
          white-space: nowrap;
        }
      }
    }

    .card-multi {
      position: relative;
      display: flex;
      overflow-x: auto;
      height: 160rpx;
    }

    .card-img {
      display: flex;
      align-items: center;
      justify-content: center;

      width: 160rpx;
      height: 160rpx;
      background-color: #FAFAFA;
      flex-shrink: 0;

      & + .card-img {
        margin-left: 20rpx;
      }

      .image {
        flex-shrink: 0;
        width: 150rpx;
        height: 150rpx;
      }
    }
  }

  .card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-top: 1rpx solid #eee;
    padding-top: 30rpx;

    .num {
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #888888;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
    }

    .price {
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
    }
  }

}
