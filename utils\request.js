import { loginWechat } from "../api/index";
const noErrorUrl = [];

let subscribers = [];
let ifPending = false; // token是否刷新中
function request({ url, data, method, callback, timeout = 60000 }) {
  const header = {
    "Content-Type": "application/json;charset=utf-8",
    token: wx.getStorageSync("token"),
  };
  return new Promise(function (resolve, reject) {
    const app = getApp();
    wx.request({
      url: wx.$config.baseUrl + url,
      data,
      method: method || "get",
      header,
      timeout, // 超时时间，单位为毫秒。默认值为 60000
      success: function (res) {
        console.log(
          "%c 请求地址:",
          "color: red",
          `(${(method || "get").toUpperCase()}) ${wx.$config.baseUrl}${url}`
        );
        console.log("请求参数:", data || {});
        console.log("响应数据:", res.data);
        console.log("token:", wx.getStorageSync("token"));
        console.log("\n\n");
        // 成功
        if (res.data.code === 0) {
          if (callback) {
            callback(res.data);
            return;
          }
          resolve(res.data);
        } else if (res.data.code === 401) {
          // 接口失效重新请求
          console.log("%c 接口401调用token", "color: red;fontSize:30px;");
          // 正在token获取加载中
          if (ifPending) {
            app.globalData.tokenPromise.then(() => {
              resolve(
                request({
                  url,
                  data,
                  method,
                  callback,
                })
              );
            });
            return;
          }
          ifPending = true;
          wx.clearStorageSync();
          console.log("重新获取token过程中");
          app.globalData.tokenPromise = new Promise((resolve) => {
            wx.login({
              success: (res) => {
                const { code } = res;
                loginWechat({
                  code,
                  appId: wx.$config.appId,
                }).then((value) => {
                  wx.setStorageSync("token", value.data.token);
                  app.globalData.token = value.data.token;
                  // token失效有可能是用户注销 需要重新获取用户信息
                  // if (app.globalData.userInfo?.id) {
                  //   resolve()
                  // } else {
                  resolve(app.getUserInfo());
                  // }
                });
              },
            });
          });
          app.globalData.tokenPromise.then(() => {
            ifPending = false;
            resolve(
              request({
                url,
                data,
                method,
                callback,
              })
            );
          });
        } else if (res.data.code > 1000) {
          // 弹窗提醒错误 需要UI出错误的弹窗 在页面定制展示  若是UI没有出弹窗  后端不允许返回》1000的code
          console.log("弹系统错误弹窗");
          reject(res.data);
        } else {
          wx.showToast({
            title: res.data.msg || "请求失败",
            icon: "none",
            duration: 2000,
          });
          reject({});
        }
      },
      fail: function (err) {
        console.error(err, "请求失败");
        wx.showToast({
          title: "访问人数较多，请稍后",
          icon: "none",
          duration: 2000,
        });

        reject({});
      },
    });
  });
}

export default wx.$request = request;
