const app = getApp()

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    numberData: {
      type: Number,
      value: 0,
    },
    point: {
      type: Object,
      value: {}
    },
    showType: {
      type: Number,
      value: 1
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    btnList: [{
        label: '里程规则',
        value: 'exchangeRecord',
        icon: '/member/point.png'
      },
      {
        label: '积分规则',
        value: 'integralRule',
        icon: '/member/point.png'
      },

    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    toRlue: app.debounce(async function (e) {
      const {
        key
      } = e.currentTarget.dataset
      switch (key) {
        case "exchangeRecord":
          wx.$mp.track({
            event: 'member_point_rule_click',
          })
          wx.$mp.navigateTo({
            url: '/pages/exchangeRecord/exchangeRecord',
            success: (result) => {},
            fail: (res) => {
              console.log(res);
            },
            complete: (res) => {},
          })
          break
        case "integralRule":
          wx.$mp.track({
            event: 'member_legend_rule_click',
          })
          wx.$mp.navigateTo({
            url: '/pages/integralRule/integralRule',
            success: (result) => {},
            fail: (res) => {
              console.log(res);
            },
            complete: (res) => {},
          })
          break
      }
    }),
    gotoExpireList() {
      wx.$mp.navigateTo({
        url: '/pages/expirePoint/expirePoint',
        success: (result) => {},
        fail: (res) => {
          console.log(res);
        },
        complete: (res) => {},
      })
    }
  },
})
