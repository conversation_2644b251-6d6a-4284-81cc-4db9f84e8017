/* pages/guide/guide.wxss */
page {
  background-color: #f7f7f7;
}

.page-content {
  width: 670rpx;
  height: 100%;
  position: relative;
  top: var(--top);
  left: 50%;
  transform: translateX(-50%);
  padding-bottom: 90rpx;
  // overflow-y: scroll;
  overflow-x: hidden;

  &-item {
    padding: 60rpx 36rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    text-align: center;
  }

  .title {
    height: 54rpx;
    font-family: SourceHanSansCN, MUJIFont2020;
    font-weight: 700;
    font-size: 36rpx;
    color: #3C3C43;
    line-height: 54rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    // margin-top: 40rpx;
  }

  .info-box {
    // width: 650rpx;
    // margin: 40rpx auto 0;
    margin-top: 40rpx;
    text-align: center;


  }

  .store-name {
    font-family: SourceHanSansCN, MUJIFont2020;
    font-weight: 500;
    font-size: 36rpx;
    color: #3C3C43;
    line-height: 54rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    // margin-top: 40rpx;
  }


  .address-box {


    &-phone {
      margin-top: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &-num {
        margin-left: 10rpx;
        font-family: SourceHanSansCN, MUJIFont2020;
        font-weight: 300;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 36rpx;
        text-align: left;
        font-style: normal;
        text-transform: none;
        text-decoration: underline;
      }
    }

    &-item {
      padding-top: 31rpx;
      display: flex;
      // align-items: center;
      justify-content: center;


      &-title {
        margin-left: 10rpx;
        max-width: 483rpx;
        font-family: SourceHanSansCN, SourceHanSansCN, PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 36rpx;
        font-style: normal;
        text-transform: none;
        // text-align: left;

        // white-space: nowrap;
        // overflow: hidden;
        // text-overflow: ellipsis;
      }

      .iconfont {
        font-size: 36rpx;
      }
    }




  }

  .qrcode-line {
    width: 570rpx;
    height: 1rpx;
    border-top: 2rpx dashed rgba(60, 60, 67, 0.1);
    margin: 0 auto;
  }

  .qrcode-box {
    margin: 40rpx auto;
    // padding: 60rpx;
    box-sizing: border-box;
    position: relative;
    z-index: 0;

    .no-data {
      margin: 187rpx 0;
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 36rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }

    .qrcode-img {
      width: 280rpx;
      height: 280rpx;
      margin: 0 auto;

      image {
        width: 280rpx;
        height: 280rpx;
      }
    }

    .txt {
      font-family: SourceHanSansCN, MUJIFont2020;
      font-weight: 400;
      font-size: 20rpx;
      color: #888888;
      line-height: 30rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
      margin-top: 20rpx;
    }
  }

  .service-time {
    // display: flex;

    margin-top: 30rpx;

    &-item {
      display: flex;
      justify-content: center;
    }

    &-li {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 36rpx;
    }
  }
}

.fixed-bottom {
  position: fixed;
  width: 100%;
  bottom: 0;
  // height: 180rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #F7F7F7;
  padding-top: 30rpx;
  padding-bottom: 80rpx;
}

.save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rpx 0rpx 1rpx 1rpx;
  width: 290rpx;
  height: 80rpx;
  background: #3C3C43;
  border-radius: 5rpx;
  // margin: 0 auto;
  font-family: SourceHanSansCN, MUJIFont2020;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
  // line-height: 80rpx;
  letter-spacing: 1px;
  text-align: center;
  font-style: normal;
  box-sizing: border-box;
  border: 1rpx solid rgba(27, 27, 51, 0.8);
  box-sizing: border-box;

  &.address {
    margin-right: 40rpx;
    width: 290rpx;
    margin-left: 1rpx;
    background: #F7F7F7;
    border-radius: 5rpx;
    // border: 1rpx solid rgba(27, 27, 51, 0.8);
    color: #3C3C43;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
