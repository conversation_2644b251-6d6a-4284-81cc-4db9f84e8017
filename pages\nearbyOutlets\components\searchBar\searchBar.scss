.search-bar {
  width: 100%;
  box-sizing: border-box;
  // padding: 0 40rpx;
  width: 670rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #F5F5F5;
  border-radius: 5rpx;
  margin: 0rpx auto;
  z-index: 10000;
  position: relative;
  display: flex;
  align-items: center;

  .left-box {
    padding: 0 20rpx;
    position: relative;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;
    text-align: center;
    font-style: normal;

    &::after {
      content: '';
      display: inline-block;
      position: absolute;
      width: 1rpx;
      height: 24rpx;
      background-color: #000;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .icon {
      height: 48rpx;
      width: 48rpx;
      position: relative;
      top: 15rpx;
    }
  }

  .middle-box {
    flex: 1;
    padding: 0 20rpx;
    position: relative;

    .input-search {
      display: flex;
      align-items: center;
      position: relative;
      width: 100%;

      .icon-box {
        height: 48rpx;
        width: 48rpx;

        .icon-img {
          height: 48rpx;
          width: 48rpx;
        }
      }
    }
  }

  .right-box {
    width: 80rpx;
    height: 80rpx;
    background: #3C3C43;
    border-radius: 0rpx 5rpx 5rpx 0rpx;
    position: relative;

    image {
      display: block;
      width: 50rpx;
      height: 50rpx;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
