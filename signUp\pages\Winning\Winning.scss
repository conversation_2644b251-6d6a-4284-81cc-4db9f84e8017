/* signUp/pages/Winning/Winning.wxss */
@import "assets/scss/common";
@import "assets/scss/config";

.page-container {
  background: linear-gradient(rgba(255, 255, 255, 0) 1%, #F8F6ED 100%);
  background-size: 100% 100%;

  .prizeDraw1 {
    flex: 1;
    overflow-y: auto;
    position: relative;

    .activeRules {
      position: absolute;
      right: 0rpx;
      top: 90rpx;
      width: 44rpx;
      height: 146rpx;
      background: #C7B397;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      writing-mode: vertical-rl;
      letter-spacing: 4rpx;
      z-index: 2;
    }

    .goLottery {
      position: absolute;
      right: 12rpx;
      bottom: 328rpx;
      width: 166rpx;
      height: 166rpx;
      z-index: 100;

      .lottery-img {
        width: 166rpx;
        height: 166rpx;
      }

      .lottery-title {
        position: absolute;
        bottom: 23rpx;
        left: 50%;
        transform: translateX(-50%);
        font-family: SourceHanSansCN;
        font-weight: 700;
        font-size: 22rpx;
        color: #3C3C43;
        line-height: 32rpx;
        text-align: center;
      }
    }

    .MyCoupon {
      position: absolute;
      right: 51rpx;
      top: 25rpx;
      padding: 30rpx;
      font-family: SourceHanSansCN;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1rpx;
      text-align: center;
      font-style: normal;
      text-decoration-line: underline;

    }

    .prizeDraw-title1 {
      height: 40rpx;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 24rpx;
      line-height: 40rpx;
      letter-spacing: 1rpx;
      margin-top: 31rpx;
    }

    .title {
      margin-left: 62rpx;
    }

    .prizeDraw-title2 {
      height: 40rpx;
      font-weight: 300;
      font-size: 24rpx;
      line-height: 40rpx;
      letter-spacing: 1rpx;

    }

    .swiper-wrap {
      margin-top: 149rpx;
      position: relative;

      .tips {
        display: flex;
        align-items: center;
        margin-left: 73rpx;


        .tips-img {
          width: 74rpx;
          height: 76rpx;
          margin-right: 21rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .prizeDraw-title3 {
          font-weight: 300;
          font-size: 32rpx;
          color: #2E2E2E;
          line-height: 39rpx;
          letter-spacing: 1rpx;
          text-align: left;

        }

        .prizeDraw-title4 {
          // width: 493rpx;
          font-weight: 700;
          font-size: 42rpx;
          color: #2E2E2E;
          line-height: 52rpx;
          letter-spacing: 1rpx;
          text-align: left;
        }
      }

      .picture {
        box-sizing: border-box;
        height: 732rpx;
        margin-right: 77rpx;
        margin-left: 77rpx;
        border-top: 1rpx dashed #3C3C43;
        border-bottom: 1rpx dashed #3C3C43;
        margin-top: 36rpx;
        padding: 2rpx 0;

        image {
          width: 100%;
          height: 100%;
        }

        .slider-point-Left {
          width: 37rpx;
          height: 37rpx;
          position: absolute;
          top: 50%;
          left: 0rpx;
          transform: translateY(-50%);
          padding: 20rpx;

          .point_left {
            width: 0rpx;
            height: 0rpx;
            border-right: 20rpx solid #C8B49A;
            border-left: 20rpx solid transparent;
            border-top: 20rpx solid transparent;
            border-bottom: 20rpx solid transparent;
          }
        }

        .slider-point-Right {
          width: 37rpx;
          height: 37rpx;
          position: absolute;
          top: 50%;
          right: 0rpx;
          transform: translateY(-50%);
          padding: 20rpx;

          .point_right {
            width: 0rpx;
            height: 0rpx;
            border-left: 20rpx solid #C8B49A;
            border-right: 20rpx solid transparent;
            border-top: 20rpx solid transparent;
            border-bottom: 20rpx solid transparent;
          }
        }
      }

      .bottom-tips {
        z-index: 222;
        font-family: MUJIFont2020,
          SourceHanSansCN;
        // height: 80rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #3C3C43;
        line-height: 40rpx;
        letter-spacing: 1rpx;
        text-align: center;
        margin-top: 70rpx;

        .text1 {
          font-weight: 400;
          font-size: 22rpx;
          color: #3C3C43;
          line-height: 40rpx;
          letter-spacing: 1rpx;
          text-align: center;
        }

        .text1-MyCoupon {
          font-family: SourceHanSansCN;
          font-weight: 500;
          font-size: 24rpx;
          color: #3C3C43;
          line-height: 40rpx;
          letter-spacing: 1px;
          text-align: center;
          // text-decoration-line: underline;
          padding: 20rpx;

          text {
            display: inline-block;
            border-bottom: 1rpx solid #3C3C43;
          }
        }
      }

      .bottom-tips1 {
        margin-top: 40rpx;
      }

      .slider-point-wrap {
        position: absolute;
        bottom: 225rpx;
        left: 50%;
        transform: translateX(-50%);

        .slider-point {
          display: flex;
          align-items: center;

          .slider-point-item {
            width: 13rpx;
            height: 13rpx;
            background: #FFFFFF;
            border-radius: 50%;
            margin-right: 20rpx;

            &:nth-last-child(1) {
              margin-right: 0rpx;
            }
          }

          .active {
            background: #C8B49A;
          }
        }

        .slider-tips {
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 18rpx;
          color: #000000;
          line-height: 25rpx;
          text-align: center;
          margin-top: 14rpx;
        }
      }


    }

  }

  .bottom-box {
    margin-top: 34rpx;
    margin-bottom: env(safe-area-inset-bottom);
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .isShare {
      font-family: SourceHanSansCN;
      font-weight: 500;
      font-size: 26rpx;
      color: #3C3C43;
      line-height: 40rpx;
      letter-spacing: 1rpx;
      text-align: center;
      margin-top: 14rpx;
    }
  }

  .share_mask {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
  }

  .page-canvas {
    position: fixed;
    left: 0;
    top: 200vh;
    width: 497rpx; // canvas按照750的宽度绘制
    height: 1045rpx;
  }

  .page-canvasone {
    position: fixed;
    left: 0;
    top: 200vh;
    width: 662rpx;
    height: 1222rpx;
  }

}

.share-popup {
  .image {
    width: 662rpx;
    height: 1222rpx;
  }

  .clock-btn {
    margin-top: 40rpx;
    display: flex;
    // background-color: #ffffff;
    justify-content: space-between;

  }
}
