<van-popup show="{{ isShow }}" bind:close="onClose" position="bottom" custom-style="min-height: 20%; max-height: 80%" closeable>
  <view class="city-content">
    <view class="title">选择城市</view>
    <view class="picker-box">
      <van-picker columns="{{ cityList }}" bind:change="onChange" visible-item-count="{{3}}" column-class="city-item" item-height="60" custom-class="list-picker" value-key="{{currentCity}}"/>
    </view>
    <view class="btn-box">
      <view class="btn-item" bindtap="confirm">确认</view>
    </view>
  </view>
</van-popup>