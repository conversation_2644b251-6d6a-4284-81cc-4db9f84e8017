export const apiPrivacyAgreement = {
  async getPrivacyAgreement () {
    return {
      content: `<div>
        <div>
          <a href="https://www.gov.cn">
            <img src="https://picsum.photos/200/100?r=1.1" alt="">
          </a>
          <p>上图有超链，点击上方图片复制第三方链接</p>
        </div>
        <div>
          <a href="/pages/index/index">
            <img src="https://picsum.photos/200/100?r=1.2" alt="">
          </a>
          <p>上图有超链，点击上方图片跳入小程序首页</p>
        </div>
        <div>
          <a href="openPrivacyContract">
            <img src="https://picsum.photos/200/100?r=1.2" alt="">
          </a>
          <p>上图有超链，点击上方图片跳入小程序隐私</p>
        </div>
        <div><a href="https://www.gov.cn">点击此处文案复制第三方链接</a></div>
        <div><a href="/pages/index/index">点击此处文案跳入小程序首页</a></div>
        <div><a href="openPrivacyContract">点击此处文案跳入小程序隐私</a></div>
        <div>变量解析之会员卡号：##会员卡号##</div>
        <div>变量解析之会员等级：##会员等级##</div>
        <div>变量解析之注册时间：##注册时间##</div>
        <div>隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议议隐私协议</div>
        <div>隐私协协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>隐私协议隐私协议私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>隐私议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议隐私协议</div>
        <div>
          <p>下图无超链，点击下方图片进行图片预览</p>
          <img src="https://picsum.photos/200/100?r=2.1" alt="">
        </div>
        <div>
          <p>下图无超链，点击下方图片进行图片预览</p>
          <img src="https://picsum.photos/200/100?r=2.2" alt="">
        </div>
      </div>`
    }
  }
}
