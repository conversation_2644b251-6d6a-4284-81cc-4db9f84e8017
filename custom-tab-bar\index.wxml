<view class="tab-bar iPhoneXpb" style="background:{{tabBar.bgColor}}" catch:touchmove="touchmove" wx:if="{{isShowTab}}">
  <view class="tab-bar-main">
    <block wx:for="{{tabBar.list}}" wx:key="id">
      <view class="tab-bar-item {{tabBar.birthday&&isBirthday?'birthday':''}} {{item.iconType==2?'big':''}}"
        data-item="{{item}}" bindtap="switchTab" data-index="{{index}}">
        <view class="image ">
          <image class="image-item selected"
            src="{{tabBar.birthday&&isBirthday?item.birthdaySelectedIconPath:item.selectedIconPath}}"
            wx:if="{{selected==index}}"></image>
          <image class="image-item normal" src="{{tabBar.birthday&&isBirthday?item.birthdayIconPath:item.iconPath}}"
            wx:else></image>
        </view>
        <view wx:if="{{item.showType==2}}" class="text {{selected===index&&'selected'}}"
          style="color:{{selected===index?tabBar.selectedColor:tabBar.color}};font-weight:{{selected===index?'bold':400}};font-size:{{tabBar.fontSize}}rpx">
          {{item.text}}
        </view>
      </view>
    </block>
  </view>
  <!-- 游客模式 -->
  <view class="tab-bar-visitor" wx:if="{{visitor}}" catchtap="showPrincy"></view>
</view>