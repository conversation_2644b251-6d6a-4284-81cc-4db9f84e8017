<page-meta page-style="{{disablePageScroll?'overflow:hidden;':''}}" />
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-content" bindscrolltolower="getMore">
    <custom-header type="{{3}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <view class="nav-btn">
          <view class="btn">
            <view class="link link-search" bind:tap="goSearch">
              <view class="iconfont icon-Search" />
            </view>
          </view>
          <view class="divided" />
          <view class="btn">
            <view class="link link-cart" bind:tap="onTapCart">
              <view class="iconfont icon-Cart">
                <view wx:if="{{cartCount > 0}}" class="count">{{cartCount}}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </custom-header>

    <block wx:if="{{userInfo.isMember===1}}">

      <my-credits myCredits="{{currentPoint}}" isShow="{{isShowMyPoint}}" />
    </block>
    <view class="column" style="height: {{tabList.length > 0 ? 203 : 103}}rpx; overflow: hidden">
      <view class="{{isFixedTop?'fixed':''}}" style="top: {{navHeight}}px; overflow: hidden;" id="stickyElement">
        <filer-box catch:updateTabList="updateTabList" tabListData="{{tabList}}"  bind:filter="handleFilter" topHeight="{{stickyElement.bottom}}" filterData="{{filterData}}" showMine="{{userInfo.isMember===1}}"  bind:toggleSticky="toggleSticky" showFilterDialog="{{showFilterDialog}}" bind:closeDialog="closeFilterDialog" />
      </view>
    </view>
    <!-- 用于吸顶后 占位用的 -->
    <!-- <view class="column" wx:if="{{isFixedTop}}" style="height: {{navBarInfo.height}}px;"></view> -->
    <view class="product-container">
      <view class="divided" />
      <view class="total-box">
        共有 {{count}} 件商品
      </view>
      <block wx:if="{{currentShelfId && bannerContent && filterData.oneTagId===undefined && filterData.twoTagIdList===undefined}}">
        <banner-list content="{{bannerContent}}" bind:change="bannerChange" />
      </block>
      <view class="product-list">
        <view class="product-item" wx:for="{{productList}}" wx:key="shelfProductId">
          <product-card skuInfo="{{item}}" />
        </view>
        <view wx:if="{{!loading && productList.length===0}}" style="width: 670rpx; min-height: 400rpx; display: flex; align-items: center;">
          <view class="no-data-box">暂无商品</view>
        </view>
      </view>
    </view>
  </view>
  <my-popup show="{{ showPointsDialog }}" closeable="{{false}}" title="积分兑换提醒" confirmText="我知道了" content="积分兑换后，需前往线下门店进行核销，MUJI欢迎您的到来。" bindclose="closeRemind" bindconfirm="setTodayReminds" data-key="store" borderRadius="{{0}}" showType="normal">
    <view class="no-reminders" bindtap="handleNoRemind">
      <view class="tips-icon {{isKeepReminds ? 'active-tip' : ''}}">
        <view wx:if="{{isKeepReminds}}" class="iconfont icon-Mark" />
      </view>
      不再提醒我
    </view>
  </my-popup>
</my-page>



<view wx:if="{{rightFixedWrapVisible}}" class="right-fixed-wrap" catch:tap="goInteractiveTask">
  <image class="right-fixed" src="{{$cdn}}/life/interacting-with-courtesy.png" mode="aspectFit" />
</view>
<movable-area class="moveIcon" wx:if="{{showIcon}}">
  <movable-view direction="vertical" class="moveIcon-item" y="645rpx">
    <image src="{{$cdn}}/firendShip/back.png" class="moveIcon-img" bindtap="goGift"></image>
  </movable-view>
</movable-area>