<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>兑换详情</text>
      </view>
    </custom-header>
    <basic-tips tipsTxt="积分兑换后不支持退还，请仔细阅读商品兑换须知" />
    <view class="page-content">
      <view class="box">
        <view class="title-box">商品信息</view>
        <view class="list-box">
          <view class="product-item" wx:for="{{detail.productList}}">
            <product-card skuInfo="{{item}}" bind:tap-use="onTapUse" />
          </view>
        </view>
      </view>

      <view wx:if="{{detail.orderCode}}" class="box">
        <view class="title-box">订单信息</view>
        <view class="info-ele">
          <view class="ele-label">订单编号</view>
          <view class="ele-value">{{detail.orderCode}}</view>
        </view>

        <view class="info-ele">
          <view class="ele-label">兑换时间</view>
          <view class="ele-value">{{detail.created}}</view>
        </view>

        <view class="info-ele">
          <view class="ele-label">消耗积分</view>
          <view class="ele-value">{{detail.orderPoint}}积分</view>
        </view>

        <view class="info-ele">
          <view class="ele-label">线下支付金额</view>
          <view class="ele-value">{{detail.orderAmount || 0}}元</view>
        </view>
      </view>
    </view>
  </view>
</my-page>