.category-content {
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 0;
  box-sizing: border-box;
  width: 100%;

  .title {
    height: 36rpx;
    font-family: NotoSansHans,
      NotoSansHans;
    font-weight: 500;
    font-size: 36rpx;
    color: #3C3C43;
    letter-spacing: 1px;
    text-align: center;
    font-style: normal;
    margin-bottom: 60rpx;
  }

  .category-list {
    .category-item {
      margin-top: 64rpx;
      margin-bottom: 64rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .icon {
        height: 44rpx;
        width: 44rpx;
        margin-right: 20rpx;

        image {
          width: 44rpx;
          height: 44rpx;
        }
      }

      .label {
        flex: 1;
        text-align: left;
      }

      .select-btn {
        width: 28rpx;
        height: 28rpx;
        border: 1rpx solid #888888;
        display: flex;
        align-items: center;
        justify-content: center;

        ._icon {
          font-size: 20rpx;
          color: #fff;
        }
      }

      .active {
        background-color: #000;
      }
    }
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      width: 320rpx;
      height: 80rpx;
      border-radius: 5rpx;
      border: 1rpx solid #3C3C43;
      line-height: 80rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #3C3C43;
      font-style: normal;

      &:last-child {
        background: #3C3C43;
        color: #fff;
      }
    }
  }
}
