const app = getApp()
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {}
      },
    },
    // 游客模式
    visitor: {
      type: <PERSON><PERSON>an,
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750
    }
  },
  data: {
    rpx: app.globalData.rpx
  },
  lifetimes: {
    attached() { }
  },
  methods: {

  }
})
