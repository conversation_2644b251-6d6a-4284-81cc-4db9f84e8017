/* pages/myCoupon/myCoupon.wxss */
.gray-bg {
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  .page-nav {
    background-color: #fff;
    padding-top: 30rpx ;
    padding-left: 80rpx;
    padding-right:80rpx ;
    
  }

  .page-content {
    flex: 1;
    height: 0;
    width: 100%;
    overflow: auto;
    margin-top: 40rpx;
   
    .list-box {
      margin: var(--page-margin);
      margin-top: 0;
      // padding-bottom: 156rpx;

      .list-item {
        margin-bottom: 30rpx;
      }
    }
  }


}

.footer-li {
  // margin-top: 40rpx;
  // position: fixed;
  // bottom: 0;
  width: 100%;
  height: 156rpx;
  background: #F5F5F5;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  // line-height: 80rpx;
  // text-align: center;
  font-style: normal;

  .footer-text {
    margin-top: 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    border-bottom: 1rpx solid #BBB;
    // width: 94rpx;
    padding-bottom: 2rpx;
  }

  .footer-line {
    width: 94rpx;
    height: 0rpx;
    // line-height: 0;
    // border-bottom: 1rpx solid #BBB;
  }
}
