.page-container {
  min-width: 100vh;
  min-height: 100vw;
  overflow: hidden;
  transform: rotate(90deg) translateY(-100vmin);
  transform-origin: 0 0;
  overscroll-behavior: none;
  -webkit-overflow-scrolling: none;


  .page-content {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    text-align: center;
    height: 100vw;
    width: 100vh;

    .title {
      // width: 42rpx;
      // height: 729rpx;
      font-family: SourceHanSansCN,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 42rpx;
      color: #000000;
      line-height: 63rpx;
      letter-spacing: 1px;
      // text-align: left;
      font-style: normal;
      margin-bottom: 111rpx;
    }

    .card-text {
      font-family: MUJIFont2020,
        MUJIFont2020;
      font-weight: 400;
      font-size: 24rpx;
      color: #888888;
      line-height: 48rpx;
      text-align: left;
      font-style: normal;
      margin-top: 100rpx;
    }
  }

  .barcode-img {
    .canvas-box {
      position: fixed;
      right: 9000px;
      z-index: -1 !important;
    }

    .canvas-image {
      width: 1053rpx;
      height: 257rpx;
    }
  }
}
