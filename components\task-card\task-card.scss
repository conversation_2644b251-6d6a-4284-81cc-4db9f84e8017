.task-card-item {
  position: relative;
  width: 100%;
  background: #FAFAFA;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  z-index: 0;

  .task-card-box {
    padding: 40rpx 30rpx;
    display: flex;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
  }

  .task-icon {
    width: 78rpx;
    height: 78rpx;
    background: #F4EEDE;
    border-radius: 39rpx;
    overflow: hidden;

    &-img {
      width: 78rpx;
      height: 78rpx;
    }
  }

  .task-info {
    margin-left: 30rpx;
    position: relative;
    flex: 1;

    .task-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 33rpx;
      font-family: SourceHanSansSC,
        SourceHanSansSC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 33rpx;
      text-align: right;
      font-style: normal;
      gap: 10rpx;

      &:first-child {
        margin-bottom: 20rpx;
      }

      .rule-icon {
        flex: 1;
        text-align: right;
        color: #888;
      }

      .task-step {
        display: flex;
        gap: 20rpx;

        .step-item {
          width: 36rpx;
          height: 36rpx;
          border-radius: 50%;
          overflow: hidden;
          background: #DDDDDD;

          .complated-item {
            width: 36rpx;
            height: 36rpx;
            background: #7F0019;
            text-align: center;

            .iconfont {
              color: #fff;
              font-size: 20rpx;
            }
          }

          .uncomplate-item {
            height: 36rpx;
            font-family: MUJIFont2020,
              MUJIFont2020;
            font-weight: bold;
            font-size: 12rpx;
            color: #FFFFFF;
            line-height: 36rpx;
            text-align: center;
            font-style: normal;
            // background-color: #ddd;

            .iconfont {
              color: white;
              font-size: 18rpx;
              font-weight: 300;
            }
          }

          .coupon {
            font-size: 16rpx;
          }
        }
      }
    }

    .gray-font {
      width: 72rpx;
      height: 24rpx;
      font-family: SourceHanSansSC,
        SourceHanSansSC;
      font-weight: 500;
      font-size: 24rpx;
      color: #888888;
      line-height: 36rpx;
      text-align: right;
      font-style: normal;
    }

    .go-btn {
      font-size: 22rpx;
      color: #3C3C43;
      position: relative;
      z-index: 100;
      background-color: #FAFAFA;

      &:hover,
      &:focus,
      &:active {
        background-color: #FAFAFA;
        opacity: 1;
        outline: none;
      }
    }

    .go-icon {
      display: inline-block;
      font-size: 22rpx;
    }


    .task-txt {
      display: flex;
      align-items: center;
      height: 33rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 33rpx;
      text-align: left;
      font-style: normal;

      .task-type {
        color: #7F0019;
      }

      .task-content {
        padding-left: 12rpx;
        margin-left: 12rpx;
        position: relative;
        box-sizing: border-box;

        &::before {
          content: '';
          display: block;
          height: 20rpx;
          width: 2rpx;
          background-color: #3C3C43;
          position: absolute;
          left: 0;
          top: 7rpx;
        }
      }
    }

    .credit-num {
      font-family: MUJIFont2020,
        MUJIFont2020,
        SourceHanSansCN;
      font-weight: 500;
      font-size: 22rpx;
      color: #7F0019;
      line-height: 44rpx;
      letter-spacing: 1px;
      text-align: right;
      font-style: normal;
      max-width: 162rpx;
    }
  }

  .task-credit {
    font-family: SourceHanSansCN,
      SourceHanSansCN;
    font-weight: 500;
    font-size: 24rpx;
    color: #3C3C43;
    // line-height: 36rpx;
    text-align: right;
    font-style: normal;
  }

  .limit-row {
    height: 28rpx;
    width: 100%;
    position: relative;
  }

  .limit-time {
    position: absolute;
    top: 0;
    left: 0;
    height: 28rpx;
    background: #F4EEDE;
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 18rpx;
    color: #7F0019;
    line-height: 28rpx;
    text-align: justify;
    font-style: normal;
    padding: 0 10rpx;
  }
}
