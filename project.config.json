{"appid": "wx38d029d79dea452e", "projectname": "muji-mini", "compileType": "miniprogram", "libVersion": "development", "packOptions": {"ignore": [{"value": "images", "type": "folder"}], "include": []}, "condition": {"miniprogram": {"list": [{"name": "pages/myCoupon/myCoupon", "pathName": "pages/myCoupon/myCoupon", "query": "", "launchMode": "default", "scene": null}, {"name": "植树活动预约", "pathName": "booking/pages/new-booking2/new-booking2", "query": "", "launchMode": "default", "scene": null}, {"name": "20周年报告", "pathName": "report/pages/index/index", "query": "", "launchMode": "default", "scene": null}, {"name": "signUp/pages/signUp/signUp", "pathName": "signUp/pages/signUp/signUp", "query": "", "launchMode": "default", "scene": null}, {"name": "hemp/pages/success/success", "pathName": "hemp/pages/success/success", "query": "", "launchMode": "default", "scene": null}, {"name": "hemp/pages/index/index", "pathName": "hemp/pages/index/index", "query": "", "launchMode": "default", "scene": null}, {"name": "hemp/pages/enter/enter", "pathName": "hemp/pages/enter/enter", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/interactiveTask/interactiveTask", "pathName": "pages/interactiveTask/interactiveTask", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/interactiveTask/interactiveTask", "pathName": "pages/interactiveTask/interactiveTask", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/interactiveTask/interactiveTask", "pathName": "pages/interactiveTask/interactiveTask", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/cart/cart", "pathName": "pages/cart/cart", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/custom/custom", "pathName": "pages/custom/custom", "query": "id=184", "launchMode": "default", "scene": null}, {"name": "member/pages/lottery/lottery", "pathName": "member/pages/lottery/lottery", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/guide/guide", "pathName": "pages/guide/guide", "query": "id=2057", "launchMode": "default", "scene": null}, {"name": "注册页回来", "pathName": "member/pages/task/task", "query": "page=popup&from=register", "launchMode": "default", "scene": null}, {"name": "注销页", "pathName": "pages/writeOffForm/writeOffForm", "query": "id=134", "launchMode": "default", "scene": null}, {"name": "dress/pages/index/index", "pathName": "dress/pages/index/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "member/pages/transferPage/transferPage", "query": "", "launchMode": "default", "scene": null}, {"name": "member/pages/gift/gift", "pathName": "member/pages/gift/gift", "query": "", "launchMode": "default", "scene": null}, {"name": "会员节-动效开始页", "pathName": "member/pages/task/task", "query": "page=start", "launchMode": "default", "scene": null}, {"name": "会员节-任务列表", "pathName": "member/pages/task/task", "query": "", "launchMode": "default", "scene": null}, {"name": "会员节-中转页", "pathName": "member/pages/transferPage/transferPage", "query": "", "launchMode": "default", "scene": null}, {"name": "会员节-抽奖页", "pathName": "member/pages/task/task", "query": "page=lottery", "launchMode": "default", "scene": null}, {"name": "积分礼赠", "pathName": "member/pages/pointGift/pointGift", "query": "", "launchMode": "default", "scene": null}, {"name": "会员落地页", "pathName": "member/pages/lottery/lottery", "query": "", "launchMode": "default", "scene": null}, {"name": "member/pages/booking/booking", "pathName": "member/pages/booking/booking", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/interactiveTask/interactiveTask", "pathName": "pages/interactiveTask/interactiveTask", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/myExchange/myExchange", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/myExchange/myExchange", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/life/life", "pathName": "pages/life/life", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/register/register", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/register/register", "pathName": "pages/register/register", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/exchange/exchange", "pathName": "pages/exchange/exchange", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/vip/index", "pathName": "pages/vip/index", "query": "", "launchMode": "default", "scene": null}]}}, "setting": {"urlCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": ["sass"]}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}}