// pages/expirePoint/expirePoint.js
import {
  getRecentExpirePoint,
  getHistoryExpirePoint,
} from '../../api/index'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: [{
      label: '即将过期积分',
      value: 1,
    }, {
      label: '已过期积分',
      value: 2,
    }],
    currentTab: 1,
    currentList: [],
    pageNum: 1,
    pageSize: 10,
    hasNextPage: false,
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      loading: true
    })
    const {
      currentTab
    } = this.data
    this.getList({
      type: currentTab,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  onChangeTab(e) {
    const {
      name
    } = e.detail;
    const {
      currentTab
    } = this.data
    if (name !== currentTab) {
      this.setData({
        currentTab: name,
        pageNum: 1,
        scrollTop: 0,
        currentList: [],
        loading: true,
      })
      this.getList()
    }
  },
  async getList() {
    const {
      pageNum,
      pageSize,
      currentList,
      currentTab,
    } = this.data
    let res;
    switch (currentTab) {
      case 1:
        res = await getRecentExpirePoint({
          pageNum,
          pageSize,
        });
        break;
      case 2:
        res = await getHistoryExpirePoint({
          pageNum,
          pageSize,
        });
        break;
    }
    this.setData({
      currentList: pageNum === 1 ? res.data.list : currentList.concat(res.data.list),
      hasNextPage: res.data.hasNextPage,
      loading: false
    })
  },
  onReachBottom() {
    const {
      hasNextPage,
      pageNum
    } = this.data;
    const nextPage = pageNum + 1;
    if (hasNextPage) {
      this.setData({
        pageNum: nextPage
      })
      this.getList()
    }
  },
})
