@import "assets/scss/common";
@import "assets/scss/config";

.bgStyle {
  width: 100%;
  height: 100%;
}

.linkStyle {
  width: 100%;
  height: 100%;
}

.video {
  position: relative;

  &-content {
    position: absolute;
    overflow: hidden;
  }

  &-img {
    display: block;
  }

  &-link {
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
    pointer-events: none;
  }

  &-play {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 30rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-muted {
    position: absolute;
    right: 0;
    bottom: 0;
    padding: 30rpx;
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-icon {
    width: 48rpx;
    height: 48rpx;
  }
}