<van-popup show="{{ isShow }}" bind:close="onClose" position="bottom" custom-style="min-height: 20%; max-height: 80%" closeable>
  <view class="category-content">
    <view class="title">品类筛选</view>
    <view class="category-list">
      <view class="category-item" wx:for="{{categoryList}}" wx:key="value">
        <view class="icon">
          <image src="{{item.image}}" />
        </view>
        <view class="label">{{item.name}}</view>
        <view class="select-btn {{item.selected && 'active'}}" bindtap="changeCategory" data-value="{{item.id}}">
          <text class="_icon iconfont icon-Mark" wx:if="{{item.selected}}"></text>
        </view>
      </view>
    </view>
    <view class="btn-box">
      <view class="btn-item" bindtap="clearAll">清空</view>
      <view class="btn-item" bindtap="confirm">确认</view>
    </view>
  </view>
</van-popup>
