import {
  throttle
} from '../../../../utils/util';
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    }
  },
  lifetimes: {
    attached() {
      this.setUserInfo()
    },
  },
  pageLifetimes: {
    show() {
      this.setUserInfo()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    emailTxt: '<EMAIL>',
    phoneTxt: '4009209299',
    userInfo: {},
    customField: ""
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 跳转到客服H5页面
    goCustomService: app.debounce(async function () {
      wx.$mp.navigateTo({
        url: '/pages/customService/customService'
      })
    }),
    setUserInfo() {
      this.setData({
        userInfo: app.globalData.userInfo,
        customField: JSON.stringify({ // 联系客服传输的自定义字段
          nickname: app.globalData.userInfo.username || '',
          mobilePhone: app.globalData.userInfo.mobile || '',
          // male:男 female：⼥ unknown：保密
          gender: ['unknow', 'male', 'female'][app.globalData.userInfo.gender] || '',
          id: app.globalData.userInfo.id,
          cardLevelName: app.globalData.userInfo.cardLevelName || '',
          cardLevel: app.globalData.userInfo.cardLevel || '',
          cardNo: app.globalData.userInfo.cardNo || '',
          unionid: app.globalData.userInfo.unionid || '',
          openid: app.globalData.userInfo.openid || '',
        })
      })
    },
    onClose() {
      this.triggerEvent('close')
    },
    copyTxt(e) {
      const {
        copy
      } = e.currentTarget.dataset;
      wx.setClipboardData({
        data: copy,
      })
    },
    // 拨打电话
    callTel: throttle(async function (e) {
      const {
        num
      } = e.currentTarget.dataset;
      wx.makePhoneCall({
        phoneNumber: num,
        success: function () {
          console.log("拨打电话成功！")
        },
        fail: function () {
          console.log("拨打电话失败！")
        }
      })
    }),
  }
})
