<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>手机号绑定</text>
      </view>
    </custom-header>

    <scroll-view class="register" scroll-y enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}">
      <view style="margin-top: {{ menuButtonBottom }}px" style="position: relative">
        <view class="register-form">
          <view class="avatar-item">
            <button class="avatar-box">
              <image class="avatar-img" wx:if="{{info.avatar}}" src="{{info.avatar}}" />
              <image class="avatar-img" wx:else src="{{$cdn}}/default-avatar.png" />
              <!-- 照相机icon -->
              <image class="auth-avatar" src="{{$cdn}}/Photos.png" />
            </button>
            <view class="avatar-desc">
              <text style="padding-right: 20rpx">绑定会员</text><text>尊享权限</text>
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              昵称
            </view>
            <view class="item-content">
              <input type="nickname" class="item-input" placeholder="请输入昵称" value="{{ info.username }}" maxlength="20"
                disabled />
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              性别
            </view>
            <view class="item-content">
              <view class="radio-group">
                <view class="radio-item" wx:for="{{genderList}}" wx:key="value" data-gender="{{item.value}}">
                  <view wx:if="{{info.gender !== item.value}}" class="iconfont icon-danxuan-weigouxuan radio" />
                  <view wx:else class="iconfont icon-xuanzhong radio"></view>
                  <view>{{item.label}}</view>
                </view>
              </view>
            </view>
          </view>
          <view class="form-item">
            <view class="item-label">
              手机号*
            </view>
            <button class="auth-phone" open-type="getPhoneNumber" bindgetphonenumber="authMobile"
              style="margin-top: 0rpx">
              <view class="item-text absolute-box">
                <text class="{{info.mobile ?'item-value' : 'without-data'}} ">{{info.mobile
                  ||'获取微信手机号'}}</text>
              </view>
              <view class="item-button">
                <view class="auth-btn">授权手机号</view>
              </view>
            </button>
          </view>
          <view class="form-item">
            <view class="item-label">
              生日（填写后不可更改）
            </view>
            <picker value="1980-01-01" end="{{ endDate }}" mode="date" disabled>
              <view class="item-content flex-center">
                <view class="item-text">
                  <text class="disabled without-data">{{info.birthday || '生日月赠送惊喜生日礼哦'}}</text>
                </view>
              </view>
            </picker>
          </view>
          <view class="form-item">
            <view class="item-label">
              所在地
            </view>
            <picker mode="region" disabled>
              <view class="item-content flex-center">
                <view class="item-text">
                  <text class="without-data disabled">
                    <text>{{info.province ? info.province + ' ' + info.city + ' ' + info.area :
                      '请选择您的所在地'}}</text>
                  </text>
                </view>
              </view>
            </picker>
          </view>
        </view>
      </view>
    </scroll-view>
    <view class="bottom-box">
      <basic-button width="{{670}}" loading="{{loading}}" size="maxlarge" bind:click="bindPhone">
        完成绑定
      </basic-button>
    </view>
  </view>

  <!-- 加入会员信息异常 -->
  <my-popup show="{{error}}" borderRadius="{{0}}" isCenter="{{false}}" closeable="{{false}}" title="加入会员信息异常"
    confirmText="我知道了" content="{{errContent}}" bindclose="close" bindconfirm="close" bindcancel="close">
  </my-popup>
</my-page>