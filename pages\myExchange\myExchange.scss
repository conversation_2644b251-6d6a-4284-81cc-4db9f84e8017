/* pages/myExchange/myExchange.wxss */
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .nav-wrap {
    height: 90rpx;
  }


  .tab-item {
    font-size: 32rpx;
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #3C3C43;
    font-style: normal;
    line-height: 90rpx;
    position: relative;
  }

  .list-box {
    overflow: hidden;
    //flex: 1;
    position: relative;
    width: 100%;

    .go-btn {
      position: relative;
      text-align: center;
      margin-top: 40rpx;
    }
  }

  .page-content {
    flex: 1;
    height: 0;
    width: 100%;

    .van-tabs__line {
      border-radius: 0 !important;
    }

    .extra {
      //height: env(safe-area-inset-bottom);
      height: 40rpx;
    }
  }
}
