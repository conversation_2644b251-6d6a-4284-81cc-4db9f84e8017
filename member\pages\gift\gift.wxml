<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <scroll-view class="page" scroll-y bindscroll="scroll" bindscrolltolower="onReachBottom">
    <view>
      <image class="page-bg" src="{{$cdn}}/firendShip/giftBg1.png" />
      <view class="page-box">
        <view class="page-box-item" wx:for="{{list}}" wx:for-index="giftKey" wx:key="giftKey" wx:for-item="gift">
          <image class="page-box-bg"
            src="{{$cdn}}/firendShip/{{(gift.couponState === null||gift.couponState === 1)?'':'un'}}giftCard.png" />
          <view class="page-box-content">
            <!-- <view class="page-box-img">
							<image wx:if="{{gift.imageUrl}}" src="{{gift.imageUrl}}"  />
						</view>
            <view class="page-box-title">{{gift.prizesName}}</view> -->
            <view class="page-box-gift-img">
              <image wx:if="{{gift.imageUrl}}" src="{{gift.imageUrl}}" />
            </view>
            <view class="page-box-subTitle" wx:if="{{gift.prizesType === 1}}">请前往线下门店核销</view>
            <view class="page-box-subTitle" wx:else><text
                class="page-box-subTitle-time">至 {{gift.expireTimeA}} </text>{{gift.expireTimeA ? '可用':''}}</view>
            <!-- <view class="page-box-subTitle" >{{gift.description || '  '}}前可用</view> -->
            <view class="page-box-btn" data-item="{{gift}}" wx:if="{{(gift.prizesType === 1 )}}" bindtap="GoAndUse">
              {{gift.couponStateTxt}}</view>
            <view class="page-box-btn" wx:if="{{(gift.prizesType === 2 || gift.prizesType === 4)}}" data-item="{{gift}}"
              bindtap="GoWriteoff">{{gift.couponStateTxt}}</view>
          </view>
        </view>
        <!-- wx:if="{{list.length === 0}}" -->
        <view class="page-data-no" wx:if="{{list.length === 0 && !loading }}">
          <image class="page-data-no-img" src="{{$cdn}}/firendShip/giftDataNo.png" />
          <view class="page-data-no-text">我的礼券为空</view>
        </view>
      </view>
      <view class="page-opacity" wx:if="{{distance>20}}"></view>
    </view>
  </scroll-view>
  <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed>

    <view slot="content" class="lottery-header-more">
      <image bind:tap="onTapMore" src="{{$cdn}}/firendShip/more.png" class="lottery-header-more-img" />
    </view>
  </custom-header>
</my-page>
