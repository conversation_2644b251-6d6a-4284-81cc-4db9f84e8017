const app = getApp();
import { hempAdd, hempInfo, hempConfig } from "../../api/index";
const dayjs = require("../../../utils/dayjs.min");

Page({
  data: {
    statusBarHeight: app.globalData.statusBarHeight + "px",
    navBarHeight: app.globalData.navBarHeight + "px",
    name: "",
    mobile: "",
    school: "",
    show: true,
  },
  onLoad() {},
  onShow() {},

  // 授权手机号
  authMobile(e) {
    wx.$authMobile(e).then(({ success, data }) => {
      if (success) {
        this.setData({
          mobile: data,
        });
      } else {
        this.setData({
          mobile: null,
        });
      }
    });
  },
  input(e) {
    const { key } = e.currentTarget.dataset;
    const { value } = e.detail;
    this.setData({
      [key]: value,
    });
  },
  // 提交
  submit: app.debounce(async function () {
    let { mobile, name, school, userInfo } = this.data;
    if (!mobile || !name || !school) {
      wx.$mp.showToast({
        title: "请完善信息后提交",
      });
      return;
    }
    this.setData({ loading: true });
    hempAdd({ name, phone: mobile, school, userId: userInfo.id })
      .then((res) => {
        this.setData({ loading: false, status: 1 });
        wx.$mp.showToast({
          title: "提交成功",
        });
        let time = setTimeout(() => {
          clearTimeout(time);
          time = null;
          wx.$mp.redirectTo({
            url: "/hemp/pages/success/success",
          });
        }, 2000);
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  }),
  close() {
    this.setData({ show: false });
  },
  onShareAppMessage() {
    return {
      title: "邀请参与汉麻新生力设计大赛",
      imageUrl: this.data.$cdn + "/hemp/share.png",
      path: "/hemp/pages/index/index",
    };
  },
});
