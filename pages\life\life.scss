@import "assets/scss/config";

.nav-btn {
  width: 217rpx;
  height: 62rpx;
  background: #FAFAFA;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-left: var(--page-margin);

  .btn {
    height: 100%;
    width: 50%;
    text-align: center;
    font-size: 48rpx;
    color: #3A3D45;
  }

  .divided {
    display: block;
    width: 1rpx;
    height: 20rpx;
    background: #3A3D45;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .link {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .link-search {
    border-radius: 100px 0 0 100px;
  }

  .link-cart {
    border-radius: 0 100px 100px 0;

    .icon-Cart {
      position: relative;
      width: 48rpx;
      height: 48rpx;
      font-size: 48rpx;

      .count {
        display: flex;
        align-items: center;
        justify-content: center;

        box-sizing: border-box;
        position: absolute;
        top: -17rpx;
        left: 100%;
        margin-left: 4rpx;
        color: #fff;
        background-color: var(--primary-color);
        border-radius: 100rpx;
        padding: 0 8rpx;
        min-width: 32rpx;
        height: 32rpx;
        line-height: 32rpx;
        font-size: 20rpx;
        text-align: center;
        white-space: nowrap;
      }
    }
  }
}

.page-content {
  box-sizing: border-box;
  padding: 0 var(--page-margin);
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: scroll;
  transition: top 0.5s ease-in-out;

  .no-data-box {
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 33rpx;
    text-align: center;
    font-style: normal;
    width: 100%;
    text-align: center;
    position: absolute;
    top: 40%;
  }


  .list-box {
    flex: 1;
    padding-bottom: 60rpx;


    &::-webkit-scrollbar {
      display: none;
    }
  }

  .product-container {
    position: relative;

    .divided {
      height: 2rpx;
      width: 100%;
      background-color: #eee;
    }
  }


  .total-box {
    height: 33rpx;
    font-family: PingFangSC,
      PingFang SC,
      MUJIFont2020;
    font-weight: 500;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 33rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 30rpx;
    padding-top: 30rpx;
    // border-top: 1rpx solid #eee;
  }

  .product-list {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    flex: 1;
    overflow-y: auto;
    row-gap: 10rpx;
    overflow-x: hidden;
    padding-bottom: 60rpx;

    .product-item {
      width: 330rpx;
      overflow: hidden;
    }
  }
}

.no-reminders {
  height: 42rpx;
  font-family: NotoSansHans,
    NotoSansHans;
  font-weight: 400;
  font-size: 24rpx;
  color: #888888;
  line-height: 42rpx;
  text-align: left;
  font-style: normal;
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin-top: 60rpx;

  .tips-icon {
    width: 30rpx;
    height: 30rpx;
    border: 2rpx solid #3C3C43;
    border-radius: 50%;
    line-height: 30rpx;
    font-size: 18rpx;
    text-align: center;
  }

  .active-tip {
    background-color: #3C3C43;
    color: #fff;
  }
}


.page-content {
  // position: relative;

  .fixed {
    position: fixed;
    z-index: 100000;
    width: calc(100% - 78rpx);
    box-sizing: border-box;
  }

  // .column {
  //   background-color: red;
  // }
}

.right-fixed-wrap {
  position: fixed;
  bottom: px2rpx(430);
  right: px2rpx(33);
  z-index: 50;
  width: px2rpx(114);
  height: px2rpx(114);
  border-radius: 50%;
  overflow: hidden;

  .right-fixed {
    width: 100%;
    height: 100%;
  }
}

.moveIcon {
  position: fixed;
  z-index: 20;
  right: 20rpx;
  height: 800rpx;
  bottom: 280rpx;
  width: 130rpx;
  pointer-events: none;
  display: flex;
  align-items: center;

  &-item {
    width: 130rpx;
    height: 130rpx;
    pointer-events: auto;
  }

  &-img {
    width: 130rpx;
    height: 130rpx;
  }
}
