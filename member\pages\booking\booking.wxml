<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <!-- 没有预约资格 -->
  <view class="page" wx:if="{{false}}">
    <view>
      <image class="page-header" src="{{$cdn}}/firendShip/bookBg1.png" mode="widthFix" />
      <view class="page-noPermission">
        <image class="page-noPermission-icon" src="{{$cdn}}/booking/booking/noInWhitelist.png" mode="widthFix" />
        <view class="page-noPermission-text">暂不符合预约条件</view>
      </view>
    </view>
  </view>
  <!-- 未预约  !resData.alreadyBooked-->
  <view class="page" wx:elif="{{!resData.alreadyBooked}}">
    <view>
      <image class="page-header" src="{{$cdn}}/firendShip/bookBg1.png" />
      <view class="page-content">
        <view class="page-title">MUJI STYLE 会员沙龙</view>
        <!-- <view class="page-subTitle">分享需求，体验新品，与品牌共创理想生活</view> -->

        <!-- <view class="page-tip">邀请对象：金级、银级会员定向邀约（每场50人以内）</view>
        <view class="page-tip">活动地点：MUJI diner</view> -->

        <view class="page-card">
          <view class="page-card-box">
            <!-- expired 如果过期展示 -->
            <!-- wx:for="{{appointmentDates}}" wx:for-index="DateKey" wx:key="DateKey" wx:for-item="Date" -->
            <!-- <view wx:for="{{appointmentDates}}" wx:for-index="DateKey" wx:key="DateKey" wx:for-item="Date">  {{DateItem.id === DateId ?  'active':''}}-->
            <view wx:for="{{slotConfigList}}" wx:for-item="DateItem" wx:for-index="DateItemKey" wx:key="DateItemKey"
              data-item="{{DateItem}}" class="page-card-li {{DateItem.id === DateId ?  'active':''}}" bindtap="GoDateItem"
              style="opacity: {{!DateItem.expired && !DateItem.full ? '1' : '0.5'}};">
              <!-- <image class="page-card-li-img"
                src="{{DateItem.id === DateId ? DateItem.unSessionImg:DateItem.sessionImg}}" mode="" /> -->
              <view class="page-card-title {{DateItem.id === DateId ?  'active':''}}">
                {{DateItem.name === '「服装课堂」' ? '「服装搭配课堂」': DateItem.name }}
              </view>
              <view class="page-card-desc">
                <text>{{DateItem.description}}</text>
              </view>
              <view class="page-card-icon">
                <image style=" width: 36rpx; height: 37rpx;" src="{{$cdn}}/firendShip/{{DateItem.id === DateId ?  'un':''}}position.png" alt="" />
              </view>
              <view class="page-card-muji">
                Open MUJI
              </view>
              <view class="page-card-dress">
                <text wx:if="{{DateItem.name === '「服装课堂」'}}">上海市黄浦区
                  淮海中路755号
                  MUJI無印良品 3F</text>
                <text wx:else >云南省昆明市五华区
                  东风西路11号顺城购物中心西塔
                  MUJI無印良品 2F</text>
              </view>
              <view class="page-card-time-icon">
                <image style=" width: 36rpx; height: 36rpx;" src="{{$cdn}}/firendShip/{{DateItem.id === DateId ?  'un':''}}time.png" alt="" />
              </view>
              <view class="page-card-time">
                {{DateItem.date}} {{DateItem.slot}}
              </view>
            </view>
            <!-- </view> -->


            <!-- <view class="page-card-li"></view> -->
          </view>
        </view>
        <view class="page-card-footer">
          <text>*本次活动仅限预约用户参加，每人仅可预约一场。
            *预约成功后请于活动现场出示本人会员卡号与预约成功页面以供核对身份， 由于活动场地有限，不可携带同伴，敬请谅解。</text>
          <view>

          </view>
        </view>
      </view>
    </view>

    <view class="page-button" style="background:#fff">
      <basic-button width="{{670}}" wx:if="{{showoverBool}}" size="large" catch:tap="submit">确定预约
      </basic-button>
      <view wx:else class="page-button-disabled">活动已结束</view>

    </view>
  </view>
  <!--已预约 -->
  <view class="page" style=" background:#edeae2;" wx:else>
    <view>
      <my-header-top></my-header-top>
      <view class="page-content" style="background: transparent;padding-top:60rpx;">
        <image class="page-bgCard"
          src="{{$cdn}}/firendShip/bookCard{{resData.userAppointment.appointmentName === '「服装课堂」'?'2':'1'}}.png" />
        <view class="page-time">{{resData.userAppointment.appointmentDate}} {{resData.userAppointment.appointmentTime}}
        </view>
        <!-- <view class="page-name">吴秋荻</view> -->
      </view>
    </view>
    <view class="page-button">
      <basic-button width="{{670}}" size="large" catch:tap="saveImg">保存到本地</basic-button>
    </view>
  </view>
  <custom-header background="{{ !resData.alreadyBooked ?'transparent':'#edeae2'}}" type="{{1}}"
    isBackHidden="{{ isBackHidden }}" isFixed />
  <my-picker-view show="{{appointmentDateShow}}" title="请选择日期" index="{{appointmentDateIndex}}"
    range="{{appointmentDates}}" catch:close="setDateVisibleHide" catch:confirm="bindPickerChangeDate" />
  <my-picker-view show="{{appointmentSlotShow}}" title="请选择场次" index="{{appointmentSlotIndex}}"
    range="{{appointmentSlots}}" catch:close="setSlotVisibleHide" catch:confirm="bindPickerChangeSlot" />
  <!-- 生成海报 -->
  <canvas class="page-canvas" type="2d" id="myBookingCanvas"></canvas>
  <!-- ImgStyle="{{'v2'}}" -->
  <share-poster src="{{posterSrc}}" show="{{posterShow}}" bindsuccess="posterSuccess" bindclose="posterClose">
  </share-poster>
</my-page>
