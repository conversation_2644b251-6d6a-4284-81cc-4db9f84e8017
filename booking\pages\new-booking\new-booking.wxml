<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="pageWrap isNotBooked" wx:if="{{resData.alreadyBooked===false}}">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed />
    <image class="mainImage" src="{{$cdn}}/booking/new-booking/mainImage.png?v=1.0.0" mode="widthFix" />
    <block wx:if="{{resData.expired}}">
      <view class="noPermission">
        <image class="icon" src="{{$cdn}}/booking/booking/expired.png" mode="widthFix" />
        <view class="text">活动已结束</view>
      </view>
    </block>
    <block wx:elif="{{!resData.inWhitelist}}">
      <view class="noPermission">
        <image class="icon" src="{{$cdn}}/booking/booking/noInWhitelist.png" mode="widthFix" />
        <view class="text">限特邀会员参与，感谢您的关注</view>
      </view>
    </block>
    <block wx:else>
      <view class="mainContent">
        <view class="title">MUJI STYLE会员沙龙<text class="title-line"></text>服装课堂</view>
        <view class="subtitle">活动内容：与MUJI共同探索服装面料、色彩与搭配的无限可能</view>
        <view class="subtitle">
          <view>活动地点：上海市黄浦区淮海中路755号</view>
          <view>MUJI無印良品3F Open MUJI</view>
        </view>
        <view class="subtitle">活动规则：本次活动仅限收到主办方短信邀约的用户可参与预约 短信转发无效。本次活动共开放20位名额...<text class="subtitle-more" catch:tap="showRules">查看更多</text></view>
        <view class="subtitle subtitle-small">*活动仅限本人参加，受邀会员参与活动可领取伴手礼一份。</view>
        <view class="form">
          <view class="formItem">
            <view class="formSelectWrap" catch:tap="setDateVisibleShow">
              <view>{{formFields.appointmentDate ? appointmentDates[appointmentDateIndex].name : '请选择日期'}}</view>
              <view class="arrow"></view>
            </view>
          </view>
          <view class="formItem">
            <view class="formSelectWrap {{!formFields.appointmentDate?'isDisabled':''}}" catch:tap="setSlotVisibleShow">
              <view>{{formFields.appointmentSlot ? appointmentSlots[appointmentSlotIndex].name : '请选择场次'}}</view>
              <view class="arrow"></view>
            </view>
          </view>
          <!--<view class="formItem">
            <view class="formInputWrap">
              <view class="formInputLabel">姓名*</view>
              <input
                class="formInputValue" placeholder="请输入您的姓名" maxlength="10"
                catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
                data-field-name="name" value="{{formFields.name}}"
              />
            </view>
          </view>
          <view class="formItem">
            <view class="formInputWrap">
              <view class="formInputLabel">手机号码*</view>
              <input
                class="formInputValue" placeholder="请输入您的手机号码" maxlength="11"
                catch:input="onInput" data-form-name="formFields" data-form-fields="{{formFields}}"
                data-field-name="phone" value="{{formFields.phone}}"
              />
            </view>
          </view>-->
        </view>
      </view>
      <view class="resetBtn">
        <basic-button width="{{670}}" size="large" catch:tap="submit">确定预约</basic-button>
      </view>
      <view class="btnDesc" catch:tap="showRules">活动规则</view>
    </block>
  </view>
  <view class="pageWrap isAlreadyBooked" wx:if="{{resData.alreadyBooked===true}}">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" />
    <view class="mainWrap">
      <view class="mainContent">
        <view class="logoWrap">
          <image class="logo" src="{{$cdn}}/booking/new-booking/mainImage.png?v=1.0.0" mode="widthFix" />
        </view>
        <view class="itemWrap">
          <view class="item">
            <view class="title">预约成功</view>
            <view class="subtitle">为了您的体验，建议您于活动开始前10分钟到达。</view>
          </view>
          <view class="item isGrey">
            <view class="title2">2025年服装沙龙展示会</view>
            <view class="subtitle2">上海市黄浦区淮海中路755号 MUJI無印良品 3F Open MUJI</view>
          </view>
          <view class="item isGrey">
            <view class="title3">预约日期</view>
            <view class="subtitle3">{{resData.userAppointment.appointmentDate}} {{resData.userAppointment.appointmentTime}}</view>
            <!--<view class="title3">姓名</view>
            <view class="subtitle3">{{resData.userAppointment.userName}}</view>-->
          </view>
        </view>
      </view>
    </view>
  </view>
  <my-picker-view
    show="{{appointmentDateShow}}" title="请选择日期"
    index="{{appointmentDateIndex}}" range="{{appointmentDates}}"
    catch:close="setDateVisibleHide" catch:confirm="bindPickerChangeDate"
  />
  <my-picker-view
    show="{{appointmentSlotShow}}" title="请选择场次"
    index="{{appointmentSlotIndex}}" range="{{appointmentSlots}}"
    catch:close="setSlotVisibleHide" catch:confirm="bindPickerChangeSlot"
  />
  <booking-active-rules isShow="{{visibleRules}}" bindclose="closeRules" />
</my-page>
