import { addFitting, deleteFitting } from '../../api/index'
import { createImage, goActivityHome, initCanvas, openSetting } from '../../utils/index'

const app = getApp()
Page({
  data: {
    isCheck: false,
    checkId: '',
    loading: false,
    showCamera: false, // 显示相机
    step: 0, // 0-空 1-相机 2-提交

    chooseImage: '', // 用户选择的图片
    drawImage: '', // 绘制完成的图片
    taked: false, // 是否已拍摄

    showShare: false, // 是否显示分享海报

    devicePosition: 'back', // 相机方向
    currentMark: '',
    currentMarkImg: '',
    inputText: '',

    marks: [
      {
        name: 'white',
        sImg: `${wx.$config.ossImg}/dress/camera-mark-white-s.jpg?v=2`,
        lImg: `${wx.$config.ossImg}/dress/camera-mark-white.png?v=2`
      },
      {
        name: 'black',
        sImg: `${wx.$config.ossImg}/dress/camera-mark-black-s.jpg?v=2`,
        lImg: `${wx.$config.ossImg}/dress/camera-mark-black.png?v=2.1`
      }
    ]
  },
  onLoad(options) {
    // 查看
    if (options.id) {
      this.setData({
        step: 2,
        drawImage: decodeURIComponent(options.url),
        isCheck: true,
        checkId: options.id
      })
      return
    }
    this.setData({ step: 1 })
    this.handleWatermarkItemTap({ currentTarget: { dataset: { name: 'white' } } })
    this.authCamera()
    this.loadFont()
  },
  // 初始化相机
  cameraContext: null,
  // 授权相机
  async authCamera() {
    const scope = 'scope.camera'
    const { authSetting } = await wx.getSetting()
    console.log('authSetting: ', authSetting)
    if (!authSetting[scope]) {
      const authorizeRes = await wx.authorize({ scope }).catch(err => {
        if (err.errCode === 104) {
          console.log('用户拒绝隐私协议授权')
          err.privacy = true
        } else if (err.errMsg.includes('deny')) {
          console.log('用户拒绝摄像头位置授权 询问打开设置重新获取')
          err.setting = true
        }
        err.error = true
        return err
      })

      console.log('authorize res', authorizeRes)
      // 用户拒绝授权 询问打开设置重新获取
      if (authorizeRes.setting) {
        const res = await openSetting('您拒绝了授权，无法获取摄像头，是否打开设置重新授权')
        // 用户点击确定 打开设置重新获取
        if (res && res.authSetting[scope]) {
          // 用户点击确定 打开设置重新获取到了摄像头权限
        } else {
          authorizeRes.inquiry = true
          wx.showToast({ title: '获取摄像头权限失败', icon: 'none' })
          return
        }
      } else if (authorizeRes.error) return
    }
    this.setData({ showCamera: true })
  },
  initCameraDone(e) {
    this.cameraContext = wx.createCameraContext()
    console.log('initCameraDone', e)
  },
  handleCameraError(e) {
    console.log('handleCameraError', e)
  },
  // 选择水印
  handleWatermarkItemTap(e) {
    const { name, lImg } = this.data.marks.find(item => item.name === e.currentTarget.dataset.name)
    if (name === this.data.currentMark) return
    this.setData({ currentMark: name, currentMarkImg: lImg })
    // 如果已经生成过图片，则直接重新使用新水印生成
    // if (this.data.taked) {
    //   this.setData({ loading: true })
    //   this.createWatermarkImage().then(img => {
    //     this.setData({ drawImage: img, loading: false })
    //   })
    // }
  },
  // 选择相册
  handleAlbumTap: app.debounce(function () {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: res => {
        this.handleCropImage(res.tempFiles[0].tempFilePath)
      }
    })
  }),
  // 拍摄
  handleTakeTap: app.debounce(function () {
    // 如果相机未授权，则先授权
    if (!this.data.showCamera) return this.authCamera()
    if (!this.cameraContext) return
    this.setData({ loading: true })
    this.cameraContext.takePhoto({
      quality: 'high',
      success: res => {
        this.handleCropImage(res.tempImagePath)
      },
      fail: err => {
        console.log('err: ', err)
        this.setData({ loading: false })
      }
    })
  }),
  handleCropImage(src) {
    wx.cropImage({
      src, // 图片路径
      cropScale: '3:4', // 裁剪比例
      success: res => {
        this.setData({ taked: true, chooseImage: res.tempFilePath, loading: false })
        // this.data.chooseImage = res.tempFilePath
        // this.createWatermarkImage().then(img => {
        //   this.setData({ taked: true, drawImage: img, loading: false })
        // })
      },

      fail: err => {
        console.log('err: ', err)
        this.setData({ loading: false })
      }
    })
  },
  // 切换相机方向
  handleTransTap() {
    this.setData({ devicePosition: this.data.devicePosition === 'back' ? 'front' : 'back' })
  },
  // 重拍
  handleRetakeTap() {
    this.cameraContext = null
    this.setData({ taked: false, chooseImage: '' })
  },

  // 创建带水印的图片
  canvasInfo: {
    canvas: null,
    ctx: null,
    width: 0,
    height: 0
  },
  async createWatermarkImage() {
    if (!this.canvasInfo.canvas) {
      this.canvasInfo = await initCanvas(this, '.draw-canvas')
    }
    const { canvas, ctx, width, height } = this.canvasInfo
    // 计算容器宽高的比例
    const scale = width / height
    ctx.clearRect(0, 0, width, height)

    const image = await createImage(canvas, this.data.chooseImage)
    // 图片尺寸的比例
    const imageScale = image.width / image.height
    console.log('scale: ', scale, imageScale)

    let drawImageWidth = width
    let drawImageHeight = height
    let drawImageX = 0
    let drawImageY = 0
    // 如果图片尺寸比例大于容器尺寸比例，绘制图片的高度为容器高度，宽度为容器高度*图片尺寸比例
    if (imageScale > scale) {
      drawImageWidth = height * imageScale
      drawImageX = (width - drawImageWidth) / 2
    } else if (imageScale < scale) {
      // 如果图片尺寸比例小于容器尺寸比例，绘制图片的宽度为容器宽度，高度为容器宽度/图片尺寸比例
      drawImageHeight = width / imageScale
      drawImageX = 0
      drawImageY = (height - drawImageHeight) / 2
    }
    ctx.drawImage(image, drawImageX, drawImageY, drawImageWidth, drawImageHeight)
    const watermark = await createImage(canvas, this.data.currentMarkImg)
    ctx.drawImage(watermark, 0, 0, width, height)

    const color = this.data.currentMark === 'white' ? '#fff' : '#3c3c43'

    ctx.fillStyle = color
    ctx.textBaseline = 'top'

    ctx.font = '400 26px MUJIFont2020-TOCANVAS'
    ctx.fillText('2025', 550, 884)

    ctx.font = 'bold 26px SourceHanSansCN-TOCANVAS'
    ctx.fillText('汉麻', 625, 884)

    if (this.data.inputText) {
      // ctx.rect(42, 843, 225, 90)
      // ctx.strokeStyle = color
      // ctx.stroke()

      ctx.font = '400 25px MUJIFont2020-TOCANVAS'
      ctx.fillText('MUJI FRIEND', 68, 857)
      ctx.font = 'bold 25px SourceHanSansCN-TOCANVAS'
      ctx.fillText(this.data.inputText, 68, 892)
    }

    const { tempFilePath } = await wx.canvasToTempFilePath({
      canvas,
      destWidth: width * 2,
      destHeight: height * 2
    })
    // await wx.saveImageToPhotosAlbum({ filePath: tempFilePath })
    return tempFilePath
  },

  // 提交图片
  handleSubmitTap: app.debounce(async function () {
    if (!this.data.taked) return
    this.setData({ loading: true })
    const imgUrl = await this.createWatermarkImage()
    // return this.setData({ step: 2, loading: false, drawImage: imgUrl })
    const res = await wx.$uploadFiles({ fileUrl: [imgUrl] })
    console.log(res, 'res 图片上传 返回图片路径')
    try {
      await addFitting({
        imgUrl: res.data[0],
        imgText: this.data.inputText,
        templateId: this.data.currentMark === 'white' ? 1 : 2
      })
      this.setData({ step: 2, loading: false, drawImage: res.data[0] })
    } catch (error) {
      console.log('handleSubmitTap error: ', error)
      this.setData({ loading: false })
    }
  }),

  // 重新编辑
  handleEditTap() {
    // this.handleRetakeTap()
    // this.setData({ step: 1 })
    goActivityHome()
  },
  // 保存并分享
  handleSaveShareTap: app.debounce(function () {
    console.log('handleSaveShareTap')
    this.setData({ showShare: true })
  }),
  // 海报操作成功后(下载图片成功、分享成功)的逻辑
  handleShareSuccess() {
    console.log('分享、下载海报、收藏成功了')
  },
  // 海报关闭
  handleShareClose() {
    this.setData({ showShare: false })
  },
  // 删除照片
  handleDeleteTap() {
    wx.showModal({
      title: '删除照片',
      content: '确定要删除这张照片吗？',
      success: res => {
        if (res.confirm) {
          deleteFitting({ id: this.data.checkId }).then(res => {
            wx.navigateBack()
          })
        }
      }
    })
  },
  handleInputChange(e) {
    const text = e.detail.value.slice(0, 7)
    this.data.inputText = text
    return text
  },
  loadFont() {
    wx.loadFontFace({
      family: 'MUJIFont2020-TOCANVAS',
      scope: ['native'],
      desc: { weight: 400 },
      source: `url('${wx.$config.ossImg}/fonts/MUJIFont2020-Regular.otf')`,
      success: () => {
        console.log('MUJIFont2020-TOCANVAS 加载成功')
      }
    })
    wx.loadFontFace({
      family: 'SourceHanSansCN-TOCANVAS',
      scope: ['native'],
      desc: { weight: 700 },
      source: `url('${wx.$config.ossImg}/fonts/SourceHanSansCN-Bold_0.otf')`,
      success: () => {
        console.log('SourceHanSansCN-TOCANVAS 加载成功')
      }
    })
  },
  onShareAppMessage() {
    return {
      title: '生成你的「自然有生活」同款海报',
      imageUrl: this.data.$cdn + '/dress/share.png',
      path: '/dress/pages/index/index'
    }
  },
})
