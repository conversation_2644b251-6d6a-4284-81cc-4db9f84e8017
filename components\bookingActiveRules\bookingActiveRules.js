import {
  throttle
} from '../../utils/util';
const app = getApp();


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false,
    },
    num: {
      type: Number,
      value: 0,
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
  },
  lifetimes: {
    attached() {
      this.setUserInfo()
    },
  },
  pageLifetimes: {
    show() {
      this.setUserInfo()
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    emailTxt: '<EMAIL>',
    phoneTxt: '4009209299',
    userInfo: {},
    pictureList: [
      {
        img: 'award1.png',
        text: '敏感肌用基础补水化妆水高保湿300ml',
        num: "（50份）"
      },
      {
        img: 'award2.png',
        text: '敏感肌用基础补水乳液高保湿200ml',
        num: "（50份）"
      },
      {
        img: 'award3.png',
        text: '敏感肌用基础补水乳霜50g',
        num: "（50份）"
      },
      {
        img: 'award4.png',
        text: '化妆棉',
        num: "（100份）"
      },
      {
        img: 'award5.png',
        text: '压缩面膜',
        num: "（100份）"
      },
      {
        img: 'award6.png',
        text: '护肤水',
        num: "8折券"
      },
      {
        img: 'award7.png',
        text: '乳液8折券',
        num: ""
      },
      {
        img: 'award8.png',
        text: '乳霜8折券',
        num: ""
      },
      {
        img: 'award9.png',
        text: '10积分',
        num: ""
      },

    ]
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 禁止穿透
    touchmove() {
      return false
    },
    setUserInfo() {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    },
    onClose() {
      this.triggerEvent('close')
    },
    copyTxt(e) {
      const {
        copy
      } = e.currentTarget.dataset;
      wx.setClipboardData({
        data: copy,
      })
    },
    // 拨打电话
    callTel: throttle(async function (e) {
      const {
        num
      } = e.currentTarget.dataset;
      wx.makePhoneCall({
        phoneNumber: num,
        success: function () {
          console.log("拨打电话成功！")
        },
        fail: function () {
          console.log("拨打电话失败！")
        }
      })
    }),
  }
})
