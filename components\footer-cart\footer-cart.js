// components/footer-cart/footer-cart.js
const app = getApp();

import {
  getUserPoint,
  isMemember,
} from '../../api/index.js'

Component({
  options: {
    styleIsolation: "apply-shared"
  },

  /**
   * 组件的属性列表
   */
  properties: {
    detail: {
      type: Object,
    },
    cartCount: {
      type: Number,
    },
    isMember: {
      type: Number,
    }
  },

  pageLifetimes: {
    show: function () {
      // 页面被展示
      this.getPoint();
    },
    hide: function () {
      // 页面被隐藏
    },
    resize: function (size) {
      // 页面尺寸变化
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentPoint: 1,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    async getPoint() {
      const res = await getUserPoint();
      if (res.code === 0) {
        this.setData({
          currentPoint: res.data.pointsNum,
        })
      }
    },

    onTapPop() {
      if (this.properties.detail.popContent && this.hasPopContent(this.properties.detail.popContent)) {
        wx.$mp.getCurrentPage().showOveralModal('jumpData', this.properties.detail.popContent)
      }
    },

    hasPopContent(content) {
      // debugger
      // let _content = JSON.parse(content)
      let result = false
      if (content && content.imgUrl) {
        result = true
      }
      return result
    },

    onTapCart() {
      if (app.ifRegister()) {
        this.triggerEvent('tap-cart')
      }
    },

    onTapCartAdd() {
      if (app.ifRegister()) {
        this.triggerEvent('tap-cart-add')
      }
    },

    onTapCartExchange() {
      if (app.ifRegister()) {
        this.triggerEvent('tap-cart-exchange')
      }
    }
  }
})
