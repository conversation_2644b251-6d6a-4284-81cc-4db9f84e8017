<my-page loading="{{loading}}">
  <view class="page-content">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>{{ '选择城市' }}</text>
      </view>
    </custom-header>
    <view id="searchBar">
      <view class="search-box">
        <basic-search placeholder="请输入城市名称" clearable="{{true}}" bind:confirm="handleSearchCity" bind:change="handleChange" />
      </view>
    </view>
    <scroll-view class="list-container" scroll-y="true" enhanced="{{true}}" show-scrollbar="{{ false }}" bounces="{{false}}" style="height: calc(100vh -  {{ navHeight + searchBarHeight }}px - {{ isIPX ? '140rpx' : '120rpx' }})" scroll-top="{{scrollTop}}" bindscroll="onScroll">
      <block wx:if="{{userLocation}}">
        <view class="list-block current-city" bind:tap="handleChangeCity">
          <view class="title">
            当前城市
          </view>
          <view class="name">
            <view class="iconfont icon-Place1" />
            {{userLocation}}
          </view>
        </view>
      </block>
      <block wx:else>
        <view class="unauthorized-box">
          <unauthorized bindShowAuthorization="openLocation" />
        </view>
      </block>
      <view class="divided" />
      <view class="list-block indexbar-box">
        <view class="title" id="content">选择城市</view>
        <indexbar-list id="indexBar" cityList="{{cityList}}" currentCity="{{currentCity}}" bind:changeCity="changeCity" type="{{searchType}}" currentSearchCities="{{currentSearchCities}}" bind:changeScroll="changeScroll" scrollTop="{{scrollTop}}" currentScrollTop="{{ currentScrollTop}}" />
      </view>
    </scroll-view>
  </view>
</my-page>