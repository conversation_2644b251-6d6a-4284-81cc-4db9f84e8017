const app = getApp()
import {
  storeTypeList
} from '../../../../utils/contants';
const dropList = Object.keys(storeTypeList).map((item) => ({
  label: storeTypeList[item],
  value: item,
}))

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    activeValue: {
      type: Number,
      value: 1,
    },
    activeType: {
      type: String,
      value: 'all',
      observer(val) {
        this.handleType(val);
      }
    },
    maskPosition: {
      type: Number,
      value: 0
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    toggleBtn: [{
      label: "门店列表",
      value: 1
    }, {
      label: '地图查询',
      value: 2,
    }],
    dropdownList: [{
      label: '全部',
      value: 'all',
    }, ...dropList],
    activeDropDownLabel: '',
    activeDropdown: 'all',
    showDropdown: false,
  },

  attached() {
    const {
      activeType,
      dropdownList
    } = this.data;
    this.setData({
      activeDropDownLabel: dropdownList.find(item => item.value === activeType).label,
    });
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //  切换门店列表或地图查询
    toggleSearchType(e) {
      const {
        value
      } = e.currentTarget.dataset;
      this.setData({
        activeValue: value,
        showDropdown: false,
      })
      this.triggerEvent('changeTab', {
        activeTab: value
      });
    },
    handleDropdown() {
      const {
        showDropdown
      } = this.data;

      this.setData({
        showDropdown: !showDropdown,
      })
    },
    selectDropdownItem(e) {
      const {
        dropdownList
      } = this.data;
      const {
        value
      } = e.currentTarget.dataset;
      this.setData({
        activeDropdown: value,
        activeDropDownLabel: dropdownList.find(item => item.value === value).label,
        showDropdown: false
      });
      this.triggerEvent('filter', {
        type: value
      });
    },
    handleType(val) {
      console.log('当前type', val);
    }
  }
})
