<view class="slider" style="width:{{width*rpx}}px">
  <block wx:if="{{show}}">
    <view class="slider-bg">
      <!-- 背景 -->
      <custom-bg bgSetting="{{data}}" class="bgStyle"></custom-bg>
    </view>
  </block>
  <view class="slider-content" style="padding-left:{{data.paddingLeft*rpx}}px;padding-right:{{data.paddingRight*rpx}}px;padding-top:{{data.paddingTop*rpx}}px;padding-bottom:{{data.paddingBottom*rpx}}px;">
    <!-- 标题 -->
    <view class="slider-subject" wx:if="{{data.subject.open}}">
      <text-setting data="{{data.subject}}" content="{{data.subject.content}}" class="textStyle">
      </text-setting>
    </view>
    <view class="slider-scroll">
      <custom-link data="{{data.list[current].imgLinks[0]}}" class="linkStyle" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare">
        <!-- 轮播 -->
        <view class="slider-roll" wx:if="{{data.list.length>1}}">
          <view class="slider-inner" style="height:{{data.height*rpx}}px">
            <swiper style="height:{{data.height*rpx}}px" indicator-dots="{{false}}" duration="{{data.durationTime}}" interval="{{data.autoplay?delayTime:0}}" autoplay="{{!!data.autoplay}}" bindchange="changeImg">
              <block wx:for="{{data.list}}" wx:key="index">
                <swiper-item>
                  <image src="{{item.imgUrl}}" class="slider-image" style="border-radius:{{data.borderRadius*rpx}}px;height:{{data.height*rpx}}px " />
                </swiper-item>
              </block>
            </swiper>
            <!-- 里面面指示点  -->
            <view class="slider-point {{['','left','center','right'][data.pointHorizonal]}} " style="top:{{data.pointVertical==1?(data.pointOutSpace*rpx+'px'):'auto'}};bottom:{{data.pointVertical==2?(data.pointOutSpace*rpx+'px'):'auto'}};--left:{{data.pointSpace*rpx / 2}}px;--right:{{data.pointSpace*rpx / 2}}px;" wx:if="{{data.pointOpen&&data.pointPostion==1}}">
              <view class="slider-point-item {{current==index?'active':''}}" style="background:{{data.pointUnSelectColor}};margin:0 {{data.pointSpace*rpx / 2}}px;width:{{data.pointSize*rpx}}px;height:{{data.pointSize*rpx}}px;--selected:{{data.pointSelectColor}};" wx:for="{{data.list}}" wx:key="index"></view>
            </view>
            <!-- 外面指示点 -->
            <view class="slider-point {{['','left','center','right'][data.pointHorizonal]}} " style="top:{{data.pointVertical==1?(-data.pointOutSpace*rpx-data.pointSize*rpx+'px'):'auto'}};bottom:{{data.pointVertical==2?(-data.pointOutSpace*rpx-data.pointSize*rpx+'px'):'auto'}};--left:{{-data.pointSpace*rpx / 2}}px;--right:{{-data.pointSpace*rpx / 2}}px;" wx:if="{{data.pointOpen&&data.pointPostion==2}}">
              <view class="slider-point-item {{current==index?'active':''}}" style="background:{{data.pointUnSelectColor}};margin:0 {{data.pointSpace*rpx / 2}}px;width:{{data.pointSize*rpx}}px;height:{{data.pointSize*rpx}}px;--selected:{{data.pointSelectColor}};" wx:for="{{data.list}}" wx:key="index"></view>
            </view>
          </view>
        </view>
        <view class="slider-roll" wx:else>
          <view class="slider-inner" style="height:{{data.height*rpx}}px">
            <image src="{{data.list[0].imgUrl}}" class="slider-image" style="border-radius:{{data.borderRadius*rpx}}px;height:{{data.height*rpx}}px " />
          </view>
        </view>
        <!-- 主标题 -->
        <view class="slider-main" wx:if="{{data.mainTitle.open}}">
          <text-setting data="{{data.mainTitle}}" content="{{data.list[current].mainTitle}}" class="textStyle">
          </text-setting>
        </view>
        <!-- 副标题 -->
        <view class="slider-small" wx:if="{{data.smallTitle.open}}">
          <text-setting data="{{data.smallTitle}}" content="{{data.list[current].smallTitle}}" class="textStyle">
          </text-setting>
        </view>
      </custom-link>
    </view>
    <!-- 滑动指示 -->
    <view class="slider-show" wx:if="{{data.slideShow}}">
      <image class="slider-show-arrow" src="{{$cdn}}/arrow-left.png"></image>
      <view class="slider-show-text">滑动查看</view>
      <image class="slider-show-arrow right" src="{{$cdn}}/arrow-right.png"></image>

    </view>

    <!-- 悬浮窗 -->
    <custom-float data="{{data.floatSetting}}" wx:if="{{data.floatSetting}}" width="{{750}}" class="floatStyle" bindgoAchor="goAchor" bindgoTop="goTop" bindgoModal="goModal" bindgoShare="goShare"></custom-float>
  </view>
  <!-- 开启了游客模式 并且现在是游客 -->
  <view class="slider-visitor" wx:if="{{visitor&&data.visitor}}" catchtap="showPrincy"></view>
</view>