<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>注销账户</text>
      </view>
    </custom-header>

    <view class="page-content">
      <!-- 声明 -->
      <view class="page-block">
        <view class="block-title" style="margin-bottom: 10rpx">
          您正在申请注销{{userInfo.mobile}}所绑定的账号，为保障您的权益，我们在此提醒您，您的账号应满足:
        </view>
        <view class="top-list">
          <view class="list-item">
            · 不存在未了结的权利义务、或任何其他因为注销该无印良品MUJI会员账号会产生纠纷或争议的情况;
          </view>
          <view class="list-item">
            · 账号下的资产或权益未结清，包括但不限于里程、积分、优惠券等任何与无印良品MUJI会员账号关联的卡券等，或您自行选择放弃该账号下所有资产或虚拟权益。
          </view>
        </view>
      </view>
      <!-- 用户信息 -->
      <view class="page-block">
        <view class="block-title">
          用户信息
        </view>
        <view class="block-list">
          <view class="list-item">
            当前等级：{{userInfo.cardLevelName}}
          </view>
          <view class="list-item">
            当前剩余积分总数：{{userInfo.points}}
          </view>
        </view>
      </view>
      <view class="divided" />
      <!-- 注销原因 -->
      <view class="page-block">
        <view class="block-title">注销原因</view>
        <view class="check-box">
          <view class="check-item" wx:for="{{ reasons}}" wx:key="id" bind:tap="hanldeCheck" data-value="{{item.value}}">
            <view class="check-label">
              {{item.label}}
            </view>
            <view class="check-radio {{item.selected && 'active'}}">
              <view wx:if="{{item.selected}}">
                <view class="iconfont icon-Mark"></view>
              </view>
            </view>
          </view>
          <textarea wx:if="{{showOtherTextArea}}" class="reason-txt" value="{{logOffDesc}}" placeholder="请在此输入" placeholder-style=" font-size: 24rpx;color: #BBBBBB; line-height: 40rpx;" maxlength="{{45}}" bindinput="handleChangeTxt">
              <view class="size-tips">{{logOffDesc.length}}/45</view>
          </textarea>


        </view>
      </view>
      <view class="divided" />
      <!-- 验证手机号 -->
      <view class="page-block">
        <view class="block-title">验证手机号</view>
        <view class="block-list">
          <view class="list-item">
            当前号码：{{userInfo.mobile}}
          </view>
          <view class="list-item code-box">
            <view class="code-input">
              验证码：
              <input class="input-box" placeholder="请输入验证码" value="{{smsCode}}" bindinput="changeCode" />

            </view>
            <view class="code-btn" bindtap="sendCode">
              {{buttonText}}
            </view>
          </view>
        </view>
      </view>
      <view class="divided" />
      <view class="tips">
        若当前号码已经不用或丢弃，您可拨打************客服热线进行咨询
      </view>
      <view class="bottom-box">
        <basic-button disabled="{{!isComplate}}" width="{{670}}" btnState="primary" size="large" bind:click="confirmWirteOff" loading="{{loading}}">
          注销账户
        </basic-button>
      </view>
    </view>
  </view>

</my-page>