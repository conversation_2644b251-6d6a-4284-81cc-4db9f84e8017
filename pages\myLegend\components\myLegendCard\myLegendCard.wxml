<view class="my-credit">
  <view class="credit-box">
    <view class="credit-number">
      <!-- <view class="title-txt">我的里程</view> -->
      <view class="number-text" wx:if="{{showType == 1}}">
        {{numberData||0}}
      </view>
      <view class="number-text" wx:if="{{showType == 2}}">
        {{point.pointsNum ||0 }}
      </view>

    </view>
    <view class="credit-btn">
      <view class="btn-item" wx:for="{{btnList}}" wx:key="value">
        <view class="title-txt algin-right" bindtap="toRlue" data-key="{{item.value}}"
          wx:if="{{ index === 0 && showType == '1' || index === 1 &&  showType == '2'  }}">
          <image src="{{$cdn}}{{item.icon}}" class="txt-point" mode="" />{{item.label}}
        </view>
      </view>
    </view>
  </view>
  <view class="number-tips" bind:tap="gotoExpireList" wx:if="{{showType == 2}}">未来60天有<text
      style="color:#7F0019">{{point.expirePointsNum||0}}</text>积分过期<view class="iconfont icon-View1"
      style="display: inline-block; margin-left: 10rpx;"></view>
  </view>
</view>