import {
  getTemplateById,
} from '../../api/index.js'
const app = getApp();
Page({
  data: {
    loading: false,
    info: {

    }
  },

  async onLoad(options) {
    await this.getInfo()
    let {
      info: {
        pageSetting
      }
    } = this.data
    // 禁止分享
    if (!pageSetting.isShare) {
      wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },
  onShow() {

  },
  // 获取模板数据
  getInfo() {
    this.setData({
      loading: true
    })
    return getTemplateById({
      id: this.data.options.id
    }).then(res => {
      res.data.content = res.data?.content ? JSON.parse(res.data.content) : {}
      res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
      res.data.navSetting = res.data.content.navSetting; // 导航
      res.data.pageSetting = res.data.content.pageSetting; // 页面设置
      res.data.componentSetting = res.data.content.componentSetting; // 组件设置
      // 允许打开 才展示数据
      if (this.ifOpen(res)) {
        this.setData({
          info: res.data
        })
      }
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  // 启动页开启判断
  ifOpen(res) {
    let {
      openFrequency, // 1-每次打开 2-每天一次 3-每几天几次
      openDays,
      openNumer
    } = res.data.pageSetting;
    // 判断开屏频率
    if (openFrequency == 1) {
      return true
    } else if (openFrequency == 2) {
      if (!app.getTodaySub('customOpen' + res.data.id)) {
        app.setTodaySub('customOpen' + res.data.id, 1)
        return true
      }
    } else if (openFrequency == 3) {
      app.setDaysSub('customOpenMore' + res.data.id)
      if (app.getDaysSub('customOpenMore' + res.data.id, openDays, openNumer)) {
        return true
      }
    }
    return false
  },
  // 分享
  goShare(e) {
    let {
      shareTitle,
      shareImg,
      sharePath
    } = e.detail
    this.data.shareInfo = {
      shareTitle,
      shareImg,
      sharePath
    }
  },
  onShareAppMessage() {
    let {
      info: {
        pageSetting
      },
      options
    } = this.data
    // 按钮分享
    if (this.data.shareInfo) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = this.data.shareInfo;
      this.data.shareInfo = ''
      return {
        title: shareTitle || '',
        imageUrl: shareImg || '',
        path: sharePath || ''
      }
    }
    // 页面分享
    return {
      title: pageSetting.shareTitle || '',
      imageUrl: pageSetting.shareImg || '',
      query: {
        id: options.id
      },
      path: pageSetting.sharePath || ''
    }
  }
})
