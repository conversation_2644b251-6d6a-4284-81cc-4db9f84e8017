import {
  addUserClientId,
} from '../../api/index.js'
const app = getApp()
Page({
  data: {
    href: ''
  },
  async onLoad() {
    // "https://ykf-weixin01.7moor.com/wapchat.html?accessId=ad7b7330-69c4-11ef-b17b-35007f3c8afd&fromUrl=&urlTitle=&language=ZHCN&clientId=b6ee74e1b40c61cb200043472c071049&otherParams={"nickName":"MJ25011426154100068783"}"
    let clientId = app.globalData.userInfo.clientId;
    if (!clientId) {
      let res = await addUserClientId()
      clientId = res.data
      app.getUserInfo() // 更新用户信息中的clientId
    }
    let otherParams = JSON.stringify({
      nickName: this.data.userInfo.cardNo || this.data.userInfo.unionid,
      // avatarUrl: this.data.userInfo.avatar || (this.data.$cdn + '/default-avatar.png')
    })
    // 其他参数
    let customField = JSON.stringify({
      username: this.data.userInfo.username,
      cardNo: this.data.userInfo.cardNo,
      cardLevel: this.data.userInfo.cardLevel,
      isMember: this.data.userInfo.isMember,
    })
    let href = `${wx.$config.client}&clientId=${clientId}&otherParams=${otherParams}`
    this.setData({
      href
    })
  }

})
