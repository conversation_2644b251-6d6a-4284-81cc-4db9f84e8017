function uploadFile({
  filePath
}) {
  const header = {
    'Content-Type': 'multipart/form-data',
    token: wx.getStorageSync('token')
  }

  return new Promise(function (resolve) {
    wx.uploadFile({
      url: wx.$config.baseUrl + '/app/basic/file/upload',
      filePath,
      name: 'file',
      method: 'post',
      header,
      success: function (res) {
        console.log('res', res);
        const {
          code,
          data,
          msg
        } = JSON.parse(res.data)
        console.log('code', code);
        console.log('data', data);
        console.log('msg', msg);
        if (code === 0) {
          resolve(({
            data,
            success: true
          }))
        } else {
          // wx.$loading.showErrorMessage(msg)
          resolve({
            success: false
          })
        }
      },
      fail: function () {
        resolve({
          success: false
        })
        // wx.$loading.showErrorMessage('请求失败')
      }
    })
  })
}

export default wx.$uploadFile = uploadFile
