<van-popup style="--popup-background-color: transparent" lock-scroll closeable="{{false}}" zIndex="{{zIndex}}" show="{{ show }}" safe-area-inset-bottom="{{false}}" position="bottom" root-portal="{{true}}" custom-style="background-color: transparent">
  <view class="my-picker-view">
    <view class="my-picker-view-title">{{title}}</view>
    <view class="my-picker-view-content">
      <picker-view immediate-change bind:change="onPickerChange" style="height: 100%" indicator-class="indicator" value="{{_value}}" wx:if="{{_show}}">
        <picker-view-column>
          <view wx:for="{{range}}" wx:key="index" class="my-picker-view-column {{item.disabled?'disabled':''}}">{{ item.name }} </view>
        </picker-view-column>
      </picker-view>
    </view>
    <view class="my-picker-view-btn {{range[_value[0]].disabled?'disabled':''}}" bindtap="confirm">确定</view>
    <view class="my-picker-view-closeBox" bindtap="close">
      <text class="iconfont icon-a-Turnoff" style="color:rgb(96, 96, 96);font-size:50rpx;"></text>
    </view>
  </view>
</van-popup>
