
import { onInput } from "../../../utils/form";
import { membersDayBookingslot_config, membersDayBookingsbook, membersDayBookingsdetails, membersDayBookingsisFull } from "../../api/index";
import { ShareTitle } from '../../utils/lotteryCode'
const dayjs = require('../../../utils/dayjs.min')
const app = getApp();
const getDataDefault = () => {
  return {
    loading: false,
    appointmentDates: [],
    appointmentDateShow: false,
    appointmentDateIndex: 0,
    appointmentDateIndexBak: -99,
    appointmentSlotShow: false,
    appointmentSlots: [],
    appointmentSlotIndex: 0,
    formFields: {
      appointmentDate: "",
      appointmentSlot: "",
      name: "",
      phone: "",
    },
    resData: {},
    isKeepReminds: false,
    posterSrc: '',
    posterShow: false,
    DateId: null,// DateId选中id
    Dateobj: null,
    slotConfigList: [],
    showoverBool: true,//控制是否展示结束按钮
    NoAppointment: true //控制没有预约场次了
  };
};

Page({
  data: getDataDefault(),

  async onLoad() {
    await app.getUserInfo()
    wx.hideShareMenu({
      menus: ['shareAppMessage', 'shareTimeline']
    })
    this.init();

  },
  onShow() {

  },
  async init() {
    this.setData(getDataDefault());
    this.getBookingInfo();
  },
  GoDateItem(e) {
    const { item } = e.currentTarget.dataset
    console.log(item);
    if (item.full && !item.expired) {
      wx.showToast({
        title: '该场次已约满',
        icon: 'none'
      })
    }
    if (!item.full && item.expired) {
      wx.showToast({
        title: '该场次已过期',
        icon: 'none'
      })
    }
    if (!item.full && !item.expired) {
      this.setData({
        Dateobj: item,
        DateId: item.id
      })
    } else if (item.full && item.expired) {
      wx.showToast({
        title: '该场次已过期',
        icon: 'none'
      })
    }

  },
  // 同意隐私协议
  handleNoRemind() {
    const { isKeepReminds } = this.data;
    this.setData({
      isKeepReminds: !isKeepReminds,
    });
  },
  // 跳转隐私协议
  goPrincy: app.debounce(function () {
    wx.$mp.navigateTo({
      url: "/pages/userPrivicy/userPrivicy",
    });
  }),
  // canvas加载图片
  async loadImage(canvas, src) {
    src = src + "?t=" + Date.now()
    console.log('src：', src)
    // const imageInfo = await wx.getImageInfo({ src })
    return new Promise((resolve, reject) => {
      let img1 = canvas.createImage(img1);
      img1.src = src
      img1.onload = function () {
        resolve(img1);
      };
      img1.onerror = function (e) {
        wx.showToast({
          title: "图片加载失败",
          icon: "none",
        });
        console.log("图片加载失败");
        reject();
      };
    });
  },
  // 生成海报图片
  createCanvas() {
    let { posterSrc } = this.data;
    if (posterSrc) {
      this.setData({ posterShow: true })
      return Promise.resolve()
    }
    return new Promise((resolve, reject) => {
      wx.createSelectorQuery()
        .select("#myBookingCanvas")
        .fields({ node: true, size: true })
        .exec(async (res) => {
          let { node: canvas, width, height } = res[0];
          let { $cdn } = this.data;
          let { rpx } = app.globalData;
          console.log('rpx：', rpx)
          const ctx = canvas.getContext("2d");
          ctx.imageSmoothingEnabled = true;
          const scale = 2;
          width = width * scale;
          height = height * scale;
          canvas.width = width;
          canvas.height = height;
          try {
            ctx.clearRect(0, 0, width, height);
            // 以上代码固定
            // 以下内容根据UI实际书写
            ctx.fillStyle = "#EEEAE1";
            ctx.fillRect(0, 0, width, height);
            // 绘制背景
            let appointmentName = this.data.resData.userAppointment.appointmentName
            let img1 = await this.loadImage(canvas, $cdn + `/firendShip/bookCard${appointmentName === '「服装课堂」' ? '2' : '1'}.png`);

            ctx.drawImage(img1, 40 * rpx * scale, 236 * rpx * scale, 670 * rpx * scale, 588 * rpx * scale);

            // 绘制文字
            ctx.textBaseline = "top";
            ctx.textAlign = "left";
            ctx.fillStyle = "#231815";
            ctx.font = "400 " + 24 * rpx * scale + "px MUJIFont2020";
            let appointmentDate = this.data.resData.userAppointment.appointmentDate
            let appointmentTime = this.data.resData.userAppointment.appointmentTime
            ctx.fillText(`${appointmentDate} ${appointmentTime}`, 80 * rpx * scale, 750 * rpx * scale);
            // ctx.font = "700 " + 24 * rpx * scale + "px SourceHanSansCN";
            // ctx.fillText("吴秋荻", 80 * rpx * scale, 818 * rpx * scale);
            // ctx.draw()
          } catch (err) {
            console.log('canvas ctx绘制 err：', err)
            reject()
            return
          }
          console.log('width：', width);
          console.log('height：', height);
          wx.canvasToTempFilePath({
            canvas,
            width: width,
            height: height,
            destWidth: width,
            destHeight: height,
            success: (res) => {
              console.log(res.tempFilePath, 'res.tempFilePathres.tempFilePathres.tempFilePath');

              this.setData({
                posterSrc: res.tempFilePath,
                posterShow: true,
              });
              resolve();
            },
            fail(err) {
              console.log('canvasToTempFilePath err：', err)
              reject();
            },
          });
        });
    });
  },
  // 海报分享成功
  posterSuccess() {
    this.setData({
      posterShow: false
    })
  },
  // 海报取消
  posterClose() {
    this.setData({
      posterShow: false
    })
  },
  // 保存海报
  saveImg() {
    if (this.data.loading) return
    if (this.data.resData) {
      this.setData({ loading: true })
      this.createCanvas().finally(() => {
        this.setData({ loading: false })
      })
    }

  },
  async setDateVisibleShow() {
    this.setData({ appointmentDateShow: true });
  },
  async setDateVisibleHide() {
    this.setData({ appointmentDateShow: false });
  },
  async getBookingDates() {//获取场次问题
    const itemOBj = []
    let showover = null
    let showfull = null
    const res = await membersDayBookingslot_config({ bookingConfigId: 1 });
    if (res.data) {
      res.data.forEach(item => {
        if (item.slotConfigList) {

          itemOBj.push(...item.slotConfigList)
        }
      })
    }
    if (itemOBj) {
      showover = itemOBj.some(item => !item.expired);
      showfull = itemOBj.some(item => !item.full);
    } else {
      showfull = true
      showover = true;
    }
    this.setData({ appointmentDates: res.data, slotConfigList: itemOBj, showoverBool: showover, NoAppointment: showfull });
  },
  async setSlotVisibleShow() {
    this.setData({ appointmentSlotShow: true });
  },
  async setSlotVisibleHide() {
    this.setData({ appointmentSlotShow: false });
  },
  async getBookingSlots(data) {//场次接口弃用
    // const res = await apiBooking.getBookingSlots(data);
    // this.setData({ appointmentSlots: res.data });
  },
  async getBookingInfo() {
    this.setData({ loading: true });
    const res = await membersDayBookingsdetails({ bookingConfigId: 1 }).finally(() => {
      this.setData({ loading: false });
    });
    if (res.code === 0) {
      if (res.data.userAppointment && res.data.userAppointment.appointmentDate) {
        res.data.userAppointment.appointmentDate = res.data.userAppointment.appointmentDate.replace(/\D/g, '/').replace(/\/$/, '');
      }
      this.setData({ resData: res.data, formFields: this.data.formFields });
      if (res.data.alreadyBooked === false) {//判断是否调取
        this.getBookingDates();
      }
    }

  },
  async bindPickerChangeDate(e) {
    console.log("bindPickerChangeDate：", e);
    this.setData({ appointmentDateIndex: e.detail.value });
    if (this.data.appointmentDateIndexBak === this.data.appointmentDateIndex)
      return;
    this.data.appointmentDateIndexBak = this.data.appointmentDateIndex;
    console.log("trigger bindPickerChangeDate：", e);
    const item = this.data.appointmentDates[this.data.appointmentDateIndex];
    const appointmentDate = item.id;
    this.data.formFields.appointmentDate = appointmentDate;
    this.data.formFields.appointmentSlot = "";
    this.setData({ formFields: this.data.formFields, appointmentSlotIndex: 0 });
    await this.getBookingSlots({ appointmentDate });
  },
  bindPickerChangeSlot(e) {
    console.log("bindPickerChangeSlot：", e);
    this.setData({ appointmentSlotIndex: e.detail.value });
    const item = this.data.appointmentSlots[this.data.appointmentSlotIndex];
    this.data.formFields.appointmentSlot = item.id;
    this.setData({ formFields: this.data.formFields });
  },
  onInput,
  async submit() {
    if (!app.ifRegister()) return;

    const {
      Dateobj,
      NoAppointment
    } = this.data;
    if (!NoAppointment) {
      return wx.$mp.showToast({ title: "暂无可预约场次" });
    }
    if (!Dateobj) {
      return wx.$mp.showToast({ title: "请选择场次" });
    }

    this.setData({ loading: true });
    membersDayBookingsisFull({
      bookingConfigId: 1, appointmentDate: this.data.Dateobj.date,
      appointmentSlot: this.data.Dateobj.slot
    }).then(async res => {
      await membersDayBookingsbook({
        bookingConfigId: 1, appointmentDate: this.data.Dateobj.date,
        appointmentSlot: this.data.Dateobj.slot
      }).finally(() => {
        this.setData({ loading: false });
        this.getBookingInfo();
      });
    }).catch((err) => {
      // this.getBookingInfo();
    }).finally(() => {
      // this.getBookingInfo();
      this.setData({ loading: false });
    });


  },

  onShareAppMessage(options) {

    return {
      title: '会员良友节沙龙活动，快来和我一起预约吧',
      imageUrl: this.data.$cdn + '/firendShip/share.png',
      path: '/member/pages/booking/booking'
    }

  }
});
