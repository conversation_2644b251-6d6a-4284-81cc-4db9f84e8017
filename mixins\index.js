export default function () {
  const app = getApp();
  return {
    // data与页面定义字段相同时 以页面字段为主
    data: {
      options: {}, // 页面入参
      $cdn: wx.$config.ossImg, // 图片路径前缀,
      userInfo: app.globalData.userInfo, // 用户信息
      styleSetting: app.globalData.styleSetting, // 全局风格设置
      userLatitude: app.globalData.userLatitude, // 用户经纬度
      userLongitude: app.globalData.userLongitude, // 用户经纬度
      visitor: app.globalData.visitor, // 游客模式
      overallModal: {
        // 全局弹窗
        secret: false, // 隐私协议弹窗
        fail: false, //接口报错同意弹窗
        store: false, // 门店定位弹窗
        subscribe: false, // 订阅消息弹窗
        jumpData: null, // 全局的热区图片跳转组件 jumpData有值就展示 清空就不展示
        yellow: false,
        white: false,
        freeze: false, // 冻结用户
        phone: false, // 注册未绑定用户
        // jumpData: {
        //   imgUrl: '',
        //   imgLinks: []
        // }
      },
    },
    // 先执行混入onLoad(),再执行页面的onLoad
    onLoad(options) {
      // options可以直接使用
      // 埋点
    },
    onShow(options) {
      // options可以直接使用
      // 埋点
    },
    onReady(options) {
      // options可以直接使用
    },
    methods: {
      // 全局弹窗显示
      showOveralModal(key, value) {
        if (value) {
          this.data.overallModal[key] = value;
        } else {
          this.data.overallModal[key] = true;
        }
        this.setData({
          overallModal: {
            ...this.data.overallModal,
          },
        });
      },
      // 全局弹窗隐藏
      hiddenOveralModal(key, callBack) {
        this.data.overallModal[key] = false;
        this.setData({
          overallModal: {
            ...this.data.overallModal,
          },
        });
        console.log(this.data.overallModal);
        // 关闭弹窗后的回调函数
        if (callBack && typeof callBack == "function") {
          callBack();
        }
      },
      // 判断是否注册
      ifRegister() {
        return app.ifRegister();
      },
      // 获取用户信息
      getUserInfo() {
        return app.getUserInfo();
      },
    },
  };
}
