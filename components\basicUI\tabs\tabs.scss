.tabs {
  display: flex;
  position: relative;
  box-sizing: border-box;
  justify-content: space-between;
  padding-bottom: var(--buttom);

  .tabs-item {
    // display: flex;
    // align-items: center;
    // flex: 1;
    // justify-content: center;

    .active {
      color: var(--color) !important;
      font-weight: var(--AfontWeight) !important;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 4rpx;
        background-color: var(--color);
        left: 50%;
        transform: translateX(-50%);
        bottom: var(--Fbuttom);
        z-index: 100;
      }
    }

    .tabs-item-text {
      font-family: PingFangSC, PingFang SC;
      font-weight: var(--fontWeight) ;
      font-size: var(--fontSize);
      color: #BBBBBB;
      line-height:  var(--lineHeight);
      position: relative;
      display: flex;
      align-items: center;
    }
  }

  .tabs-suo {
    margin-right: 10rpx;
    width: 21rpx;
    height: 20rpx;
  }

  .tabs-line {
    width: 100%;
    position: absolute;
    height: 1px;
    background-color: #eee;
    bottom: 0rpx;
  }
}