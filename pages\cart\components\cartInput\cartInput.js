const app = getApp()

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    type: {
      type: String,
      value: ''
    },
    skuInfo: {
      type: Object,
      value: {},
    },
  },
  onLoad(options) {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    onTapCartDel() {
      this.triggerEvent('tap-del', this.properties.skuInfo)
    },

    onTapCartAdd() {
      this.triggerEvent('tap-add', this.properties.skuInfo)
    },

    onInputConfirm(e) {
      const {
        detail: {
          value
        }
      } = e;

      // if (value < 1000) {
      this.triggerEvent('confirm', {
        ...this.properties.skuInfo,
        number: +value
      })
      // } else {
      //   wx.showToast({
      //     title: '最多可购买1000件',
      //     icon: 'none',
      //   });
      // }
    }
  }
})
