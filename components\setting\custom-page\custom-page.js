// components/custom-page/custom-page.js
const app = getApp()
Component({
  properties: {
    info: {
      type: Object,
      value() {
        return {}
      }
    },
    // 游客模式
    visitor:{
      type:Boolean,
    },
    // 页面数据
    pageSetting: {
      type: Object,
      value() {
        return {}
      },
      observer(val) {
        if (val) {
          // 加定时的原因是等待隐私协议的处理
          let time = setTimeout(() => {
            clearTimeout(time)
            time = null
            this.init()
          }, 500);
        }
      }
    },
    // 导航数据
    navSetting: {
      type: Object,
      value() {
        return {}
      }
    },
    // 组件数据
    componentSetting: {
      type: Array,
      value() {
        return []
      },
    },
  },

  data: {
    // 10个弹窗
    modal: [{
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    {
      show: false,
      id: ''
    },
    ],
    isTabBarPage: false,
    scrollTop: 0, // 滚动元素高度
    view: '', // 滚动元素id
    time: null
  },
  attached() {
    console.log('红红火火恍恍惚惚-----------------------------', wx.$mp.isTabBarPage())
    this.setData({
      isTabBarPage: wx.$mp.isTabBarPage()
    })
  },
  pageLifetimes: {
    show() {
      console.log('红红火火恍恍惚惚-----------------------------', wx.$mp.isTabBarPage())
      this.setData({
        isTabBarPage: wx.$mp.isTabBarPage()
      })
    }
  },
  methods: {
    async init() {
      let {
        modalLinks,
        autoModal,
        modalList,
        openModalType,
        pageType, //1-常规页 2-加载页 3-开屏页  4-弹窗
        autoGo, // 是否自动跳转
        autoTime, // 自动跳转时间
        imgLinks, // 自动跳转链接

      } = this.data.pageSetting;
      let {
        id,
        actionLinks
      } = this.data.info;

      // 获取当前页面 更新全局弹窗状态
      var currentInstance = wx.$mp.getCurrentPage();
      const app = getApp()
      let userInfo = app.globalData.userInfo;
      let privacyData = app.globalData.privacyData;
      let needAuthorization = app.globalData.needAuthorization;
      console.log(autoModal)
      // 自动弹窗逻辑 // openModalType 1-每次展示 2-永久1次  3-每天一次
      // 在隐私协议弹窗不需要展示的前提下 才能自动弹窗
      if ((!wx.$config.noShowPrivicy.some(item => item.includes(currentInstance.path))) && ((userInfo?.policyVersion != privacyData?.policyVersion) || needAuthorization)) {
        console.log('不执行自动弹窗1')
      } else if ((!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path)) && (app.globalData.userInfo.isFreeze || (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile)))) { // 冻结或者需要绑定手机号
        console.log('不执行自动弹窗2')
      } else if (autoModal) {
        console.log('执行自动弹窗3')

        // 排序 sort值越小 优先级越大
        modalList.sort((a, b) => a.sort - b.sort)

        for (let i = 0; i < modalList.length; i++) {
          let { id, openModalType, modalLinks } = modalList[i];
          // 有效操作的行为
          let validData = await app.clickHot(modalLinks[0])
          // 有有效的行为数据
          if (validData?.length) {
            if (openModalType == 1) {
              console.log('每次展示')
            } else if (openModalType == 2) {// 永久展示
              if (wx.getStorageSync('autoModal' + id)) { // 已经展示过
                continue
              }
              // 没有展示过
              wx.setStorageSync('autoModal' + id, true)
            } else if (openModalType == 3) { // 每天展示一次
              if (app.getTodaySub('autoModal' + id)) { // 今天弹过
                continue
              }
              // 今天没有弹过
              app.setTodaySub('autoModal' + id, true)
            }
            validData.forEach(data => {
              this.autoModal(data)
            })
            break
          }
        }
      }



      // 在同意了隐私协议弹窗的前提下才 加载页的自动逻辑
      if ((!wx.$config.noShowPrivicy.some(item => item.includes(currentInstance.path))) && ((userInfo?.policyVersion != privacyData?.policyVersion) || needAuthorization)) {
        console.log('不执行自动逻辑')
      } else if ((!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path)) && (app.globalData.userInfo.isFreeze || (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile)))) { // 冻结或者需要绑定手机号
        console.log('不执行自动弹窗')
      } else if (pageType == 2 && actionLinks && actionLinks.length) {
        console.log('执行自动逻辑')
        app.clickHot(actionLinks[0]).then(validData => {
          validData.forEach(data => {
            this.autoGo(data)
          })
        })
      }


      // 在同意了隐私协议弹窗下的自动跳转
      if ((!wx.$config.noShowPrivicy.some(item => item.includes(currentInstance.path))) && ((userInfo?.policyVersion != privacyData?.policyVersion) || needAuthorization)) {
        console.log('不执行自动跳转')
      } else if ((!wx.$config.noShowPhone.some(item => item.includes(currentInstance.path)) && (app.globalData.userInfo.isFreeze || (app.globalData.userInfo.isMember > 0 && !app.globalData.userInfo.mobile)))) { // 冻结或者需要绑定手机号
        console.log('不执行自动跳转')
      } else if (pageType == 3 && autoGo && imgLinks.length) {
        console.log('执行自动跳转')
        clearTimeout(app.globalData.autoGoTimer)
        app.globalData.autoGoTimer = setTimeout(() => {
          clearTimeout(app.globalData.autoGoTimer)
          app.globalData.autoGoTimer = null
          app.clickHot(imgLinks[0]).then(validData => {
            validData.forEach(data => {
              this.autoGo(data)
            })
          })
        }, autoTime || 0);
      }

    },
    // 自动弹窗事件
    autoModal(data) {
      let {
        modalIndex,
        linkUrl
      } = data;
      this.goModal({
        detail: {
          modalIndex, // 弹窗下标
          operateType: 2, //1-关闭  2-打开
          id: linkUrl.split('?id=')[1]
        }
      })
    },
    // 自动跳转
    autoGo(data) {
      if (data.operateType == 9) {
        wx.$mp.navigateBack()
      } else {
        app.goLink(data)
      }
    },
    // 返回顶部
    goTop() {
      this.setData({
        view: 'top',
      })
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.setData({
        view: 'view' + e.detail.anchorIndex
      })
    },
    // 分享
    goShare(e) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = e.detail
      this.triggerEvent('goShare', {
        shareTitle,
        shareImg,
        sharePath
      })
    },
    // 对弹窗的操作
    goModal(e) {
      // operateType 1-关闭  2-打开
      let {
        modalIndex,
        operateType,
        id
      } = e.detail;
      if (operateType == 1) {
        this.data.modal[modalIndex].show = false
      } else {
        this.data.modal[modalIndex].show = true
        this.data.modal[modalIndex].id = id
      }
      this.setData({
        modal: this.data.modal
      })
    },
    // 点击跳过按钮
    goJump() {
      wx.$mp.track({
        event: 'open_jump_click'
      })
    },
    closeModal(e) {
      let {
        index
      } = e.detail;
      this.data.modal[index].show = false;
      this.setData({
        modal: this.data.modal
      })

    },
    scroll(e) {
      this.setData({
        scrollTop: e.detail.scrollTop
      })
      this.triggerEvent('scrollTop', e.detail.scrollTop)
    },
  }
})
