<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-content">
    <view id="outletsTop">
      <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
        <view slot="content" class="page-title">
          <text>附近门店</text>
        </view>
      </custom-header>
      <!-- 搜索 -->
      <search-bar bind:showDialog="handleShowCategory" bind:showCity="handleShowCity" currentCity="{{filters.city}}" bind:filter="handleFilter" />
      <!-- 筛选 -->
      <filter-bar maskPosition="{{outletsTopHight}}" activeValue="{{activeTab}}" bindchangeTab="changeTab" activeType="{{filters.type}}" bindfilter="handleFilter" />
      <!-- 未授权 -->
      <block wx:if="{{!isLocationAuthorized}}">
        <unauthorized bindShowAuthorization="handleShowPostiton" />
      </block>
    </view>
    <!-- 门店列表 -->
    <block wx:if="{{activeTab===1}}">
      <scroll-view style="height: calc(100vh -  {{outletsTopHight}}px)" class="list-box" enhanced="{{true}}" show-scrollbar="{{ false }}" scroll-y="{{true}}" bounces="{{true}}" bindscrolltolower="getMore">
        <block wx:if="{{outletsList.length>0&&outletsList[0].cityTag===1}}">
          <no-store />
        </block>
        <view class="total">门店总计：{{count}}家</view>
        <view class="list-content">
          <block wx:if="{{outletsList.length>0}}">
            <view class="list-item" wx:for="{{outletsList}}" wx:key="id">
              <outlet-card outletData="{{item}}" />
            </view>
          </block>
          <block wx:elif="{{outletsList.length===0&&!loading}}">
            <no-data-available text="抱歉，暂时没有匹配的搜索结果" />
          </block>
        </view>
      </scroll-view>
    </block>
    <block wx:else>
      <view class="map-box">
        <map-box longitude="{{longitude || 116.397499 }}" latitude="{{latitude ||39.908722}}" outletsList="{{outletsList}}" bindchangeLocation="changLocation"></map-box>
      </view>
    </block>
  </view>
  <!-- 服务类别选择弹窗 -->
  <!-- <category-dialog isShow="{{showCategoryDialog}}" bindcloseDialog="handleCloseDialog" bindfilterStore="handleFilter" /> -->
  <!-- 城市列表弹窗 -->
  <!-- <city-list-dialog isShow="{{showCityDialog}}" bindcloseDialog="handleCloseDialog" bindfilter="handleFilter" /> -->
  <!-- <authorization-diaglog title="开启位置未授权" isShow="{{showPositionDialog}}" bindclose="handleClosePostiton">
  <view slot="content">
    请点击开启并在设置页允许“使用我的地理位置”，开启授权后，将为您匹配附近的门店及专属顾问
  </view>
</authorization-diaglog> -->
</my-page>