.product-card {
  width: 330rpx;
  margin-bottom: 40rpx;

  .product-img {
    height: 330rpx;
    width: 330rpx;
    background-color: #fafafa;
    margin-right: 30rpx;
    position: relative;
    z-index: 0;

    .empty {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.6);

      .text {
        display: flex;
        align-items: center;
        justify-content: center;

        border-radius: 100rpx;
        width: 120rpx;
        height: 120rpx;
        background: rgba(0, 0, 0, 0.4);

        font-family: SourceHanSansCN, SourceHanSansCN;
        font-weight: 500;
        font-size: 28rpx;
        color: #FFFFFF;
        line-height: 42rpx;
        text-align: center;
        font-style: normal;
      }
    }

    .image {
      height: 310rpx;
      width: 310rpx;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-color: #fff;
    }
  }

  .product-info {
    position: relative;
    margin-top: 20rpx;
    box-sizing: border-box;
    padding-left: 10rpx;

    .product-title {
      width: 300rpx;
      height: 33rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 33rpx;
      text-align: left;
      font-style: normal;
    }

    .product-price {
      display: flex;
      align-items: center;
      gap: 10rpx;
      position: relative;
      margin-top: 20rpx;

      .plus-icon {
        width: 11rpx;
        height: 25rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 18rpx;
        color: #3C3C43;
        line-height: 25rpx;
        text-align: left;
        font-style: normal;
        // margin-left: 10rpx;
        // margin-right: 10rpx;
      }

      .price-item {
        display: flex;
        color: #3C3C43;
        position: relative;
        font-size: 28rpx;
        height: 36rpx;

        .num {
          height: 34rpx;
          font-family: PingFangSC,
            PingFang SC,
            MUJIFont2020;
          font-weight: 500;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          margin-right: 5rpx;
        }

        .unit {
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 18rpx;
          line-height: 25rpx;
          text-align: left;
          font-style: normal;
          position: relative;
          top: 12rpx;
        }
      }

      .red-font {
        color: #7F0019;
      }

      .small-font {
        font-size: 18rpx;
        height: 28rpx;
      }

      .line {
        color: #3C3C43;
        text-decoration-line: line-through;
      }


    }

  }
}
