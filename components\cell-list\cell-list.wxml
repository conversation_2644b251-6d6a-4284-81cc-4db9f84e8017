<view class="cell-box">
  <block wx:for="{{listData}}" wx:key="index">
    <view class="cell-li-box" data-index="{{index}}" catch:tap="unfold">
      <view class="cell-li">
        <view class="cell-label">{{item.label}}</view>
        <view class="cell-jia">
          <view wx:if="{{!item.isUnfold}}" class="iconfont icon-shouqi" />
          <view wx:else class="iconfont icon-zhankai" />
        </view>
      </view>
      <view class="cell-content" wx:if="{{item.isUnfold}}">
        <view wx:if="{{!item.isImage}}" class="cell-content-title">
          <mp-html class="{{item.className}}" content="{{item.content}}" />
        </view>
        <block wx:else>
          <view class="cell-item" wx:for="{{item.content}}" wx:for-item="subItem" wx:key="index">
            <image src="{{subItem}}" mode="widthFix" />
          </view>
        </block>

      </view>
    </view>

  </block>
</view>
