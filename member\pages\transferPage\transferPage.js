// member/pages/transferPage/transferPage.js

import { lotteryUserGet_prizes, initLotteryNum, lotteryClockIn, lotteryDraw } from '../../api/index'
import { BlindBox, PrizeDraw, BlindBoxPrizeDraw, prizeTypeP1 } from "../../utils/lotteryCode"
const app = getApp()
let timerTo
Page({

  /**
   * 组件的初始数据
   */
  data: {
    loading: true,
  },

  async onShow() {
    // wx.$mp.redirectTo({
    //   // url: "/member/pages/task/task?page=start",
    //   // url: "/member/pages/task/task?page=lottery",
    //   // url: "/member/pages/task/task?page=task",
    //   url: "/member/pages/task/task",
    // });

    this.getLotteryRecord()
  },

  // 获取盲盒抽奖记录
  getLotteryRecord() {
    this.setData({ loading: true })
    const data = {
      pageNum: 1,
      pageSize: 10,
      lotteryCode: BlindBox,
      prizeType: prizeTypeP1
    }
    lotteryUserGet_prizes(data).then(res => {


      console.log(res)

      const hasLottery = res?.data?.list?.length > 0
      if (hasLottery) {
        if (timerTo) {
          clearTimeout(timerTo)
        }
        timerTo = setTimeout(() => {
          this.setData({ loading: false })
          clearTimeout(timerTo)
          wx.$mp.redirectTo({
            url: "/member/pages/lottery/lottery", // 落地页
          });
        }, 1000)

      } else {
        this.setData({ loading: false })
        wx.$mp.redirectTo({
          url: "/member/pages/task/task?page=start", // 动效开始
          // url: "/member/pages/task/task?page=lottery",
          // url: "/member/pages/task/task?page=task",
        });
      }
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
