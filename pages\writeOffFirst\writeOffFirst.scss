/* pages/writeOffFirst/writeOffFirst.wxss */
.page-container {
  box-sizing: border-box;
  position: relative;

  .page-content {
    box-sizing: border-box;
    padding: 25rpx 40rpx 0 40rpx;
    width: 100%;
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 50rpx;
    text-align: left;
    font-style: normal;
    position: relative;

    .row {
      margin-bottom: 40rpx;
    }
  }

  .bottom-box {
    margin-top: 165rpx;

    /* 避免被系统底部遮挡 */
    display: flex;
    justify-content: center;
    justify-content: space-between;
    padding: 0 40rpx;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    padding-bottom: env(safe-area-inset-bottom);
  }

}
