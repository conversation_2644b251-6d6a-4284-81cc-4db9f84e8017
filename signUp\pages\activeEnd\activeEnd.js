// signUp/pages/activeEnd/activeEnd.js
const app = getApp()
import {
  getSubscribeByScene,
} from '../../../api/index.js';
import {
  getCampaignType,
  getEnrollInfo
} from '../../api/index.js'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: app.globalData.statusBarHeight,
    navBarHeight: app.globalData.navBarHeight,
    menuButtonHeight: app.globalData.menuButtonHeight,
    type: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getSubNum()
    this.setData({
      type: options.type
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},
  // 跳转到活动规则页面
  activeRules: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  }),
  // 跳转公示结果页
  activeRules2: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/announcement/announcement',
    })
  }),
  // 查询订阅次数
  getSubNum() {
    getSubscribeByScene({
      scene: "new_campaign"
    }).then(res => {
      console.log(res.data, '订阅信息');
      console.log(this.data.userSignInfo, '用户报名信息');
      let disabled = false
      // 有小于1的  就需要订阅
      disabled = res.data.some(item => item.subNumber < 1)
      console.log(disabled, 'disabled');
      this.setData({
        disabled
      })
    })
  },
  // 获取报名信息数据
  getData() {
    getCampaignType().then(res => {
      // state	活动状态 0未开始 1进行中 2公示期 3已结束
      let data = res.data
      // data.showTime = this.getDate(data.campaignShowTime)
      console.log(data, 'data 活动报名数据');
      // 判断距离结束时间是否还有6天
      let newDate = new Date(data.campaignEndTime); //结束时间
      let day = newDate.getDate();
      newDate.setDate(day - 6) //最后开启打卡时间
      console.log(newDate, '最后打卡时间');
      // 获取当前日期时间
      var currentDate = new Date();
      // 获取待比较的日期
      var compareDate = new Date(newDate);
      if (currentDate > compareDate) { //  还剩6天 完不成打卡的
      }
      this.setData({
        campaignCode: data.campaignCode,
        signUpInfo: data,
        sendSmsTime: data.sendSmsTime
      })
    })
    // 查询报名信息
    getEnrollInfo({
      campaignCode: this.data.campaignCode
    }).then(({
      data
    }) => {
      this.setData({
        userSignInfo: data
      })
    })
  },
  click: app.debounce(async function (e) {
    wx.$mp.track({
      event: 'recruit_failed2_new_Subscription_click',
    })
    let that = this
    if (app.ifRegister()) {
      app.subscribe2("new_campaign").then((res) => {
        setTimeout(() => {
          that.getSubNum()
        }, 3000)
        let templateIds = res?.templateIds || []
        if (templateIds && templateIds.length > 0) {
          let subscribe = ""
          subscribe = templateIds.some((item) => res[item] == "accept")
          if (subscribe) {
            wx.showToast({
              title: '订阅成功',
              icon: 'none',
            });

          } else {
            wx.showToast({
              title: '拒绝订阅',
              icon: 'none',
            });
          }
        }

        // wx.showToast({
        //   title: '订阅成功',
        //   icon: 'none',
        // });
      })
    }
  }),
  // 用户点击拒绝授权 展开提示在某个地方开启授权弹框
  openGuide() {
    this.setData({
      showContact1: true
    })
  },
  closeGuide() {
    this.setData({
      showContact1: false
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },



  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
