<view class="card-cell-content" wx:if="{{showType ===0 }}">
  <!-- <block wx:for="{{list}}"> -->
  <!--changeType	变动类型 1增加 2扣减-->
  <view class="cell-item">
    <view class="cell-title">{{item.changeType==1?'获得原因':'扣除原因'}}</view>
    <view class="cell-value">{{item.obtainDesc}}</view>
  </view>
  <view class="cell-item" wx:if="{{item.consumptionTime}}">
    <view class="cell-title">{{list[1]}}</view>
    <view class="cell-value">{{item.consumptionTime}}</view>
  </view>
  <view class="cell-item" wx:if="{{item.consumptionAmount}}">
    <view class="cell-title">{{list[2]}}</view>
    <view class="cell-value">￥{{item.consumptionAmount}}</view>
  </view>
  <view class="cell-item" wx:if="{{item.cumulativeAmount}}">
    <view class="cell-title">{{list[3]}}</view>
    <view class="cell-value">￥{{item.cumulativeAmount}}</view>
  </view>
  <view class="cell-item" wx:if="{{item.channelStore}}">
    <view class="cell-title">{{list[4]}}</view>
    <view class="cell-value">{{item.channelStore}}</view>
  </view>
  <!-- </block> -->

</view>

<view class="card-cell-content" wx:if="{{showType === 1 }}">
  <!-- <block wx:for="{{list}}"> -->
  <view class="cell-item">
    <!--changeType	变动类型 1增加 2扣减-->
    <view class="cell-title">{{item.changeType==1?'获得原因':'扣除原因'}}</view>
    <view class="cell-value">{{item.obtainDesc}}</view>
  </view>

  <view class="cell-item" wx:if="{{item.consumptionTime}}">
    <view class="cell-title">{{list[1]}}</view>
    <view class="cell-value">{{item.consumptionTime}}</view>
  </view>

  <view class="cell-item" wx:if="{{item.consumptionAmount}}">
    <view class="cell-title">{{list[2]}}</view>
    <view class="cell-value">￥{{item.consumptionAmount}}</view>
  </view>
  <view class="cell-item" wx:if="{{item.cumulativeAmount}}">
    <view class="cell-title">{{list[3]}}</view>
    <view class="cell-value">￥{{item.cumulativeAmount}}</view>
  </view>
  <!-- 渠道门店 -->
  <!-- <view class="cell-item" wx:if="{{item.channelStore}}">
    <view class="cell-title">{{list[4]}}</view>
    <view class="cell-value">{{item.channelStore}}</view>
  </view> -->
  <!-- </block> -->

</view>