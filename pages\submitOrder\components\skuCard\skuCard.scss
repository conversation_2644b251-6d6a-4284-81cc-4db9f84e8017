.card-wrapper {
  padding: 20rpx 0;
}

.cart-card {
  display: flex;
  //margin-bottom: var(--page-margin);
  height: 260rpx;
  position: relative;

  .cart-img {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 260rpx;
    width: 260rpx;
    background-color: #FAFAFA;
    margin-right: 30rpx;
    position: relative;

    .img {
      display: block;
      width: 244rpx;
      height: 244rpx;
    }

    .new-tag {
      width: 64rpx;
      height: 34rpx;
      background: #7F0019;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 34rpx;
      text-align: center;
      font-style: normal;
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  .cart-info {
    position: relative;
    height: 100%;

    .cart-title {
      height: 110rpx;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .cart-price {
      position: relative;
      margin-bottom: 30rpx;
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .cart-btn {
      position: absolute;
      bottom: 0;
      display: flex;
      align-items: center;
      line-height: 60rpx;

      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;

      .btn-item {
        width: 40%;
        text-align: center;
        font-size: 24rpx;
      }

      .sku-num {
        flex: 1;
        text-align: center;
        font-size: 24rpx;
      }
    }
  }
}
