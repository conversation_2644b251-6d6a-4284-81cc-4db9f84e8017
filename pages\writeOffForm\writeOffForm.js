// pages/wirteOffForm/writeOffForm.js
import {
  writeOffUser,
  sendSmsCode,
} from '../../api/index'
import {
  writeOffReason
} from '../../utils/contants'
import {
  throttle
} from '../../utils/util';
const app = getApp()

// const requiredFields = ['logOffDesc', '  smsCode']

Page({

  /**
   * 页面的初始数据
   */
  data: {
    phone: '',
    userInfo: {},
    reasons: [],
    showOtherTextArea: false,
    logOffDesc: '',
    smsCode: '',
    isComplate: false,
    countState: false,
    countdown: 60,
    buttonText: '发送验证码' // 按钮文本
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.init();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.setData({
      userInfo: app.globalData.userInfo
    })

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  init() {
    const v = Object.keys(writeOffReason).map((item, index) => ({
      id: index,
      label: writeOffReason[item],
      value: item,
      selected: false,
    }))
    this.setData({
      reasons: v,
    })
  },
  hanldeCheck(e) {
    const {
      reasons,
      logOffDesc,
    } = this.data;
    const {
      value
    } = e.currentTarget.dataset;
    const newData = reasons.map(item => {
      const v = item;
      if (v.value === value) {
        v.selected = !item.selected;
      }
      return v;
    });
    console.log('newData', newData);
    wx.$mp.track({
      event: 'write_off_reasons',
      props: {
        reasons: newData.filter(item => item.selected).map(item => item.label)
      }
    })
    const isShow = newData.some(item => item.selected && item.value === "5")
    this.setData({
      reasons: newData,
      showOtherTextArea: isShow,
      logOffDesc: isShow ? logOffDesc : '',
    })
    this.validateForm()
  },
  handleChangeTxt(e) {
    const {
      value
    } = e.detail;
    this.setData({
      logOffDesc: value,
    })
    this.validateForm()
  },
  changeCode(e) {
    const {
      value
    } = e.detail;
    this.setData({
      smsCode: value,
    })
    this.validateForm();
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const {
      reasons,
      logOffDesc,
      smsCode,
    } = this.data;
    const isSmsCodeValid = smsCode.trim() !== "";
    const isReasonValid = reasons.some(reason => reason.selected === true);
    let isLogOffDescValid = true;
    if (reasons.some(reason => reason.value === '5' && reason.selected) && logOffDesc === '') {
      isLogOffDescValid = false;
    }
    this.setData({
      isComplate: isSmsCodeValid && isReasonValid && isLogOffDescValid,
    })
  },
  sendCode: throttle(async function () {
    if (this.data.countState) return;
    this.setData({
      countState: true,
      buttonText: `${this.data.countdown}秒后重发`
    })
    // 启动倒计时
    let countdown = this.data.countdown;
    let interval = setInterval(() => {
      countdown--;
      this.setData({
        countdown,
        buttonText: `${countdown}秒后重发`
      });
      // 当倒计时结束时
      if (countdown <= 0) {
        clearInterval(interval); // 清除定时器
        this.setData({
          countState: false,
          buttonText: '发送验证码', // 恢复按钮文本
          countdown: 60,
        });
      }
    }, 1000); // 每秒更新一次
    const res = await sendSmsCode();
    if (res.code === 0) {
      wx.showToast({
        title: '发送验证码成功',
      })
    }
  }),
  confirmWirteOff: app.debounce(async function () {
    wx.$mp.track({
      event: 'write_off_submit',
    })
    this.setData({
      loading: true
    })
    const {
      logOffDesc,
      smsCode,
      reasons,
    } = this.data;
    const params = {
      smsCode,
    }
    params.logOffType = reasons.filter(item => item.selected).map(item => item.value).join(',');
    if (reasons.some(item => item.value === '5')) {
      params.logOffDesc = logOffDesc;
    } else {
      params.logOffDesc = undefined;
    }
    writeOffUser(params).then(async res => {
      wx.showToast({
        title: '注销成功',
      })
      // 清空所有的数据
      app.clearData()
      let time = setTimeout(function () {
        clearTimeout(time)
        time = null
        wx.reLaunch({
          url: '/pages/index/index'
        })
      }, 500);
    }).catch(() => {
      this.setData({
        loading: false
      })
    })
  })
})
