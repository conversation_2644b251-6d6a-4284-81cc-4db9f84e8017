<page-meta root-background-color="{{styleSetting.pageStyle.pageColor}}"></page-meta>
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <custom-page info="{{info}}" pageSetting="{{info.pageSetting}}" navSetting="{{info.navSetting}}" componentSetting="{{info.componentSetting}}" wx:if="{{info.id}}" bindgoShare="goShare">
    <view class="page-container1">
      <view class="page-content">
        <view class="card-bg" />
        <view class="card-content">
          <view class="card-top" style="background-image: url({{$cdn}}/my/bg.png)">
            <view class="row" style="{{!userInfo.isMember ? 'margin-bottom: 116rpx' : ''}}">
              <view class="logo-box">
                <view class="iconfont icon-MUJIpassport" />
              </view>
            </view>
            <view class="row">
              <view class="col left-col">
                <block wx:if="{{!userInfo.isMember}}">
                  <view class="welcome-txt">
                    您好，未注册
                    <!-- <view class="register-tips">点击注册</view> -->
                  </view>
                  <view class="btn-box">
                    <basic-button data-id="9" bindtap="goRegister" width="{{192}}" btnState="primary" size="small">
                      立即注册
                    </basic-button>
                  </view>
                </block>
                <block wx:else>
                  <view class="item-box" bind:tap="handleGoEdit">
                    <view class="title">昵称</view>
                    <view class="value">{{userInfo.username}}</view>
                  </view>
                  <view class="item-box">
                    <view class="title">等级</view>
                    <view class="value">{{userInfo.cardLevelName}}</view>
                  </view>
                  <view class="item-box" bind:tap="handleGoLegend">
                    <view class="level-line" style="--persent: {{levelPercent}}%"></view>
                    <view wx:if="{{userInfo.cardLevel==='4'}}" class="level-desc">
                      已到达最高等级
                      <view class="iconfont icon-View1" />
                    </view>
                    <view wx:else class="level-desc">
                      距{{userInfo.nextLevelName}}还需{{userInfo.nextMileage}}里程
                      <view class="iconfont icon-View1" />
                    </view>
                  </view>
                </block>
              </view>
              <view class="col right-col">
                <view class="avatar">
                  <image src="{{userInfo.avatar ? userInfo.avatar : $cdn + '/default-avatar.png'}}" mode="" />
                </view>
              </view>
            </view>
          </view>
          <view class="card-bottom">
            <view class="list-box">
              <view class="list-item" data-id="{{item.id}}" data-url="{{item.path}}" bindtap="handleGo" wx:for="{{rightsList}}" wx:key="id" data-track="{{item.trackData}}">
                <view class="icon">
                  <image src="{{$cdn}}/my/{{item.icon}}" mode="" />
                  <view wx:if="{{item.id===7 &&  unreadMsgNum!==0}}" class="dot">
                    {{unreadMsgNum}}
                  </view>
                </view>
                <view class="name">
                  {{item.name}}
                </view>

              </view>
            </view>
          </view>
        </view>

      </view>
      <contact-popup isShow="{{showContact}}" bindclose="closeContact" />
    </view>
  </custom-page>
</my-page>