{"pages": ["pages/index/index", "pages/exchange/exchange", "pages/qrCode/qrCode", "pages/life/life", "pages/more/more", "pages/princyModal/princyModal", "pages/webView/webView", "pages/custom/custom", "pages/productSearch/productSearch", "pages/register/register", "pages/integralDetails/integralDetails", "pages/clause/clause", "pages/cart/cart", "pages/coupon/coupon", "pages/submitOrder/submitOrder", "pages/submitOrderResult/submitOrderResult", "pages/myCode/myCode", "pages/myLegend/myLegend", "pages/myCoupon/myCoupon", "pages/blogList/blogList", "pages/rightsRecord/rightsRecord", "pages/nearbyOutlets/nearbyOutlets", "pages/guide/guide", "pages/interactiveTask/interactiveTask", "pages/myExchange/myExchange", "pages/exchangeDetail/exchangeDetail", "pages/couponDetail/couponDetail", "pages/customPreview/customPreview", "pages/membershipRules/membershipRules", "pages/myMessage/myMessage", "pages/myIntegral/myIntegral", "pages/integralRule/integralRule", "pages/exchangeRecord/exchangeRecord", "pages/historicalGift/historicalGift", "pages/writeOffFirst/writeOffFirst", "pages/writeOffForm/writeOffForm", "pages/purchaseRecord/purchaseRecord", "pages/userAgreement/userAgreement", "pages/userPrivicy/userPrivicy", "pages/open/open", "pages/open1/open1", "pages/campaingOpen/campaingOpen", "pages/purchaseDetail/purchaseDetail", "pages/orderDetail/orderDetail", "pages/myBarCode/myBarCode", "pages/mileageDetails/mileageDetails", "pages/pointsDetails/pointsDetails", "pages/expireTask/expireTask", "pages/exchangeRules/exchangeRules", "pages/citySearch/citySearch", "pages/customService/customService", "pages/coupon/index", "pages/mall/index", "pages/information/index", "pages/vip/index", "pages/store-detail/index", "pages/expirePoint/expirePoint", "pages/editPhone/editPhone", "pages/bindPhone/bindPhone"], "subPackages": [{"root": "hemp", "pages": ["pages/index/index", "pages/enter/enter", "pages/success/success"]}, {"root": "booking", "pages": ["pages/new-booking/new-booking", "pages/new-booking2/new-booking2"]}, {"root": "report", "pages": ["pages/index/index", "pages/h5/h5"]}, {"root": "signUp", "pages": ["pages/signUp/signUp"]}, {"root": "dress", "pages": ["pages/index/index", "pages/camera/camera", "pages/photo/photo"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "MUJI", "navigationBarBackgroundColor": "#ffffff"}, "componentFramework": "glass-easel", "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "tabBar": {"custom": true, "backgroundColor": "#ffffff", "color": "#bebebe", "selectedColor": "#000000", "borderStyle": "white", "list": [{"pagePath": "pages/index/index", "text": "首页"}, {"pagePath": "pages/exchange/exchange", "text": "兑换"}, {"pagePath": "pages/qrCode/qrCode", "text": "会员码"}, {"pagePath": "pages/life/life", "text": "生活圈"}, {"pagePath": "pages/more/more", "text": "更多"}]}, "usingComponents": {"mp-html": "/components/mp-html/index", "BaseHomeModuleTitle": "/components/BaseHomeModuleTitle/index", "van-icon": "@vant/weapp/icon/index", "van-button": "@vant/weapp/button/index", "van-dialog": "@vant/weapp/dialog/index", "van-popup": "@vant/weapp/popup/index", "van-cell": "@vant/weapp/cell/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-swipe-cell": "@vant/weapp/swipe-cell/index", "van-tab": "@vant/weapp/tab/index", "van-tabs": "@vant/weapp/tabs/index", "van-checkbox": "@vant/weapp/checkbox/index", "van-checkbox-group": "@vant/weapp/checkbox-group/index", "van-dropdown-menu": "@vant/weapp/dropdown-menu/index", "van-dropdown-item": "@vant/weapp/dropdown-item/index", "van-field": "@vant/weapp/field/index", "van-search": "@vant/weapp/search/index", "van-picker": "@vant/weapp/picker/index", "van-uploader": "@vant/weapp/uploader/index", "van-rate": "@vant/weapp/rate/index", "my-page": "/components/my-page/my-page", "my-credits": "/components/myCredits/myCredits", "my-modal": "/components/modal/modal", "my-popup": "/components/popup/popup", "jumpModal": "/components/jumpModal/jumpModal", "custom-header": "/components/custom-header/custom-header", "coupon-item": "/components/coupon-item/coupon-item", "header-search-input": "/components/header-search-input/header-search-input", "my-header-top": "/components/header-top/header-top", "no-data-available": "/components/no-data-available/no-data-available", "basic-button": "/components/basicUI/button/button", "basic-dialog": "/components/basicUI/dialog/dialog", "basic-tabs": "/components/basicUI/tabs/tabs", "basic-tips": "/components/basicUI/tips/tips", "basic-search": "/components/basicUI/search/search", "basic-popup": "/components/basicUI/popup/popup", "footer-cart": "/components/footer-cart/footer-cart", "bar-code": "/components/bar-code/bar-code", "custom-modal": "/components/setting/custom-modal/custom-modal", "task-card": "/components/task-card/task-card", "custom-page": "/components/setting/custom-page/custom-page", "custom-nav": "/components/setting/custom-nav/custom-nav", "custom-bg": "/components/setting/custom-bg/custom-bg", "custom-link": "/components/setting/custom-link/custom-link", "my-poster": "/components/setting/poster/poster", "my-division": "/components/setting/division/division", "my-video": "/components/setting/my-video/my-video", "my-rich": "/components/setting/my-rich/my-rich", "active-rules": "/components/activeRules/activeRules", "booking-active-rules": "/components/bookingActiveRules/bookingActiveRules", "booking2-active-rules": "/components/booking2ActiveRules/bookingActiveRules", "my-picker-view": "/components/pickerView/pickerView"}, "permission": {"scope.userLocation": {"desc": "授权位置信息"}}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>"], "embeddedAppIdList": ["wx4099604b04bf38d1"]}