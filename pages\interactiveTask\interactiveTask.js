// pages/interactiveTask/interactiveTask.js
import { getTaskList } from "../../api/index.js";

const app = getApp();

Page({
  data: {
    taskList: [],
    tabList: [
      {
        label: "待完成",
        value: "1",
      },
      {
        label: "已完成",
        value: "2",
      },
    ],
    type: 1,
    pageNum: 1,
    pageSize: 10,
    navHeight: app.globalData.navBarHeight + app.globalData.statusBarHeight,
    showRuleDialog: false,
    currentRuleImg: "",
    useInfo: {},
    loading: false,
    showNew: false,
    tabHeight: 0,
  },
  onLoad(options) {
    const that = this;
    wx.createSelectorQuery()
      .select("#tabBar")
      .boundingClientRect(function (rect) {
        that.setData({
          tabHeight: rect.height,
        });
      })
      .exec();
  },
  onShow() {
    this.getList();
  },
  onShareAppMessage() {
    return {
      title: "邀请您加入MUJI会员，感觉良好的生活", // 分享标题
      path: `/pages/index/index`, // 分享的路径，带上用户的 inviteCode
      imageUrl: `${wx.$config.ossImg}/share/task.jpg`, // 分享图片（可选）
    };
  },
  async getList() {
    this.setData({
      loading: true,
    });
    const { type } = this.data;
    const res = await getTaskList({
      type, // type为1请求的是未完成数据
    });
    const { data } = res;
    this.setData({
      taskList: data.data,
      loading: false,
      showNew: data.showNew === 1,
    });
  },
  onChangeTab(e) {
    const { type } = e.detail;
    this.setData({
      type,
      taskList: [],
    });
    this.getList();
  },
  showRuleDialog(e) {
    const { img } = e.detail;
    this.setData({
      showRuleDialog: true,
      currentRuleImg: img,
    });
  },
  handleClose() {
    this.setData({
      showRuleDialog: false,
      // currentRuleImg: '',
    });
  },
  handleGoto(e) {
    const { url } = e.currentTarget.dataset;
    app.goUrl(url);
  },
});
