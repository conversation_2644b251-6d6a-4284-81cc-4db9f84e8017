<view bindtap="toCoupon" class="coupon-item" data-item="{{item}}" style="background-image: url({{$cdn}}/coupon/coupon-background.png);">
  <!-- catchtap="" -->
  <!-- style="background-image: url({{$cdn}}/coupon/coupon-background.png);" -->
  <view class="coupon-title ellipsis">
    {{item.couponName}}
  </view>
  <view class="coupon-title-vice">
    {{item.couponDesc}}
  </view>
  <view class="coupon-btn" wx:if="{{item.receiveStatus == 1}}" data-item="{{item}}">
    去使用
  </view>
  <view class="coupon-btn1" wx:if="{{item.receiveStatus == 2}}" data-item="{{item}}">
    领取并使用
  </view>
  <view class="coupon-btn dis" wx:if="{{item.isUsed}}">
    {{item.isUsed== 1?'已使用':'已过期'}}
  </view>
  <!-- background-size: 100% 100%;  -->
  <!-- <view class="coupon-line">
  </view> -->
  <view class="coupon-date" wx:if="{{item.isUsed}}">
    {{item.couponEffectiveTime}}前可用
  </view>
  <view class="coupon-date" wx:if="{{item.receiveStatus == 1}}">
    {{item.couponEffectiveTime}}前可用
  </view>
  <view class="coupon-date" wx:if="{{item.receiveStatus == 2}}">
    {{item.receiveTime}}前可领取
  </view>
  <image class="coupon-brand" src="{{$cdn}}/coupon/draw-down.png" mode="" />
</view>