<!-- taskDesc 任务内容 1-线下打卡 2-任务兑礼 3-线下消费 4-邀请好友 5-首购任务-->
<!-- readyCycle 任务完成要求 1.一次性   2.周期   3.周期-阶梯 -->
<!-- type 任务状态 1-待完成  2-已完成 3-已过期 -->
<!-- taskType 任务类型  1-限时 2-购物 3-互动 -->
<!-- isTimeRestrict 任务完成是否限时 1-限时 2-不限时 -->
<view class="task-card-item" bind:tap="showRuleDialog" data-img="{{taskData.ruleImg}}" bind:tap="showRuleDialog">
	<!-- 限时时间完成 -->
	<view class="limit-row" wx:if="{{taskData.isTimeRestrict===1}}">
		<view class="limit-time">
			任务时间: {{ taskData.timeTxt}}
		</view>
	</view>
	<view class="task-card-box">
		<!-- 左侧icon展示 -->
		<view class="task-icon">
			<image class="task-icon-img" src="{{taskData.showImg || $cdn + taskType[taskData.taskType].defaultImg}}" />
		</view>
		<view class="task-info">
			<view class="task-row">
				<!-- 任务类型标识 -->
				<view class="task-txt">
					<view class="task-type">{{taskType[taskData.taskType].label}}</view>
					<view class="task-content">{{taskData.taskName}}</view>
				</view>
				<!-- 规则提示 -->
				<view class="rule-icon" wx:if="{{taskData.ruleImg!==null}}">
					<view class="iconfont icon-Prompts"></view>
				</view>
				<!-- 奖励显示 -->

				<!--周期阶梯类型任务 -->
				<block wx:if="{{taskData.readyCycle === 3}}">
					<!-- 优惠券奖励 -->
					<view wx:if="{{isShowCoupon}}">
						<view wx:if="{{taskData.taskRewardList.length >= 1}}" class="credit-num">
							最多获得{{taskData.taskRewardList.length}}张券
						</view>
						<view wx:else class="credit-num  ellipsis">
							{{taskData.taskRewardList[0].couponNames}}
						</view>
					</view>
					<!-- 积分奖励 -->
					<view wx:else class="credit-num">
						+{{totalPoints}}积分
					</view>
				</block>
				<!-- 一次性 周期类型任务 -->
				<block wx:else>
					<!-- 优惠券奖励 -->
					<view wx:if="{{isShowCoupon}}" class="credit-num  ellipsis">
						{{taskData.taskRewardList[0].couponNames}}
					</view>
					<!-- 积分奖励 -->
					<view wx:else class="credit-num">
						+{{taskData.taskRewardList[0].pointsNum}}积分
					</view>
				</block>
			</view>
			<!-- 任务完成状态显示 -->
			<view class="task-row">
				<!-- 已完成 -->
				<view wx:if="{{type==='2'}}" class="task-step">
					<!-- readyCycle 1.一次性   2.周期   3.周期-阶梯 -->
					<!-- isCycleTask 非阶梯性任务 -->
					<block wx:if="{{taskData.isCycleTask}}">
						<view class="step-item" wx:for="{{taskData.isCycleTask}}" wx:key="index">
							<view class="{{visitor?'uncomplate-item':'complated-item'}}">
								<view class="iconfont icon-Mark"></view>
							</view>
						</view>
					</block>
					<!-- 阶梯性任务 -->
					<block wx:else>
						<view class="step-item" wx:for="{{taskData.taskRewardList}}" wx:key="index">
							<view class="{{visitor?'uncomplate-item':'complated-item'}}">
								<view class="iconfont icon-Mark"></view>
							</view>
						</view>
					</block>

				</view>
				<!-- 待完成  已过期 -->
				<view wx:else class="task-step">
					<!-- 阶梯任务 -->
					<block wx:if="{{taskData.readyCycle===3}}">
						<view class="step-item" wx:for="{{taskData.taskRewardList}}" wx:key="id">
							<view wx:if="{{!item.complated||visitor}}" class="uncomplate-item {{item.rewardType===1 ? ' coupon': '' }}">
								<block wx:if="{{item.rewardType===1}}">券</block>
								<block wx:else>+{{item.pointsNum}}</block>
							</view>
							<view wx:else class="complated-item">
								<view class="iconfont icon-Mark"></view>
							</view>
						</view>
					</block>
					<!-- 一次性 周期任务 -->
					<block wx:else>
						<view class="step-item" wx:for="{{taskData.isCycleTask}}" wx:key="index">
							<view wx:if="{{!item||visitor}}" class="uncomplate-item">
								<view class="iconfont icon-Mark"></view>
							</view>
							<view wx:else class="complated-item">
								<view class="iconfont icon-Mark"></view>
							</view>
						</view>
					</block>
				</view>
				<view wx:if="{{type === '2'}}" class="gray-font">
					已获得
				</view>
				<view wx:elif="{{type==='3'}}" class="gray-font">
					已过期
				</view>
				<block wx:else>
					<!-- 打卡类型 -->
					<!-- checkClocNow    当天是否已打卡上限：1是，2否 checkClocNow -->
					<button wx:if="{{taskData.taskDesc==1}}" class="go-btn" catchtap="handleGo" data-item="{{taskData}}" data-type="task" data-id="{{taskData.id}}" data-task="{{taskData.taskDesc}}">
						去打卡
						<!-- {{taskData.checkClocNow==1?'打卡成功':'立即打卡'}} -->
						<view class="iconfont icon-View1 go-icon" />
					</button>
					<button wx:else open-type="{{taskData.taskDesc===4 && 'share'}}" class="go-btn" catchtap="handleGo" data-item="{{taskData}}" data-type="task" data-id="{{taskData.id}}" data-task="{{taskData.taskDesc}}">
						{{taskBtnType[taskData.taskDesc]}}
						<view class="iconfont icon-View1 go-icon" />
					</button>
				</block>
			</view>
		</view>
	</view>
</view>

<!-- 未打卡弹窗 -->
<!-- 打卡弹窗 -->
<store-popup show="{{showModalDialog }}" title="{{modal.title}}" content="{{modal.content}}" confirmText="{{modal.confirmText}}" cancelText="{{modal.cancelText}}" tip="{{modal.tip}}" icon="{{modal.icon}}" store="{{modal.store}}" bindcancel="close" bindconfirm="confirm" bindclose="close" showType="normal">
</store-popup>