<my-modal show="{{show}}" zIndex="{{zIndex}}" background="{{background}}" overlayClick="{{overlayClick}}"
  overlayBackground="{{overlayBackground}}" closeable="{{closeable}}" borderRadius="{{borderRadius}}" bindclose="close">
  <view class="popup" id="popup">
    <!-- 标题 -->
    <view class="popup-title" catch:touchmove="touchmove1">{{title}}</view>
    <!-- 内容区域 可以是富文本 -->
    <view class="popup-content">
      <scroll-view class="popup-scroll" style="max-height:{{height}}rpx" scroll-y catch:touchmove="touchmove"
        wx:if="{{height}}">
        <view
          style="padding-left:60rpx;padding-right:30rpx;box-sizing:border-content;text-align: {{isCenter?'center':'left'}};">
          <bolck wx:if="{{showType==='normal'}}">
            <text class="normal-content">{{content}}</text>
          </bolck>
          <block wx:elif="{{showType=='editor'}}">
            <mp-html content="{{content}}" />
          </block>
        </view>
      </scroll-view>
    </view>
    <view class="popup-bottom" catch:touchmove="touchmove1">
      <!-- 其他内容 这里的内容如果需要滑动 必须使用scroll-view-->
      <scroll-view class="popup-other" scroll-y style="width:100%;max-height:690rpx;">
        <slot></slot>
      </scroll-view>
      <!-- 按钮区域 -->
      <view class="popup-button confirm" bindtap="confirm" wx:if="{{confirmText}}">{{confirmText}}</view>
      <view class="popup-button cancel" bindtap="cancel" wx:if="{{cancelText}}">{{cancelText}}</view>
      <view style="height:60rpx;width:100%;"></view>
    </view>
  </view>
</my-modal>