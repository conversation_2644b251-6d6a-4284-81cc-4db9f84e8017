.cart-card {
  display: flex;
  //margin-bottom: var(--page-margin);
  //margin: 0 var(--page-margin);
  //margin-top: 20rpx;
  //margin-bottom: 20rpx;
  height: 260rpx;
  position: relative;

  .cart-img {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 260rpx;
    width: 260rpx;
    //background-color: pink;
    background-color: #FAFAFA;
    margin-right: 30rpx;
    position: relative;

    .img {
      display: block;
      width: 244rpx;
      height: 244rpx;
    }

    .new-tag {
      width: 64rpx;
      height: 34rpx;
      background: #7F0019;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 34rpx;
      text-align: center;
      font-style: normal;
      position: absolute;
      left: 0;
      top: 0;
    }
  }

  .cart-info {
    position: relative;
    height: 100%;

    .cart-title {
      height: 110rpx;

      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .cart-price {
      display: flex;
      align-items: flex-end;
      position: relative;
      margin-bottom: 30rpx;
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;

      .red {
        margin-right: 10rpx;
        height: 40rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 28rpx;
        color: #7F0019;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
      }

      .line {
        height: 28rpx;
        line-height: 28rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 18rpx;
        color: #3C3C43;
        text-align: left;
        font-style: normal;
        text-decoration-line: line-through;
      }
    }

    .cart-btn {
      position: absolute;
      left: 0;
      bottom: 10rpx;
      width: 160rpx;
      height: 60rpx;
      border-radius: 5rpx;
      border: 1rpx solid #BBBBBB;
      display: flex;
      align-items: center;

      font-feature-settings: "tnum";
      font-variant-numeric: tabular-nums;

      .btn-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 40%;
        text-align: center;
        font-size: 24rpx;
      }

      .sku-num {
        flex: 1;
        text-align: center;
        font-size: 24rpx;
        white-space: nowrap;
      }
    }
  }
}
