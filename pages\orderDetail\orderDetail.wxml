<!--pages/orderDetail/orderDetail.wxml-->
<!--pages/purchaseDetail/purchaseDetail.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-list">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>订单详情</text>
      </view>
    </custom-header>
    <scroll-view scroll-y="true" style="--bottom:0rpx; flex: 1;height: 0;width: 100%;">
      <view class="page-content">
        <view class="page-header-top">
          <view class="page-header-logo">
            <image src="{{$cdn}}/details-logo.png" mode="" />
          </view>
          <view class="page-header-txt">
            {{details.channelName}}
          </view>
          <view class="page-header-line"></view>
          <view style="margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">单号</view>
            <view class="page-header-cell-value">{{details.orderSn}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">时间</view>
            <view class="page-header-cell-value">{{details.payTime}}</view>
          </view>
          <view style="margin-bottom: 40rpx;" class="page-header-line"></view>
          <block wx:for="{{details.goods}}" wx:key="index">
            <view class="page-header-shop">
              <view class="page-header-shop-info">
                <view class="page-header-shop-name">{{item.goodsName}}</view>
                <view class="page-header-cell-value">￥{{item.buyerFee}}</view>
              </view>
              <view class="page-header-shop-info">
                <view class="page-header-shop-item">{{item.goodsId}}</view>
                <view class="page-header-shop-item">×{{item.quantity}}</view>
              </view>
            </view>
          </block>
          <block wx:for="{{details.payList}}" wx:key="index">
            <view class="page-header-cell">
              <view class="page-header-cell-item">{{item.payName}}</view>
              <view class="page-header-cell-value">{{item.pay}}</view>
            </view>
          </block>
          <view style="margin-top: 40rpx;margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">数量合计</view>
            <view class="page-header-cell-value">{{total}}件</view>
          </view>
          <view class="page-header-cell" style="margin-bottom: 0;">
            <view class="page-header-cell-item">商品总价</view>
            <view class="page-header-cell-value">￥{{details.totalFee}}</view>
          </view>
          <view style="padding-bottom: 80rpx;"></view>
        </view>

        <view class="page-equity">
          <view class="page-equity-title">权益信息</view>
          <view class="page-equity-line"></view>
          <view class="page-equity-cell">
            <view class="page-equity-cell-item">可累计权益金额</view>
            <view class="page-equity-cell-value">￥{{details.calculateFee}}</view>
          </view>
          <view class="page-equity-cell" style="margin-bottom: 29rpx;">
            <view class="page-equity-cell-item">本次消费共获得</view>
            <view class="page-equity-cell-value" style="color: #7F0019;">{{details.bonus}}积分</view>
          </view>
          <view class="page-equity-cell-left-value" style="color: #7F0019;">{{details.mileage}}里程</view>

        </view>
        <view style="padding-bottom: 40rpx;"></view>
        <view class="online-tips">*详细订单信息请前往订单对应商城查询</view>
        <view style="height: 80rpx;"></view>
      </view>

    </scroll-view>
  </view>
</my-page>