// signUp/pages/clock/clock.js
const app = getApp()
import {
  getUserSignInSpeed,
  signInUserSupplement, // 补卡
  getLotteryUser, // 获取剩余抽奖次数 分享次数
  getCampaignType
} from '../../api/index.js'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 展示打卡列表
    clockList: [{
        days: 1,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      },
      {
        days: 2,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      },
      {
        days: 3,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      },
      {
        days: 4,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      }, {
        days: 5,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      },
      {
        days: 6,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      },
      {
        days: 7,
        state: 0, // 打卡状态 0未打卡 1已打卡 2缺卡
      }
    ],
    recordList: [], // 打卡记录列表
    showContact: false,
    showContact1: false,
    showRules: false,
    campaignCode: wx.getStorageSync('campaignCode'),
    channelTwo: app.globalData.channelTwo,
    channelOne: app.globalData.channelOne,
    completeTimes: 0, // 完成的次数
    surplusTimes: 7, // 剩余的次数
    surplusRepairSignInTimes: '', // 补卡次数
    isSignIn: false, // 今日是否打卡
    disabled: false,
    btnTitle: '记录体验完成打卡',
    isLastDay: false, // 是否是第7天
    prizeDraw: false, // 抽奖
    title1: '完成7天打卡\n解锁惊喜好礼',
    title2: `天打卡\n再打卡`,
    title3: `天打卡\n获得`,
    SupplementDays: '',
    days: 0,
    showPopup: false,
    infoLottery: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },
  getDatas() {
    getCampaignType().then(res => {
      console.log(res.data, 'res.data');
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      this.setData({
        CampaignData: res.data
      })
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getDatas()
    this.getData()
    // this.setData({
    // showContact1: false,
    // showContact: false,
    // showRules: false
    // })
  },
  getData(openContact) {
    let clockList = this.data.clockList
    // 获取当前打卡进度
    getUserSignInSpeed({
      campaignCode: this.data.campaignCode
    }).then(({
      data
    }) => {
      console.log(data, '当前打卡进度');
      let {
        list,
        surplusRepairSignInTimes,
        surplusTimes,
        completeTimes,
        isSignIn,
        days
      } = data
      if (list && list.length > 0) {
        list.forEach((item, index) => {
          clockList = clockList.map((obj) => {
            if (item.days == obj.days) {
              obj = {
                ...obj,
                ...item
              }
            }
            return obj
          })
        })
        console.log(clockList, 'clockList');
      } else {
        clockList.forEach((item, index) => {
          if (index == 0) {
            item.today = true
          }
          // list 返回为空 判断当前打卡天数
          if (item.days < days) {
            item.state = 2
          }
        })
      }
      let prizeDraw = this.data.prizeDraw
      // 判断打卡是否完成
      if (completeTimes >= 7) {
        prizeDraw = true
        // 没有分享 弹弹框
        if (!wx.getStorageSync('isPrizeDialog')) {
          wx.setStorageSync('isPrizeDialog', 1)
          this.openPopup()
        }
      } else prizeDraw = false
      this.setData({
        clockList,
        recordList: list ? list.reverse() : [],
        surplusRepairSignInTimes,
        surplusTimes,
        completeTimes,
        // 判断是否是要去抽奖 最后一天已经打完卡 就显示去抽奖
        prizeDraw,
        // 判断今天是否已经打过卡
        isSignIn,
        btnTitle: isSignIn ? '今日已打卡' : '记录体验完成打卡',
        // 当前是第几天
        days: days,
        isLastDay: days >= 7
      })
      if (openContact) {
        this.openContact()
      }
    }).catch(err => {
      console.log(err);
    })
  },
  openPopup() {
    this.setData({
      showPopup: true
    })
  },
  // 点击我知道了和弹框关闭按钮跳到抽奖列表页面
  closePopup: app.debounce(async function () {
    wx.redirectTo({
      url: '/signUp/pages/prizeDraw/prizeDraw',
    })
    this.setData({
      showPopup: false
    })
  }),
  detail(e) {
    console.log(e, '查看详情');
    console.log(this.data.recordList, 'this.data.recordList');
    let index = e.currentTarget.dataset.index
    wx.navigateTo({
      url: `/signUp/pages/clockIn/clockIn?isLastDay=${this.data.isLastDay}&info=${JSON.stringify(this.data.recordList[index])}&type=view&days=${this.data.recordList[index].days}`,
    })
  },
  clickRules() {
    wx.$mp.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  },
  closeRules() {
    this.setData({
      showRules: false,
    })
  },
  openContact() {
    this.setData({
      showContact: true,
    })
  },
  closeContact() {
    this.setData({
      showContact: false,
    })
  },
  openGuide() {
    this.setData({
      showContact1: true
    })
  },
  closeGuide() {
    this.setData({
      showContact1: false
    })
  },
  // 补卡功能
  buqian: app.debounce(async function (e) {
    wx.$mp.track({
      event: 'punch_replace_submit_click',
    })
    console.log(e, 'eeeee');
    let {
      state,
      days
    } = e.currentTarget.dataset.query
    if (state == 2) {
      // 如果是补卡他弹出分享指引 判断是否已经补卡
      this.openGuide()
      this.setData({
        SupplementDays: days
      })
    }
  }),
  // 去打卡
  submit: app.debounce(async function () {
    wx.$mp.track({
      event: 'punch_submit_click',
    })
    let isLastDay = this.data.isLastDay
    console.log(!this.data.isSignIn && this.data.completeTimes < 6, '!this.data.isSignIn && this.data.surplusTimes > 6');
    console.log(isLastDay, 'isLastDay');
    if (!this.data.isSignIn) {
      wx.navigateTo({
        url: `/signUp/pages/clockIn/clockIn?isLastDay=${isLastDay}&days=${this.data.days}&type=edit`,
      })
    }
  }),
  // 去抽奖
  ClickPrizeDraw: app.debounce(async function () {
    getLotteryUser({
      campaignCode: this.data.campaignCode
    }).then(({
      data
    }) => {
      console.log(data, 'data');
      // 抽奖一共两次 第一次进抽奖列表 第二次进抽奖信息详情 总数减去剩余的 ==0 跳列表页面
      if (data && (data.totalCount - data.surplusCount == 0)) {
        wx.navigateTo({
          url: `/signUp/pages/prizeDraw/prizeDraw`,
        })
      } else {
        // 剩余抽奖次数为1 直接进入抽奖信息详情页面
        wx.navigateTo({
          url: `/signUp/pages/Winning/Winning`,
        })
      }

    })

  }),
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage(e, obj) {
    console.log(e, obj, 'e,obj');
    let that = this
    if (this.data.SupplementDays) {
      signInUserSupplement({
        campaignCode: this.data.campaignCode,
        channelTwo: this.data.channelTwo,
        channelOne: this.data.channelOne,
        days: this.data.SupplementDays,
      }).then(ShareData => {
        console.log(ShareData, '分享后返回数据 1111');
        let time = setTimeout(() => {
          if (ShareData.data.patchSignInState == 1) {
            this.setData({
              surplusRepairSignInTimes: ShareData.data.patchSignInTimes,
              SupplementDays: ''
            })
            this.getData(!ShareData.openContact)
          } else {
            wx.showToast({
              title: ShareData.data.patchSignInStateDesc,
              icon: 'none',
              duration: 4000,
            })
          }
          clearTimeout(time)
        }, 3000)
        this.closeGuide()
      })
    }
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
