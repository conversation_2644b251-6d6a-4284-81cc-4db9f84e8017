// 获取导航数据
exports.getTabBar = data => wx.$request({
  url: '/app/basic/white/navigation_config/default',
  data,
  method: 'get'
})
// 获取全局风格设置
exports.getStyle = data => wx.$request({
  url: '/app/basic/style_config/get',
  data,
  method: 'get'
})

// 根据模板ID查询小程序页面模板
exports.getTemplateById = data => wx.$request({
  url: '/app/basic/miniapp_template/info',
  data,
  method: 'get'
})

// 根据模板ID预览小程序页面模板
exports.getTemplateByPreviewId = data => wx.$request({
  url: '/app/basic/miniapp_template/preview',
  data,
  method: 'get'
})


// 根据模板类型及页面类型查询小程序页面模板
exports.getTemplateByType = data => wx.$request({
  url: '/app/basic/miniapp_template/info_by_type',
  data,
  method: 'get'
})

// 根据场景获取小程序订阅消息
exports.getSubscribeByScene = data => wx.$request({
  url: '/app/basic/subscribe_msg/ids_by_scene',
  data,
  method: 'get'
})

// 添加小程序订阅消息订阅记录
exports.addSubscribe = data => wx.$request({
  url: '/app/basic/subscribe_msg/record_add',
  data,
  method: 'post'
})

export const apiIndex = {
  async getHomeMessageReminder(data) {
    return wx.$request({ url: '/app/user/un/read/msg/list', data, method: 'get' }).then(res => {
      return res
    })
  },
  async getHomeMessageReminderRead(data) {
    return wx.$request({ url: '/app/user/msg/read', data, method: 'get' }).then(res => {
      return res
    })
  },

}
