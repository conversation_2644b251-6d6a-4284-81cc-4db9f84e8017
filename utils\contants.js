const genderList = [{
  label: '先生',
  value: 1,
},
{
  label: '女士',
  value: 2,
}
]

const storeTypeList = {
  1: '普通店',
  2: '旗舰店'
}


// const taskType = {
//   1: '限时',
//   2: '购物',
//   3: '互动'
// }

const taskType = {
  1: {
    label: '限时',
    defaultImg: '/task/limit_1.png',
  },
  2: {
    label: '购物',
    defaultImg: '/task/shoping_2.png'
  },
  3: {
    label: '互动',
    defaultImg: '/task/interact_3.png'
  },
}

const taskBtnType = {
  1: '前往打卡', //'线下打卡',
  2: '去兑换', // '兑礼任务',
  3: '去购买', // 线下消费
  4: '前往邀请', // '邀请好友',
  5: '去购买' // 首次购买
}
const taskBtnTrack = {
  1: "offLine",
  2: "exchange",
  3: "buy",
  4: "invite",
  5: "first_buy"
}

const writeOffReason = {
  1: '不想/不再需要使用',
  2: '优惠活动少',
  3: '账户出现异常',
  4: '担忧隐私及安全问题',
  5: '其他'
}

const productType = {
  1: '实物商品',
  2: '电子券'
}

const productTagArray = [{
  value: '人气新品',
  imgUrl: `${wx.$config.ossImg}/product/new_hot.png`,
  height: 38,
  width: 108,
  position: 'top', // 列表位置
  txt: '人气新品', // 商详对应文案
  bgColor: '#7F0019', // 商详标签背景色
  fontColor: '#FFF', // 商详标签字体色
},
{
  value: '会员节专享',
  imgUrl: `${wx.$config.ossImg}/product/member_tag.png`,
  height: 82,
  width: 68,
  position: 'top', // 列表位置
  txt: '会员节专享', // 商详对应文案
  bgColor: '#7F0019', // 商详标签背景色
  fontColor: '#FFF', // 商详标签字体色
},
{
  value: '活动限定',
  imgUrl: `${wx.$config.ossImg}/product/active_tag.png`,
  height: 75,
  width: 68,
  position: 'top',
  txt: '活动限定',
  bgColor: '#E0CEAA',
  fontColor: '#7F0019',
},
// {
//   value: '限时折扣 积分兑礼',
//   imgUrl: `${wx.$config.ossImg}/product/limit_discount.png`,
//   height: 48,
//   width: 310,
//   position: 'bottom'
// },
{
  value: '限时折扣 积分兑礼',
  imgUrl: `${wx.$config.ossImg}/product/limit_discount1.png`,
  height: 82,
  width: 68,
  position: 'top',
  txt: '限时折扣',
  bgColor: '#7F0019',
  fontColor: '#FFF',
},
{
  value: '金级专享',
  imgUrl: `${wx.$config.ossImg}/product/gold_grade.png`,
  height: 122,
  width: 122,
  position: 'top',
  txt: '金级专享',
  bgColor: '#D4B361',
  fontColor: '#FFF',
},
{
  value: '银级以上专享',
  imgUrl: `${wx.$config.ossImg}/product/silver_grade.png`,
  height: 122,
  width: 122,
  position: 'top',
  txt: '银级以上专享',
  bgColor: '#ACB4BF',
  fontColor: '#FFF',
}, {
  value: '铜级以上专享',
  imgUrl: `${wx.$config.ossImg}/product/copper_grade.png`,
  height: 122,
  width: 122,
  position: 'top',
  txt: '铜级以上专享',
  bgColor: '#BF9A78',
  fontColor: '#FFF',
}
]

module.exports = {
  productType,
  genderList,
  storeTypeList,
  taskType,
  taskBtnType,
  taskBtnTrack, // 任务埋点对应文案
  writeOffReason,
  productTagArray
};
