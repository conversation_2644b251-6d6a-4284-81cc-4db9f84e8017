import {
  throttle
} from '../../utils/util'

const app = getApp()


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    isShow: {
      type: Boolean,
      value: false
    },
    num: {
      type: Number,
      value: 0
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    }
  },
  lifetimes: {
    attached () {
      this.setUserInfo()
    }
  },
  pageLifetimes: {
    show () {
      this.setUserInfo()
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    checked: false,
    scrolltolower: false,
    userInfo: {}
  },
  /**
   * 组件的方法列表
   */
  methods: {
    scroll () {
      if (this.data.scrolltolower) {
        setData({ scrolltolower: false })
      }
    },
    scrolltolower () {
      setData({ scrolltolower: true })
    },
    checkedHandler () {
      this.setData({ checked: !this.data.checked })
    },
    // 禁止穿透
    touchmove () {
      return false
    },
    setUserInfo () {
      this.setData({
        userInfo: app.globalData.userInfo
      })
    },
    onClose () {
      // if (!this.data.checked) return wx.$mp.showToast({ title: '请勾选隐私政策' })
      this.triggerEvent('close')
    },
    copyTxt (e) {
      const {
        copy
      } = e.currentTarget.dataset
      wx.setClipboardData({
        data: copy
      })
    },
    // 拨打电话
    callTel: throttle(async function (e) {
      const {
        num
      } = e.currentTarget.dataset
      wx.makePhoneCall({
        phoneNumber: num,
        success: function () {
          console.log('拨打电话成功！')
        },
        fail: function () {
          console.log('拨打电话失败！')
        }
      })
    })
  }
})
