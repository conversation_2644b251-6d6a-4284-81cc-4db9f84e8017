<!-- <navigator url="{{message.jumpUrl}}"> -->
<view class="message-card" bindtap="handlegoto" data-url="{{message.jumpUrl}}" data-state="{{message.isRead}}" data-id="{{message.id}}">
  <view class="icon-box">
    <view class="iconfont icon-Messages" />
  </view>
  <view class="info-box">
    <view class="title">
      <view class="txt">
        {{message.title}}
      </view>
      <view class="time">
        {{message.time}}
      </view>
    </view>
    <view class="desc">
      {{message.msgDesc}}
    </view>
  </view>
</view>
<!-- </navigator> -->