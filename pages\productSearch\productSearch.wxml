<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>{{ '搜索' }}</text>
      </view>
    </custom-header>
    <view class="page-header">
      <view class="search-box">
        <basic-search placeholder="搜索商品名称" value="{{productName}}" clearable="{{true}}" bind:confirm="handleSearchStore" bind:change="handleChange" focus="{{true}}" />
      </view>
      <view wx:if="{{searchState}}" class="page-header-tips">
        共有 {{productList.length}} 件商品
      </view>
    </view>
    <scroll-view class="shop-list" scroll-y="true" style="--bottom:0">
      <view class="page-shop-list">

        <view class="product-item" wx:for="{{productList}}" wx:key="shelfProductId">
          <product-card skuInfo="{{item}}" />
        </view>

      </view>
      <block wx:if="{{searchState}}">
        <no-data-available loading="{{loadingList}}" data="{{productList}}" text="抱歉，没有找到相关结果">
        </no-data-available>
      </block>
    </scroll-view>
  </view>
</my-page>