// pages/editPhone/editPhone.js
const app = getApp()

import {
  sendSmsEditCode,
  updatePhone,
} from '../../api/index'
import {
  throttle
} from '../../utils/util';

const requiredFields = ['oldMobile', 'newMobile', 'smsCode']
const requireSendDisabled = ['oldMobile', 'newMobile']

Page({

  /**
   * 页面的初始数据
   */
  data: {
    info: {},
    isComplate: false,
    loading: false,
    countState: false,
    countdown: 60,
    buttonText: '发送验证码',
    error: false, // 异常报错
    errContent: "您填写的手机号已关联了其他微信账户,\n请确认手机号后重新进行更换。\n\n如有疑问请致电客服\n400-920-9299[86315]",
    sendDisabled: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  changeForm(e) {
    const {
      key
    } = e.currentTarget.dataset;
    const {
      value
    } = e.detail;
    const {
      info
    } = this.data;
    const params = info;
    params[key] = value;
    this.setData({
      info: params,
    })
    this.validateForm();
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const {
      info
    } = this.data;
    const allFilled = requiredFields.every(field => info[field]);
    const sendDisabled = requireSendDisabled.every(field => info[field]);
    console.log('   sendDisabled', sendDisabled);
    this.setData({
      isComplate: allFilled,
      sendDisabled,
    })

  },
  // 弹窗关闭
  close() {
    this.setData({
      error: false,
      success: false
    })
  },
  sendCode: throttle(async function () {
    const {
      info: {
        newMobile
      }
    } = this.data;
    if (!newMobile) {
      wx.showToast({
        title: '请填写新手机号',
        icon: 'none',
      });
      return;
    }
    if (this.data.countState) return;


    try {
      const res = await sendSmsEditCode({
        mobile: newMobile
      })
      if (res?.code === 0) {
        wx.showToast({
          title: '发送验证码成功',
        })
        this.setData({
          countState: true,
          buttonText: `${this.data.countdown}秒后重发`
        })
        // 启动倒计时
        let countdown = this.data.countdown;
        let interval = setInterval(() => {
          countdown--;
          this.setData({
            countdown,
            buttonText: `${countdown}秒后重发`
          });
          // 当倒计时结束时
          if (countdown <= 0) {
            clearInterval(interval); // 清除定时器
            this.setData({
              countState: false,
              buttonText: '发送验证码', // 恢复按钮文本
              countdown: 60,
            });
          }
        }, 1000); // 每秒更新一次
      }
    } catch (error) {
      // 加入会员信息异常
      if (error?.code > 1000) {
        this.setData({
          error: true,
        })
      }
    }


  }),
  confirm() {
    this.setData({
      loading: true
    })
    let {
      oldMobile,
      newMobile,
      smsCode
    } = this.data.info;
    updatePhone({
      oleMobile: oldMobile,
      newMobile,
      smsCode,
    }).then(async res => {
      this.setData({
        loading: false
      })
      await app.getUserInfo()
      wx.showToast({
        title: '修改手机号成功！',
        duration: 1000,
      })
      // 更新上个页面的手机号
      const eventChannel = this.getOpenerEventChannel()
      eventChannel?.emit('chagePhone', newMobile);
      const timer = setTimeout(() => {
        clearTimeout(timer)
        wx.navigateBack();
      }, 1000)
    }).catch((res) => {
      this.setData({
        loading: false
      })
      // 加入会员信息异常
      if (res && res.code > 1000) {
        this.setData({
          error: true,
        })
      }
    })
  }
})
