<!--pages/purchaseDetail/purchaseDetail.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-list">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>小票详情</text>
      </view>
    </custom-header>
    <scroll-view scroll-y="true" style="--bottom:0rpx; flex: 1;height: 0;width: 100%;">
      <view class="page-content">
        <view class="page-header-top">
          <view class="page-header-logo">
            <image src="{{$cdn}}/details-logo.png" mode="" />
          </view>
          <view class="page-header-txt">
            {{details.shopName}}
          </view>
          <view class="page-header-line"></view>
          <view class="page-header-rule" bindtap="toRule">
            <view class="page-header-text">
              <image style="width: 28rpx;height: 28rpx;" src="{{$cdn}}/rule-icon.png" mode="" /> 商品退换细则
            </view>
            <view class="page-header-icon">
              <image style="width: 28rpx;height: 28rpx;" src="{{$cdn}}/left-icon.png" mode="" />
            </view>
          </view>
          <view style="margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">单号</view>
            <view class="page-header-cell-value">{{details.orderSn}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">时间</view>
            <view class="page-header-cell-value">{{details.checkoutDt}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">收银员</view>
            <view class="page-header-cell-value">{{details.staffId}}</view>
          </view>
          <view style="margin-bottom: 40rpx;" class="page-header-line"></view>
          <block wx:for="{{details.items}}" wx:key="index">
            <view class="page-header-shop">
              <view class="page-header-shop-name">{{item.itemName}}</view>
              <view class="page-header-shop-order">{{item.itemId}}</view>
              <view class="page-header-shop-info">
                <view class="page-header-shop-info1">{{item.salePrice}} * {{item.qty}}</view>
                <view class="page-header-shop-info2">{{item.salePrice}}</view>
              </view>
            </view>
          </block>
          <view style="margin-top: 40rpx;margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">应收金额</view>
            <view class="page-header-cell-value">{{details.saleAmt}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">实收金额</view>
            <view class="page-header-cell-value">{{details.realAmt}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">找零</view>
            <view class="page-header-cell-value">{{details.change}}</view>
          </view>
          <view style="margin-top: 40rpx;margin-bottom: 40rpx;" class="page-header-line"></view>
          <block wx:for="{{details.payList}}" wx:key="index">
            <view class="page-header-cell">
              <view class="page-header-cell-item">{{item.payName}}</view>
              <view class="page-header-cell-value">{{item.pay}}</view>
            </view>
          </block>
          <view style="margin-top: 40rpx;margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">数量合计</view>
            <view class="page-header-cell-value">{{details.qty}}</view>
          </view>
          <view class="page-header-cell">
            <view class="page-header-cell-item">优惠金额</view>
            <view class="page-header-cell-value">{{details.discountAmt}}</view>
          </view>
          <view style="margin-top: 40rpx;margin-bottom: 40rpx;" class="page-header-line"></view>
          <view class="page-header-code">
            <canvas id="barcodeCoupon" style="width: 550px; height: 95px;" type="2d" />
            <image src="{{barcodeImg}}" style="width: 550px; height: 95px;" mode="" />
          </view>
        </view>

        <view class="page-equity">
          <view class="page-equity-title">权益信息</view>
          <view class="page-equity-line"></view>
          <view class="page-equity-cell">
            <view class="page-equity-cell-item">可累计权益金额</view>
            <view class="page-equity-cell-value">￥{{details.calculateFee}}</view>
          </view>
          <view class="page-equity-cell" style="margin-bottom: 29rpx;">
            <view class="page-equity-cell-item">本次消费共获得</view>
            <view class="page-equity-cell-value" style="color: #7F0019;">{{details.bonus}}积分</view>
          </view>
          <view class="page-equity-cell-left-value" style="color: #7F0019; font-weight: 500;">{{details.mileage}}里程</view>

        </view>
        <view class="page-info">
          <view class="page-info-title">
            谢谢惠顾
          </view>
          <view class="page-info-txt">请仔细核对收据票据，找零及商品，离柜概不负责。</view>
          <view class="page-info-line" style="margin-bottom: 25rpx;"></view>
          <view class="page-info-item" style="align-items: flex-start;">
            <view class="page-info-item-label">地址：</view>
            <view class="page-info-item-value">{{details.shopAddr}}</view>
          </view>
          <view class="page-info-item">
            <view class="page-info-item-label">电话：</view>
            <view class="page-info-item-value">{{details.telNo}}</view>
          </view>
          <view class="page-info-item">
            <view class="page-info-item-label">网络商城：</view>
            <view class="page-info-item-value">www.muji.com.cn</view>
          </view>
          <view class="page-info-line" style="margin-bottom: 40rpx;margin-top: 45rpx;"></view>
          <view class="page-info-footer">
            更多活动讯息，请关注我们的
          </view>
          <view class="page-info-cell">
            <view class="page-info-cell-item">官方微博：</view>
            <view class="page-info-cell-value">无印良品MUJI</view>
          </view>
          <view class="page-info-cell">
            <view class="page-info-cell-item">微信ID：</view>
            <view class="page-info-cell-value">MUJI_CHINA</view>
          </view>
        </view>
        <view style="padding-bottom: 80rpx;"></view>
      </view>
    </scroll-view>
  </view>
</my-page>