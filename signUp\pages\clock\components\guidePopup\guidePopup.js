Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // step: {
    //   type: Object,
    //   default: () => { },
    // },
    showGuide: {
      type: Boolean,
      default: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    stepName: "step", //该提示步骤的名称，用于不在重复展示
    // showGuide: true, // 是否显示引导
    systemInfo: "", //屏幕宽度高度等信息
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // skip() {
    //   this.setData({
    //     showGuide: false,
    //   });
    //   // wx.setStorageSync(this.data.stepName, "true");
    // },
    // 关闭指引
    close() {
      this.triggerEvent('finish')
    },
  },
  lifetimes: {
    attached: function () {
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
    },
  },
});

