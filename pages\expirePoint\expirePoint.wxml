<!--pages/expirePoint/expirePoint.wxml-->
<!--pages/myCoupon/myCoupon.wxml-->
<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>我的积分</text>
      </view>
    </custom-header>
    <view class="page-nav">
      <van-tabs color="#3C3C43" title-inactive-color="#888888" title-active-color="#3C3C43" tab-class="tab-item"
        custom-class="nav-custom" nav-class="nav-block" wrap-class="nav-wrap" line-height="1.6" active="{{currentTab }}"
        bind:change="onChangeTab" line-width="102">
        <van-tab wx:for="{{tabList}}" name="{{item.value}}" title="{{item.label}}" wx:key="value" />
      </van-tabs>
    </view>
    <scroll-view scroll-y="{{currentList.length>0}}" scroll-top="{{scrollTop}}" class="page-content"
      bindscrolltolower="onReachBottom" enhanced="{{true}}" show-scrollbar="{{ false }}" scroll-y="{{true}}"
      bounces="{{true}}">
      <view class="list-box" wx:if="{{currentList.length>0}}">
        <view class="list-item" wx:for="{{currentList}}" wx:key="index">
          <expire-item item="{{item}}" type="{{currentTab}}" />
        </view>
      </view>
      <block wx:if="{{currentList.length===0 && currentTab===1}}">
        <no-data-available top="{{536}}" data="{{currentList}}" text="暂无即将过期积分" />
      </block>
      <block wx:if="{{currentList.length===0 && currentTab===2}}">
        <no-data-available top="{{536}}" data="{{currentList}}" text="暂无已过期积分" />
      </block>

    </scroll-view>
  </view>
</my-page>