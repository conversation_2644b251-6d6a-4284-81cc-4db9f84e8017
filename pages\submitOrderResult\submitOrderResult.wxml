<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
      <view slot="content" class="page-title">
        <text>兑换结算</text>
      </view>
    </custom-header>

    <view class="result-wrapper">
      <view class="result-header">
        <view class="result-icon iconfont icon-Fulfill1"></view>
        <view>兑换成功</view>
      </view>

      <view class="result-content">
        {{options.orderPoint}}积分<block wx:if="{{options.orderAmount>0}}">+{{options.orderAmount}}元</block>
      </view>

      <view class="result-desc">
        <view>您已成功完成兑换</view>
        <view>可前往【我的】-【我的礼券】查看</view>
        <view>商品金额部分需到店支付</view>
      </view>
    </view>

    <view class="bottom-box">
      <basic-button width="{{320}}" btnState="plain" size="large" bind:click="onClickBack">
        继续兑换
      </basic-button>

      <basic-button width="{{320}}" btnState="primary" size="large" bind:click="onClickDetail">
        查看订单
      </basic-button>
    </view>

  </view>
</my-page>