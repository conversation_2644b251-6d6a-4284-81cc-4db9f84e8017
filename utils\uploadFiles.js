import FormData from './formData'
function uploadFiles({
  fileUrl
}) {
  let formData = new FormData();    //新建一个formData对象
  for (let i in fileUrl) {
    formData.appendFile(`files`, fileUrl[i]);
  }
  let data = formData.getData();
  console.log(data.contentType, 'data.contentType,');
  console.log(data, 'data.buffer,');
  const header = {
    'Content-Type': data.contentType,
    token: wx.getStorageSync('token')
  }

  return new Promise(function (resolve) {
    // console.log(wx.$config.baseUrl + '/app/basic/file/campaign/upload', ' 附件上传');
    wx.request({
      url: wx.$config.baseUrl + '/app/basic/file/campaign/upload',
      data: data.buffer,
      method: 'post',
      header,
      success: function (res) {
        console.log('res 多个文件上传', res);
        const {
          code,
          data,
          msg
        } = res.data
        console.log('code', code);
        console.log('data', data);
        console.log('msg', msg);
        if (code === 0) {
          resolve(({
            data,
            success: true
          }))
        } else {
          // wx.$loading.showErrorMessage(msg)
          resolve({
            success: false
          })
        }
      },
      fail: function () {
        resolve({
          success: false
        })
        // wx.$loading.showErrorMessage('请求失败')
      }
    })
  })
}

export default wx.$uploadFiles = uploadFiles
