<view class="bottom-bar">
  <view class="cart-btn" bind:tap="onTapCart">
    <view class="iconfont icon-Cart">
      <view wx:if="{{cartCount>0}}" class="count">{{cartCount}}</view>
    </view>
    <text>购物车</text>
  </view>

  <view wx:if="{{isMember===1}}" class="action-btns">

    <basic-button wx:if="{{detail.popType === '商品售罄'}}" bind:click="onTapPop" width="{{550}}" btnState="gray"
      size="large">
      已抢完
    </basic-button>

    <basic-button wx:elif="{{detail.popType === '人群限购'}}" bind:click="onTapPop" width="{{550}}" btnState="gray"
      size="large">
      目前无法满足兑换条件
    </basic-button>
    <block wx:else>
      <basic-button width="{{260}}" btnState="plain" size="large" bind:click="onTapCartAdd">
        加入购物车
      </basic-button>

      <basic-button wx:if="{{currentPoint && currentPoint >= detail.costPoint}}" width="{{260}}" btnState="primary"
        size="large" bind:click="onTapCartExchange">
        立即兑换
      </basic-button>
      <!-- wx:if="{{!currentPoint || currentPoint < detail.costPoint}}"  -->
      <basic-button wx:else width="{{260}}" btnState="gray" size="large">
        <!-- 积分不足目前无法兑换 -->
        积分不足无法兑换
      </basic-button>
    </block>
  </view>
  <view wx:else class="action-btns">
    <basic-button width="{{260}}" btnState="plain" size="large" bind:click="onTapCartAdd">
      加入购物车
    </basic-button>
    <basic-button width="{{260}}" btnState="primary" size="large" bind:click="onTapCartExchange">
      立即兑换
    </basic-button>
  </view>
</view>
