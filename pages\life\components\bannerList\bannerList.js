const app = getApp()
import {
  getBannerList,
  getPromotionProductState
} from '../../../../api/index'
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    content: {
      type: String,
      observer(val) {
        this.handleData(val)
      }
    }
  },
  onLoad() { },
  onShow() { },
  /**
   * 组件的初始数据
   */
  data: {
    list: [],
    autoplay: false,
    duration: 0,
    height: 0,
    width: 0,
    currentIndex: 0, // 当前显示的 swiper 项目索引
    interval: 0,
    circular: false,
    showIndicator: false,
  },
  lifetimes: {
    attached() { }
  },
  methods: {
    // async getData(val) {
    //   console.log('推广为val', val)
    //   if (val) {
    //     const res = await getBannerList({
    //       shelfId: val,
    //     })
    //     if (res.data) {
    //       this.handleData(res.data.content);
    //     }
    //   } else {
    //     this.setData({
    //       list: []
    //     })
    //   }


    // },
    handleData(val) {
      if (val !== '') {
        const v = JSON.parse(val);
        console.log('vaaaaaaaaaaaaaaaaaaaa', v);
        this.setData({
          list: v.list,
          autoplay: v.autoplay,
          duration: v.duration,
          height: v.style.height,
          width: v.style.width,
          interval: v.interval,
          circular: v.circular,
          showIndicator: v.showIndicator,
        })
      } else {
        this.setData({
          list: [],
        })
      }

    },
    // 监听 swiper 滑动变化
    swiperChange(e) {
      const currentIndex = e.detail.current;
      this.setData({
        currentIndex: currentIndex
      });
    },
    handleGo: app.debounce(async function (e) {
      const {
        url,
        item
      } = e.currentTarget.dataset;
      console.log(e, 'eeeeeeeeeeeeeeeeeeeee', 'plptype', url, item);
      if (item.plpType === 1) {
        app.subscribe('order').then(async () => {

          wx.$mp.track({
            event: 'shop_promotion',
            props: {
              shelfProductId: !url ? undefined : url.split('?')[1].split('=')[1],
            }
          })
          if (!url) return;
          const id = url.split('?')[1].split('=')[1];
          const res = await getPromotionProductState({
            shelfProductId: id
          })

          if (res.data !== null) {
            wx.$mp.navigateTo({
              url,
            })
          }
        })
      } else if (item.plpType === 2) {
        this.triggerEvent('change', item);
      } else {
        app.subscribe('order').then(async () => {

          wx.$mp.track({
            event: 'shop_promotion',
            props: {
              shelfProductId: !url ? undefined : url.split('?')[1].split('=')[1],
            }
          })
          if (!url) return;
          const id = url.split('?')[1].split('=')[1];
          const res = await getPromotionProductState({
            shelfProductId: id
          })

          if (res.data !== null) {
            wx.$mp.navigateTo({
              url,
            })
          }
        })
      }

    }, 1500)
  }
})
