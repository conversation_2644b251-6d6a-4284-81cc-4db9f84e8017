// pages/purchaseRecord/purchaseRecord.js
// import dayjs from "../../utils/dayjs.min";
const dayjs = require('../../utils/dayjs.min')
import {
  getPurchaseRecords
} from '../../api/index'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    yearData: new Date().getFullYear(),
    endDate: new Date().getFullYear(),
    startDate: dayjs().subtract(3, 'year').format('YYYY'),
    list: [],
    pageSize: 10,
    pageNum: 1,
    hasMore: false,
    loading: false,

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getList();

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },


  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  changeYear(e) {
    this.setData({
      yearData: e.detail.value,
      pageNum: 1,

    })
    this.getList();
  },
  // 获取任务列表
  async getList() {
    this.setData({
      loading: true
    })
    const {
      yearData,
      pageNum,
      pageSize
    } = this.data;
    const res = await getPurchaseRecords({
      year: yearData,
      pageNum,
      pageSize,
    }).finally(() => {
      this.setData({
        loading: false
      })
    });
    const {
      count,
      hasNextPage
    } = res.data;
    const newData = res.data.list
    let listArr = []
    if (this.data.pageNum == 1) {
      listArr = newData;
    } else {
      listArr = this.data.list.concat(newData);
    }
    this.setData({
      list: listArr || [],
      hasNextPage: hasNextPage,
    })
  },
  onScrollToLower() {
    const {
      list,
      hasNextPage
    } = this.data
    console.log(hasNextPage);
    if (!hasNextPage) return
    let pageNum = Math.ceil(list.length / 10)
    this.setData({
      'pageNum': pageNum + 1
    })
    this.getList()
  }
})
