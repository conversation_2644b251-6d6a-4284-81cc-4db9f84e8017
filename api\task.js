exports.getTaskList = (data) =>
  wx.$request({
    url: "/app/sales/interaction/task/list",
    data,
    method: "get",
  });

// 线下打卡
exports.offLineStoreTask = (data) =>
  wx.$request({
    url: "/app/sales/interaction/task/card",
    data,
    method: "get",
  });

// 首购任务
exports.firstPurchaseTask = () =>
  wx.$request({
    url: "/app/sales/interaction/three/first/order",
    method: "get",
  });

// 任务埋点记录
exports.taskRecords = (data) =>
  wx.$request({
    url: "/app/sales/userTask/add",
    method: "get",
    data,
  });

// 立即打卡
exports.taskSuccessRecordCardButton = (data) =>
  wx.$request({
    url: "/app/sales/interaction/task/card/button",
    data,
    method: "get",
  });

// 判断条件
exports.taskSuccessRecordCardButtonCheck = (data) =>
  wx.$request({
    url: "/app/sales/interaction/task/card/button/check",
    data,
    method: "get",
  });
