import {
  getOrderDetail
} from '../../api/index.js'

import {
  productType
} from '../../utils/contants';

const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,

    detail: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  getData() {
    this.setData({
      loading: true
    })
    const orderCode = this.options.orderCode
    getOrderDetail({
      orderCode
    }).then(res => {
      // debugger
      // isExpire： 1未过期 2已过期
      // status：1未使用 2已使用
      if (res?.data?.productList?.length) {
        res?.data?.productList.forEach(item => {
          item.pdTypeDesc = productType[item.pdType]
          let text = ''
          if (item.pdTypeDesc == '电子券') {
            text = '使用'
          } else if (item.pdTypeDesc == '实物商品') {
            text = '兑换'
          }

          if (item?.status == 3) {
            item.buttonText = '已过期'
          } else if (item?.status == 2) {
            item.buttonText = `已${text}`
          } else if (item?.status == 1) {
            item.buttonText = `到店${text}`
          }
        })
      }
      this.setData({
        detail: res.data
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  onTapUse(e) {
    const detail = e.detail
    // debugger
    wx.$mp.navigateTo({
      url: `/pages/couponDetail/couponDetail?couponId=${detail.couponId}&couponCode=${detail.couponCode}`
    })
  }
})
