import {
  getTemplateByType,
  unreadMessageNum
} from '../../api/index'

const app = getApp()
Page({
  data: {
    loading: false,
    info: {

    },
    isLogin: true,
    userInfo: {},
    rightsList: [{
      icon: 'icon1.png',
      name: '我的兑换',
      id: 1,
      unLock: true,
      path: '/pages/myExchange/myExchange',
      trackData: 'account_my_exchange',

    }, {
      icon: 'icon2.png',
      name: '消费记录',
      id: 2,
      unLock: true,
      path: '/pages/purchaseRecord/purchaseRecord',
      trackData: 'account_expense_record'
    }, {
      icon: 'icon3.png',
      name: '我的礼券',
      id: 3,
      unLock: true,
      path: '/pages/myCoupon/myCoupon',
      trackData: 'account_my_coupon'
    }, {
      icon: 'shop.png',
      // name: '会员卡',
      name: '官方商城',
      id: 4,
      unLock: true,
      path: 'openCard',
      trackData: 'account_member_card'
    }, {
      icon: 'icon5.png',
      name: '联系客服',
      id: 5,
      unLock: true,
      path: 'customerService',
      trackData: 'account_contact_service'
    }, {
      icon: 'icon6.png',
      name: '用户规则',
      id: 6,
      unLock: false,
      path: '/pages/clause/clause',
      trackData: 'account_user_rule'
    }, {
      icon: 'icon7.png',
      name: '我的消息',
      id: 7,
      unLock: false,
      path: '/pages/myMessage/myMessage',
      trackData: 'account_my_message'
    }, {
      icon: 'icon8.png',
      name: '完善信息',
      id: 8,
      unLock: false,
      path: '/pages/register/register?type=edit',
      trackData: 'account_edit_info'
    }],
    showContact: false,
    levelPercent: 0,
    unreadMsgNum: 0,
  },

  async onLoad(options) {
    await this.getInfo()

    let {
      info: {
        pageSetting
      }
    } = this.data
    // 禁止分享
    if (!pageSetting.isShare) {
      wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
    this.setData({
      userInfo: app.globalData.userInfo
    })
    const {
      currentMileage,
      currentLeveLMileage,
      nextLeveLMileage,
      cardLevel
    } = this.data.userInfo;
    let percent = 0;
    if (cardLevel === '4') {
      percent = 100
    } else {
      percent = (currentMileage - currentLeveLMileage) / (nextLeveLMileage - currentLeveLMileage) * 100;
    }
    this.setData({
      levelPercent: percent
    })
  },
  async onShow() {
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4,
        isBirthday: app.globalData.isBirthday
      })
    }
    this.setData({
      showContact: false
    })
    await app.getUserInfo()
    await this.getUnreadMessageNum()
  },
  // 获取模板数据
  getInfo() {
    this.setData({
      loading: true
    })
    return getTemplateByType({
      //  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
      templateType: 1,
      // pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多
      pageType: 5,
    }).then(res => {
      res.data.content = res.data?.content ? JSON.parse(res.data.content) : {}
      res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
      res.data.navSetting = res.data.content.navSetting; // 导航
      res.data.pageSetting = res.data.content.pageSetting; // 页面设置
      res.data.componentSetting = res.data.content.componentSetting; // 组件设置
      this.setData({
        info: res.data
      })
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  // 分享
  goShare(e) {
    let {
      shareTitle,
      shareImg,
      sharePath
    } = e.detail
    this.data.shareInfo = {
      shareTitle,
      shareImg,
      sharePath
    }
  },
  onShareAppMessage() {
    let {
      info: {
        pageSetting
      },
      options
    } = this.data
    // 按钮分享
    if (this.data.shareInfo) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = this.data.shareInfo;
      this.data.shareInfo = ''
      return {
        title: shareTitle || '',
        imageUrl: shareImg || '',
        path: sharePath || ''
      }
    }
    // 页面分享
    return {
      title: pageSetting.shareTitle || '',
      imageUrl: pageSetting.shareImg || '',
      query: {
        id: options.id
      },
      path: pageSetting.sharePath || ''
    }
  },
  // 直接跳转注册页
  goRegister() {
    wx.$mp.track({
      event: 'account_register'
    })
    wx.$mp.navigateTo({
      url: '/pages/register/register'
    })
  },
  // icon点击操作
  handleGo: app.debounce(async function (e) {
    const {
      id,
      url,
      track,
    } = e.currentTarget.dataset;
    wx.$mp.track({
      event: track
    })
    switch (id) {
      case 5: // 联系客户
        this.openContact();
        break;
      case 4: // 会员卡
      // if (app.ifRegister()) {
        // this.openCard();
        wx.$mp.navigateToMiniProgram({
          appId: 'wx7358ece5bf80b319',
          path: 'pages/index/index',
          envVersion: 'release',
          // extraData: {
          //   id: 532
          // }
        })
      // }
     
        break;
      case 6: // 用户规则
        app.goUrl(url);
        break;
      case 1: // 我的兑换;
        if (app.ifRegister()) {
          app.subscribe('order').then(() => {
            app.goUrl(url);
          })
        }
        break;
      case 3: // 我的礼券
        wx.$mp.track({
          event: 'member_coupon_click'
        })
        if (app.ifRegister()) {
          app.subscribe('coupon').then(() => {
            app.goUrl(url);
          })
        }
        break;
      case 2: // 消费记录
        wx.$mp.track({
          event: 'more_consumption_click'
        })
        if (app.ifRegister()) {
          app.goUrl(url);
        }
        break
      default: // 其他的都需要判断用户是否注册
        if (app.ifRegister()) {
          app.goUrl(url);
        }
        break;
    }
  }),
  openContact() {
    this.setData({
      showContact: true,
    })
  },
  closeContact() {
    this.setData({
      showContact: false,
    })
  },
  openCard() { // 打开卡包
    const {
      createAppId,
      createCardId,
      createType,
      cardNo,
    } = this.data.userInfo;
    wx.$mp.navigateToMiniProgram({
      appId: 'wxeb490c6f9b154ef9',
      path: 'pages/card_open/card_open',
      envVersion: 'release',
      extraData: {
        create_card_appid: createAppId,
        card_id: createCardId,
        // card_code: cardNo,
        activate_type: createType, // 指定跳转激活
      },
    })
  },
  async getUnreadMessageNum() {
    const res = await unreadMessageNum();
    this.setData({
      unreadMsgNum: res.data,
    })
  },
  handleGoLegend() {
    wx.$mp.track({
      event: 'member_legend_click'
    })
    if (app.ifRegister()) {
      wx.$mp.navigateTo({
        url: '/pages/myLegend/myLegend',
      })
    }
  },
  handleGoEdit() {
    wx.$mp.navigateTo({
      url: '/pages/register/register?type=edit',
    })
  }
})
