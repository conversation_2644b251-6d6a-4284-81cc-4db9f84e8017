/* pages/purchaseRecord/purchaseRecord.wxss */
.page-content {
  padding: 40rpx;
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
  .top-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding-bottom: 40rpx;
    border-bottom: 1rpx solid #EEEEEE;

    .title {
      height: 45rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #3C3C43;
      line-height: 45rpx;
      letter-spacing: 1px;
      text-align: center;
      font-style: normal;
    }

    .filter-btn {
      width: 160rpx;
      height: 58rpx;
      background: #3C3C43;
      border-radius: 5rpx;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #FFFFFF;
      line-height: 58rpx;
      font-style: normal;
      text-align: center;

      .iconfont {
        display: inline;
      }
    }
  }

}
