const app = getApp();
import { hempInfo, hempConfig } from "../../api/index";
const dayjs = require("../../../utils/dayjs.min");

Page({
  data: {
    statusBarHeight: app.globalData.statusBarHeight + "px",
    navBarHeight: app.globalData.navBarHeight + "px",
    status: -3, //-2 报名已结束 -1报名未开始 0-未报名 1-已报名
  },
  onLoad() {},
  onShow() {
    this.init();
  },
  async init() {
    // 是否已经报名
    await hempInfo().then((res) => {
      if (res.data) {
        wx.$mp.redirectTo({
          url: "/hemp/pages/success/success",
        });
        this.data.status = 1;
        // this.setData({ status: 1 });
      }
    });
    if (this.data.status == 1) {
      return;
    }
    // 没有报名
    if (this.data.status <= 0) {
      hempConfig().then((res) => {
        let { startDate, endDate } = res.data;
        if (
          startDate &&
          dayjs(startDate + " 00:00:00").unix() > dayjs().unix()
        ) {
          this.setData({ status: -1 });
          wx.$mp.showToast({
            title: "报名未开始",
          });
        } else if (
          endDate &&
          dayjs(endDate + " 23:59:59").unix() < dayjs().unix()
        ) {
          this.setData({ status: -2 });
          wx.$mp.showToast({
            title: "报名已结束",
          });
        } else {
          this.setData({ status: 0 });
        }
        // 报名停止 返回上一页
        // if (this.data.status < 0) {
        //   let time = setTimeout(() => {
        //     clearTimeout(time);
        //     time = null;
        //     wx.$mp.navigateBack();
        //   }, 2000);
        // }
      });
    }
  },
  goSign: app.debounce(async function () {
    if (this.data.status == 0) {
      wx.$mp.navigateTo({
        url: "/hemp/pages/enter/enter",
      });
    } else if (this.data.status == -1) {
      wx.$mp.showToast({
        title: "报名未开始",
      });
    } else if (this.data.status == -2) {
      wx.$mp.showToast({
        title: "报名已结束",
      });
    }
  }),
  onShareAppMessage() {
    return {
      title: "邀请参与汉麻新生力设计大赛",
      imageUrl: this.data.$cdn + "/hemp/share.png",
      path: "/hemp/pages/index/index",
    };
  },
});
