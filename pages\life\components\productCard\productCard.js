const app = getApp()
import {
  productTagArray
} from '../../../../utils/contants.js'

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    skuInfo: {
      type: Object,
      value: {},
      observer(val) {
        this.handleVal(val)
      }
    },
  },
  onLoad() {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {
    tagData: undefined
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleGoDetail: app.debounce(async function (e) {
      const {
        id,
        name,
      } = e.currentTarget.dataset;
      wx.$mp.track({
        event: 'shop_product',
        props: {
          productId: id,
          productName: name,
        }
      })
      wx.$mp.navigateTo({
        url: `/pages/integralDetails/integralDetails?productId=${id}`,
      })
    }),
    handleVal(val) {
      const {
        superscript
      } = val;
      if (superscript && superscript.length > 0) {
        const v = productTagArray.find(item => item.value === superscript[0]);
        this.setData({
          tagData: v
        })
      }
    }
  }
})
