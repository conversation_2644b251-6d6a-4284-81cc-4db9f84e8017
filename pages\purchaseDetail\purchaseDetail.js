// pages/purchaseDetail/purchaseDetail.js
import {
  getMuji_orderticket_info
} from '../../api/index'
import {
  CODE128
} from "wxapp-barcode";
const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    orderSn: '',
    loading: false,
    isBackHidden: false,
    details: {},
    barcodeImg: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow(options) {
    this.setData({
      loading: true
    })
    if (options) {
      await this.setData({
        orderSn: options.orderSn
      })
    }
    console.log(options, 'optionsoptionsoptions');
    await this.getDetail()
  },
  getDetail() {

    const {
      orderSn,
      loading
    } = this.data
    getMuji_orderticket_info({
      orderSn: orderSn
    }).then(async res => {
      await this.setData({
        details: res.data
      })
      if (res.data && res.data.orderSn) {
        await this.getCode(res.data.orderSn)
      }


    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },
  getCode(code) {
    CODE128('#barcodeCoupon', code, {
      canvasType: '2d',
      width: 550,
      height: 95
    })

    const query = wx.createSelectorQuery()
    query.select('#barcodeCoupon')
      .fields({
        node: true,
        size: true
      })
      .exec((res) => {
        const canvas = res[0].node
        const ctx = canvas.getContext('2d')

        // 设置canvas大小

        setTimeout(() => {
          wx.canvasToTempFilePath({
            canvas: canvas,
            width: 550,
            height: 95,
            fileType: 'png',
            type: '2D',
            success: (res) => {
              console.log(res, '33333333');

              this.setData({
                barcodeImg: res.tempFilePath
              })
            },
            fail: (err) => {
              console.error(err)
            }
          })
        }, 100)
      })
  },
  toRule: app.debounce(async function (e) {
    wx.$mp.navigateTo({
      url: '/pages/exchangeRules/exchangeRules',
    })
  }),
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
})
