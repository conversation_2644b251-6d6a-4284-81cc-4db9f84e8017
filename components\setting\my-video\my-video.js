const app = getApp();
Component({
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {};
      },
      observer(val) {
        if (val?.id) {
          this.video = wx.createVideoContext("video" + val.id, this);
          this.setData({
            muted: val.muted,
            playing: val.autoplay,
          });
        }
      },
    },
    // 游客模式
    visitor: {
      type: <PERSON><PERSON><PERSON>,
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750,
    },
  },
  data: {
    videoWidth: "", // 视频宽度
    videoHeight: "", // 视频高度
    height: "", // 组件高度
    muted: false,
    playing: false,
    rpx: app.globalData.rpx,
  },
  ready() {
    // this.video = wx.createVideoContext('video' + this.data.data.id, this)
    // console.log(this.video, '-------------')
  },
  lifetimes: {
    attached() {
      let {
        width,
        data: {
          imgWidth,
          imgHeight,
          paddingBottom,
          paddingLeft,
          paddingRight,
          paddingTop,
        },
      } = this.data;
      let videoWidth = width - paddingLeft - paddingRight;
      let videoHeight = parseInt((videoWidth * imgHeight) / imgWidth);
      let height = videoHeight + paddingBottom + paddingTop;
      this.setData({
        videoWidth,
        videoHeight,
        height,
      });
    },
  },
  methods: {
    // 返回顶部
    goTop() {
      this.triggerEvent("goTop");
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent("goAchor", e.detail);
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent("goModal", e.detail);
    },
    // 分享
    goShare(e) {
      let { shareTitle, shareImg, sharePath } = e.detail;
      this.triggerEvent("goShare", {
        shareTitle,
        shareImg,
        sharePath,
      });
    },
    // 播放 暂停
    changePlay: app.debounce(async function () {
      if (this.data.playing) {
        this.video.pause();
      } else {
        this.video.play();
      }
      this.setData({
        playing: !this.data.playing,
      });
    }, 500),
    // 静音
    changeMuted: app.debounce(async function () {
      this.setData({
        muted: !this.data.muted,
      });
    }, 500),
  },
});
