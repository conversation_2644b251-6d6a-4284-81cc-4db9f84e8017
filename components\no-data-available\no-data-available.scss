.empty {
  text-align: center;
  margin-top: var(--top);
  display: flex;
  flex-direction: column;
  align-items: center;

  &-img {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 10rpx;
  }

  &-text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 33rpx;
    text-align: center;
    font-style: normal;

    &.black {
      color: rgba(255, 255, 255, 0.2);
    }
  }

  &-btn {
    margin-top: 40rpx;
    width: 240rpx;
    height: 60rpx;
    background: #3C3C43;
    border-radius: 5rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 22rpx;
    color: #FFFFFF;
    line-height: 30rpx;
    text-align: center;
    font-style: normal;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.mb200 {
  margin-bottom: 200rpx;
}

.pdt200 {
  padding: 305rpx 0 305rpx 0 !important;
  // height: 250rpx;
}

@keyframes ui-loading {
  0% {
    -webkit-transform: scaleY(1);
    transform: scaleY(1)
  }

  50% {
    -webkit-transform: scaleY(.4);
    transform: scaleY(.4)
  }

  to {
    -webkit-transform: scaleY(1);
    transform: scaleY(1)
  }
}

.ui-loading {
  padding: 300rpx 0 0;
  font-size: 0;
  text-align: center;

  &>.dot {
    display: inline-block;
    width: 6rpx;
    height: 30rpx;
    margin: 5rpx;
    font-size: 0;
    background-color: var(--color);
    border-radius: 4rpx;
    -webkit-animation: ui-loading .5s cubic-bezier(.85, .25, .37, .85) 0s infinite;
    animation: ui-loading .5s cubic-bezier(.85, .25, .37, .85) 0s infinite;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
  }

  &>.dot:nth-child(2),
  .ui-loading>.dot:nth-child(4) {
    -webkit-animation-delay: .2s;
    animation-delay: .2s
  }
}

.ui-loading>.dot:first-child,
.ui-loading>.dot:nth-child(5) {
  -webkit-animation-delay: .4s;
  animation-delay: .4s
}
