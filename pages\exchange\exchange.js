const app = getApp()
import {
  getTemplateByType,
  grade_infoList,
  grade_infoInfo,
  usermypoints,
  basicConfigInfo
} from '../../api/index'
import {
  throttle
} from '../../utils/util';
Page({
  data: {
    memberLevel: [], //tabs 切换
    currentLevel: null, //tabs 默认值切换
    expandState: true,
    rightsList: [], //会员权益数组
    currentNameIndex: 0,
    expirePointsNum: null,
    indexMemcer: null
  },
  async onLoad(options) {
    await this.getInfo()
    let {
      info: {
        pageSetting
      }
    } = this.data
    // 禁止分享
    if (!pageSetting.isShare) {
      wx.hideShareMenu({
        menus: ['shareAppMessage', 'shareTimeline']
      })
    }
  },

  async onShow() {
    if (typeof this.getTabBar === 'function' &&
      this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1,
        isBirthday: app.globalData.isBirthday
      })
    }
    // this.setData({
    //   loading: true
    // })
    await app.getUserInfo()
    await this.getConf()
    await this.getList()
  },
  getConf() {
    basicConfigInfo({
      id: 3
    }).then(res => { //获取 生日礼信息
      console.log(app.globalData.userInfo.birthday, '.globalData.userInfo');
      if (!app.globalData.userInfo.birthday && app.globalData.userInfo.isMember == 1) {
        // 获取上次弹窗时间
        // const lastPopupTime = wx.getStorageSync('birthdayPopupTime');
        // const today = new Date().toDateString();
        // console.log(lastPopupTime !== today, lastPopupTime, today);
        console.log(!app.getTodaySub('birthdayPopupTime'));
        // 检查是否今天已经弹过窗
        if (!app.getTodaySub('birthdayPopupTime')) {
          if (res.data.reveiveBallImg && res.data.ballJumpUrl) {
            this.setData({
              ['overallModal.jumpData']: {
                imgUrl: res.data.reveiveBallImg,
                imgLinks: JSON.parse(res.data.ballJumpUrl)
              }
            })
          } else if (res.data.reveiveBallImg) {
            this.setData({
              ['overallModal.jumpData']: {
                imgUrl: res.data.reveiveBallImg,
                imgLinks: []
              }
            })
          }
          // 记录本次弹窗时间
          app.setTodaySub('birthdayPopupTime', 1)
          // wx.setStorageSync('birthdayPopupTime', today);
        }
      }
    })
  },
  memberTo: throttle(async function (e) {
    wx.$mp.track({
      event: 'member_right_click'
    })
    if (app.ifRegister()) {
      app.subscribe('right').then(() => {
        wx.$mp.navigateTo({
          url: '/pages/rightsRecord/rightsRecord',
        })
      })
    }

  }),
  toMember: throttle(async function (e) {
    wx.$mp.track({
      event: 'member_rule_click'
    })
    if (app.ifRegister()) {
      wx.$mp.navigateTo({
        url: '/pages/userAgreement/userAgreement',
        success: (result) => { },
      })
    }

  }),

  getList() { //获取会员列表和处理详情数据
    grade_infoList().then(res => {
      this.setData({
        memberLevel: res.data.map(item => {
          if (item.gradeCode) {
            item.gradeCode = Number(item.gradeCode)
          }
          return item
        }) || [],
      })
      Promise.all(res.data.map((item) => this.getDetail(item.id, item.gradeCode))).then(res1 => {
        console.log(res1, 'res1res1res1res1');
        this.setData({
          rightsList: res1 || []
        })
        // 如果已有选中的等级，保持选中状态
        let targetIndex = this.data.currentLevel;
        if (targetIndex === undefined || targetIndex === -1 || targetIndex === null) {
          // 没有选中的等级时，展示当前会员等级
          targetIndex = this.data.rightsList.findIndex((item) => item.gradeCode === this.data.userInfo.cardLevel);
        }
        console.log(targetIndex);
        this.setData({
          currentNameIndex: Number(app.globalData.userInfo.cardLevel),
          currentLevel: targetIndex,
          indexMemcer: this.data.rightsList.findIndex((item) => item.gradeCode === this.data.userInfo.cardLevel)
        })
      })
    }).finally(() => {
      // this.setData({
      //   loading: false
      // })
    })
    usermypoints({}).then(res => {
      console.log(res, '888888');
      this.setData({
        expirePointsNum: res.data.expirePointsNum
      })
    })
  },
  // 获取会员等级详情
  getDetail(id, code) {
    return new Promise((resolve, reject) => {
      grade_infoInfo({
        id,
        code
      }).then((res) => {
        res.data.activateBenefits =
          res.data.activateBenefits && res.data.activateBenefits.length ?
            res.data.activateBenefits.map((item) => {
              // item.showImg = item.activateImg
              if (item.jumpLink) {

                item.jumpLink = JSON.parse(item?.jumpLink)
              }
              return item
            }) : []
        res.data.unActivateBenefits =
          res.data.unActivateBenefits && res.data.unActivateBenefits.length ?
            res.data.unActivateBenefits.map((item) => {
              // item.showImg = item.unActivateImg
              if (item.jumpLink) {

                item.jumpLink = JSON.parse(item?.jumpLink)
              }
              return item
            }) : []

        res.data.befefits = [...res.data.activateBenefits, ...res.data.unActivateBenefits]
        resolve(res.data)
      })
    })
  },
  popup: app.debounce(async function (e) { //会员权益点击事件
    const {
      item
    } = e.currentTarget.dataset
    // 需要在PC端配置加埋点
    if (item.code) {
      wx.$mp.track({
        event: item.code
      })
    }
    if (app.ifRegister()) {
      if (item.jumpLink) {
        if (item.popupImg) {
          this.setData({
            ['overallModal.jumpData']: {
              imgUrl: item.popupImg,
              imgLinks: item.jumpLink
            }
          })
        } else if (!item.popupImg) {
          if (item.jumpLink[0]['actions'].length > 0 && item.jumpLink[0]['actions'][0]['linkUrl'])
            wx.$mp.navigateTo({
              url: item.jumpLink[0]['actions'][0]['linkUrl'],
            })
        }

      }
      if (item.popupImg && !item.jumpLink) {

        this.setData({
          ['overallModal.jumpData']: {
            imgUrl: item.popupImg,
            imgLinks: []
          }
        })

      }
    }


    console.log(item, '2222222');
  }),



  changeLevel(data) { //切换会员等级变化
    const {
      value,
      index
    } = data.detail;
    // 埋点
    wx.$mp.track({
      event: 'member_' + this.data.rightsList[index].gradeCode + '_click',
      props: {
        gradeName: this.data.rightsList[index].gradeName
      }
    })
    this.setData({
      currentLevel: index,
    })
  },
  expandMemberCard() { //切换收起展开
    const {
      expandState
    } = this.data;
    this.setData({
      expandState: !expandState,
    })
  },
  // 获取模板数据
  getInfo() {
    return getTemplateByType({
      //  templateType	模板类型 1常规页面 2系统页面 3定制页面 4弹窗页面
      templateType: 1,
      // pageType	页面类型 0二级自定义页面 1首页 2兑换 3会员码 4生活圈 5更多
      pageType: 2,
    }).then(res => {
      res.data.content = res.data?.content ? JSON.parse(res.data.content) : {}
      res.data.actionLinks = res.data.content.actionLinks; // 逻辑页面
      res.data.navSetting = res.data.content.navSetting; // 导航
      res.data.pageSetting = res.data.content.pageSetting; // 页面设置
      res.data.componentSetting = res.data.content.componentSetting; // 组件设置
      this.setData({
        info: res.data
      })
    })
  },
  // 分享
  goShare(e) {
    let {
      shareTitle,
      shareImg,
      sharePath
    } = e.detail
    this.data.shareInfo = {
      shareTitle,
      shareImg,
      sharePath
    }
  },
  onShareAppMessage() {
    let {
      info: {
        pageSetting
      },
      options
    } = this.data
    // 按钮分享
    if (this.data.shareInfo) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = this.data.shareInfo;
      this.data.shareInfo = ''
      return {
        title: shareTitle || '',
        imageUrl: shareImg || '',
        path: sharePath || ''
      }
    }
    // 页面分享
    return {
      title: pageSetting.shareTitle || '',
      imageUrl: pageSetting.shareImg || '',
      query: {
        id: options.id
      },
      path: pageSetting.sharePath || ''
    }
  }
})
