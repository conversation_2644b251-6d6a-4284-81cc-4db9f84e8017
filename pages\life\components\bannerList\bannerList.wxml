<view class="promotion-container">
  <!-- swiper 组件 -->
  <block wx:if="{{list.length>1}}">
    <swiper autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" circular="{{circular}}" style="height: {{height}}px;" bindchange="swiperChange">
      <!-- 动态生成 swiper-item -->
      <block wx:for="{{list}}" wx:key="index">
        <swiper-item>
          <image class="swiper-image" style="height: {{height}}px; width: 100%" src="{{item.imgSrc}}" bind:tap="handleGo" data-url="{{item.imgLinks[0].linkUrl}}" data-item="{{item}}"   lazy-load />
        </swiper-item>
      </block>

    </swiper>
    <!-- 自定义指示点的位置 -->
    <view wx:if="{{showIndicator}}" class="indicator-dots">
      <block wx:for="{{list}}" wx:key="index">
        <view class="indicator-dot {{index === currentIndex ? 'active' : ''}}"></view>
      </block>
    </view>
  </block>
  <block wx:elif="{{list.length===1}}">
    <view class="promotion-single-box" style="height: {{height}}px; box-sizing: border-box;     border: 10rpx solid #fafafa; overflow: hidden;">
      <image style="height: {{height}}px; width: 100%; " data-item="{{list[0]}}"  src="{{list[0].imgSrc}}" bind:tap="handleGo" data-url="{{list[0].imgLinks[0].linkUrl}}" lazy-load />
    </view>
  </block>
</view>