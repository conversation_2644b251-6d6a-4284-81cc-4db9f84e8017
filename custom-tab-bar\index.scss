.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  box-shadow: 0px 0px 16rpx 0px rgba(0, 0, 0, 0.04);

  &-visitor {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
  }

  &-main {
    height: inherit;
    display: flex;
    flex-direction: row;
    pointer-events: auto;
    box-sizing: border-box;
    //box-shadow: 0rpx 1rpx 4rpx 0rpx rgba(0, 0, 0, 0.2);
    // border: 1px solid #ededed;
    border-bottom: none;
  }

  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    text-align: center;
    position: relative;

    &.birthday {
      .image {
        width: 56rpx;
        height: 56rpx;
      }
    }

    &.big {
      .image {
        width: 120rpx;
        height: 120rpx;
        margin-top: -60rpx;
      }

      .text {}
    }

    .image {
      position: relative;
      width: 48rpx;
      height: 48rpx;
      flex-shrink: 0;

      &-item {
        width: 100%;
        height: 100%;
      }
    }

    .text {
      margin-top: 10rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      line-height: 28rpx;
    }
  }
}