.filter-bar {
  width: 100%;
  box-sizing: border-box;
  position: relative;
  // height: 150rpx;
  // background-color: #fff;
  z-index: 1;

  .btn-box {
    width: 100%;
    box-sizing: border-box;
    padding: 40rpx 40rpx 40rpx 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10;
    position: relative;
    // background-color: #fff;
  }


  .toggle-btn {
    width: 400rpx;
    height: 70rpx;
    background: #F5F5F5;
    border-radius: 35rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    position: relative;

    .btn-item {
      width: 192rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      font-style: normal;
    }

    .active {
      color: #fff;
      background-color: #000;
      width: 192rpx;
      height: 58rpx;
      line-height: 58rpx;
      text-align: center;
      background: #3C3C43;
      border-radius: 35rpx;
    }
  }

  .dropdown-filter {
    height: 40rpx;
    font-family: PingFangSC,
      PingFang SC,
      MUJIFont2020,
      SourceHanSansCN;
    font-weight: 500;
    font-size: 24rpx;
    color: #3C3C43;
    line-height: 40rpx;
    letter-spacing: 1px;
    text-align: right;
    font-style: normal;
    position: relative;

    image {
      width: 20rpx;
      height: 10rpx;
      position: relative;
      top: -4rpx;
    }
  }

  .dropdown-container {
    padding: 0 40rpx;
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
  }

  .dropdown-content {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 40rpx 0;
    gap: 30rpx;
    border-top: 1px solid #eee;


    .dropdown-item {
      padding: 0 30rpx;
      font-size: 28rpx;
      color: #333;
      height: 60rpx;
      line-height: 60rpx;
      background: #F5F5F5;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      font-style: normal;
    }

    .dropdown-item.active {
      color: #fff;
      background-color: #3c3c3c;
    }
  }

}
