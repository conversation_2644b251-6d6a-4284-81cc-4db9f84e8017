const app = getApp()

const sortData = [{
    label: '默认排序',
    value: 'default',
    selected: true,
    track: 'default',
  },
  {
    label: '积分由低到高',
    value: 1,
    selected: false,
    track: 'up',
  }, {
    label: '积分由高到低',
    value: 2,
    selected: false,
    track: 'down'
  }
]
const selectedLabel = sortData.find(v => v.selected).label
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true,
  },
  properties: {
    // shelfId: {
    //   type: Number,
    //   value: undefined,
    //   observer(shelfId) {
    //     if (!shelfId) return
    //     this.init()
    //   }
    // },
    tabListData: {
      type: Array,
      value: [],
      observer(val) {
        this.handleTab(val)
      }
    },
    showMine: {
      type: Boolean,
    },
    topHeight: {
      type: Number,
      value: app.globalData.navBarHeight,
    },
    filterData: {
      type: Object,
      value: {},
      observer(val) {
        console.log('监听-----------------------', val)
        // 改变样式  默认选中的
        if (val?.oneTagId) {
          this.setData({
            activeTab: val?.oneTagId
          })
        } else if (val?.twoTagIdList) {
          console.log('二级 什么也不做')
        } else {
          this.setData({
            activeTab: 'all'
          })
        }
      }
    },
    showFilterDialog: {
      type: Boolean,
      value: false,
    },

  },
  onLoad() {},
  onShow() {},
  /**
   * 组件的初始数据
   */
  data: {
    // showDialog: false,
    tabList: [{
      label: '全部',
      value: 'all',
    }],
    hasTabList: false,
    activeTab: 'all',
    currentFilterType: 'tag', // 当前搜索类型
    currentDialogContent: [], // 弹出框内容
    currentCheckType: 1, // 判断当前筛选项是多选还是单选，如果单选1则不显示确认按钮，如果多选2则展示确认按钮
    sortData,
    selectedLabel,
    pointList: [
      // { // 条件筛选
      //   label: '全部',
      //   value: 'all',
      //   selected: true,
      // },
      {
        label: '1-100',
        value: '1-100',
        selected: false,
      }, {
        label: '101-1000',
        value: '101-1000',
        selected: false,
      }, {
        label: '1001-3000',
        value: '1001-3000',
        selected: false,
      }, {
        label: '3001-6000',
        value: '3001-6000',
        selected: false,
      }, {
        label: '6001以上',
        value: '6001-99999999',
        selected: false,
      }
    ],
    isMyExchange: false,
  },
  lifetimes: {
    attached() {}
  },
  methods: {
    handleTab(val) {
      const defaultVal = [{
        label: '全部',
        value: 'all',
      }]
      const tabListData = val.map(item => ({
        label: item.tagName,
        value: item.tagId,
        childList: item.children?.map(ele => ({
          label: ele.tagName,
          value: ele.tagId,
          selected: false,
        })) || [],
      }));
      // 更新 tabList 数据
      this.setData({
        hasTabList: !!tabListData.length,
        tabList: defaultVal.concat(tabListData)
      });
      this.triggerEvent('updateTabList');
    },
    // 切换tab，如果父标签的子标签长度>0,则展开筛选框，否则直接传值给父组件
    changeTab: app.debounce(async function (e) {
      console.log(e)
      const {
        value,
        filter,
        label,
      } = e.currentTarget.dataset;
      console.log('value', value);
      console.log('label', label);
      const {
        tabList,
        filterData,
      } = this.data;
      wx.$mp.track({
        event: 'shop_filter_tag',
        props: {
          tagId: value,
          tagName: label,
        }
      })
      const filterValue = filterData;
      // 判断是否需要显示对话框
      let isShowDialog = false;
      const currentTab = tabList.find(item => item.value === value);
      // 如果是有子元素的则要弹窗
      if (currentTab.childList && currentTab.childList.length > 0) {
        isShowDialog = true;
        filterValue.oneTagId = undefined;
        // 如果筛选项里的二级标签选项存在,则需要做数据初始化
        if (filterValue.twoTagIdList && filterValue.twoTagIdList.length > 0) {
          currentTab.childList = currentTab.childList.filter(item => {
            const res = item;
            if (filterValue.twoTagIdList.includes(item.value)) {
              res.selected = true;
            } else {
              res.selected = false;
            }
            return res;
          })
        }
      } else {
        isShowDialog = false;
        filterValue.oneTagId = value;
        filterValue.twoTagIdList = undefined;
        if (value === 'all') {
          filterValue.oneTagId = undefined
        }
        this.triggerEvent('filter', {
          filterValue
        })
      }
      this.triggerEvent('toggleSticky', {
        showFilter: isShowDialog, // 根据条件动态设置
      })
      this.setData({
        activeTab: value,
        currentDialogContent: isShowDialog ? currentTab.childList : [],
        currentCheckType: isShowDialog && 2,
        currentFilterType: filter,
      });
    }, 500),
    closeDialog() {
      // this.setData({
      //   showDialog: false,
      // })
      this.triggerEvent('closeDialog')
      this.triggerEvent('toggleSticky', {
        showFilter: false
      })
    },
    changeFilter(e) {
      const {
        currentDialogContent,
        currentCheckType, // 为2是多选，为1是单选
        filterData,
      } = this.data;
      const {
        label,
        value,
      } = e.currentTarget.dataset;
      let newData;
      if (currentCheckType === 2) {
        newData = currentDialogContent.map((item) => {
          const res = {
            ...item
          };
          if (item.value === value) {
            res.selected = !item.selected
          }
          return res;
        })
        this.setData({
          currentDialogContent: newData,
        })
      }
      // 如果是单选, 则是排序选项
      if (currentCheckType === 1) {
        const currentTrack = sortData.find(item => item.value === value).track;
        wx.$mp.track({
          event: 'shop_filter_sort',
          props: {
            type: currentTrack,
          }
        })
        newData = currentDialogContent.map((item) => {
          const res = {
            ...item
          };
          if (item.value === value) {
            res.selected = true;
          } else {
            res.selected = false
          }
          return res;
        })
        const filterValue = filterData;
        if (value !== 'default') {
          filterValue.sortType = value;
        } else {
          filterValue.sortType = undefined;
        }
        this.setData({
          selectedLabel: label,
          sortData: newData,
        })

        this.triggerEvent('filter', {
          filterValue
        });
        this.closeDialog();
      }
      this.setData({
        currentDialogContent: newData,
      })
    },
    handleReset() {
      const {
        currentFilterType,
        activeTab,
        currentDialogContent,
        filterData,
        tabList,
      } = this.data;
      const filterValue = filterData;
      const resetData = currentDialogContent.map(item => ({
        ...item,
        selected: false
      }))
      this.setData({
        currentDialogContent: resetData,
      });
      switch (currentFilterType) {
        case 'tag':
          filterValue.twoTagIdList = [];
          filterValue.oneTagId = activeTab;
          const tabListData = tabList.map(item => ({
            label: item.label,
            value: item.value,
            childList: item.childList?.map(ele => ({
              label: ele.label,
              value: ele.value,
              selected: false,
            })) || [],
          }));
          this.setData({
            tabList: tabListData,
          })
          break;
        case 'point':
          filterValue.scoreRangeList = undefined;
          this.setData({
            pointList: resetData,
          });
          break;
      }
      this.triggerEvent('filter', {
        filterValue
      });
      this.closeDialog()
    },
    handleConfirm() {
      const {
        currentFilterType,
        currentDialogContent,
        filterData,
        activeTab,
      } = this.data;
      const filterValue = filterData;
      const v = currentDialogContent.filter(item => item.selected);
      switch (currentFilterType) {
        case 'tag':
          const twoTagIdList = v.map(item => item.value);
          if (twoTagIdList.length === 0) {
            filterValue.oneTagId = activeTab;
          } else {
            filterValue.twoTagIdList = v.map(item => item.value);
          }
          break;
        case 'point':
          filterValue.scoreRangeList = v.map(item => ({
            startNum: item.value.split('-')[0],
            endNum: item.value.split('-')[1],
          }))
          wx.$mp.track({
            event: 'shop_filter_point',
            props: {
              range: v.map(item => item.label),
            }
          })
          break;
      }
      this.triggerEvent('filter', {
        filterValue
      });
      this.closeDialog()
    },
    async hanldeToggleFilter(e) {
      const {
        sortData,
        pointList,
        isMyExchange,
        filterData,
      } = this.data;
      const {
        filter,
        check,
      } = e.currentTarget.dataset;
      // 弹窗要展示的内容
      let diaglogContent;
      // 切换的时候做数据初始化
      if (filter === 'sort') {
        diaglogContent = sortData;
        // 处理默认排序
        if (filterData.sortType) {
          // 默认排序是单选
          diaglogContent = sortData.map(item => {
            const res = item;
            if (filterData.sortType === item.value) {
              res.selected = true;
            } else {
              res.selected = false;
            }
            return res;
          })
        }
      } else if (filter === 'point') {
        diaglogContent = pointList;
        if (filterData.scoreRangeList) {
          const startValue = filterData.scoreRangeList.map(item => `${item.startNum}-${item.endNum}`);
          // 处理积分，根据初始积分做处理
          diaglogContent = pointList.map(item => {
            const res = item;
            if (startValue.includes(item.value)) {
              res.selected = true;
            } else {
              res.selected = false
            }
            return res;
          })
        }
      } else if (filter === 'isMyExchange') {
        wx.$mp.track({
          event: 'shop_filter_mine'
        })
        const filterValue = filterData;
        filterValue.isMyExchange = !isMyExchange;
        if (!isMyExchange) {
          filterValue.isMyExchange = 1
        } else {
          filterValue.isMyExchange = undefined;
        }
        this.triggerEvent('filter', {
          filterValue
        })
        diaglogContent = [];
        this.setData({
          isMyExchange: !isMyExchange
        })
      }
      this.triggerEvent('toggleSticky', {
        showFilter: diaglogContent.length > 0
      });
      this.setData({
        currentFilterType: filter,
        currentDialogContent: diaglogContent,
        currentCheckType: check,
      })
    }
  },
})
