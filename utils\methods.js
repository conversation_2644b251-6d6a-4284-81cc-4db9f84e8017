import {
  getUserInfo,
  getSubscribeByScene,
  addSubscribe,
  checkCrowd,
  usercouponreceive,
  subscribeMsgIds,
  homeGiftList,
  offLineStoreTask,
  isMemember,
  getPrivacyPolicy,
  getGiftLeveShow,
} from "../api/index.js";
// import dayjs from "./dayjs.min";
const dayjs = require("./dayjs.min");
// app全局方法添加
export default {
  // 判断路径是否为一级页面
  ifTabbar(url) {
    let path = url.split("?")[0];
    return wx.$config.tabbarPage.some((item) => item.includes(path));
  },
  // 根据 appid 判断是否为本小程序
  ifSelfMini(appid) {
    return !appid || wx.$config.appId == appid;
  },
  // 本小程序页面跳转
  goUrl(url, ifRedirectTo) {
    console.log("跳转url", url);
    // 路径不存在
    if (!url) return;
    // 一级tabbar页面  路径后不能带参数
    if (this.ifTabbar(url)) {
      wx.$mp.switchTab({
        url,
      });
    } else {
      // 普通页面
      if (ifRedirectTo) {
        wx.$mp.redirectTo({
          url,
        });
      } else {
        wx.$mp.navigateTo({
          url,
        });
      }
    }
  },
  // 自定义链接跳转
  goLink(data) {
    console.log("跳转数据", data);
    //     linkName: '', // 名称
    //     linkType: '', // 1-常规页面 2-系统页面 3-定制页面 4-弹窗页面 5-开屏页面 6-H5页面 7-自定义链接
    //     linkUrl: '',  // linkType=1,2,3,5
    //     appid: '', ifHalfScreen-是否半屏拉起   // linkType==7
    //     path: '',     // linkType==7
    //     url: ''       // linkType=6
    let {
      linkType,
      linkUrl,
      appid,
      path,
      url,
      ifRedirectTo, // 是否跳转重定向
      ifHalfScreen, // 是否半屏拉起
      extraData, // 半屏拉起额外传参
    } = data;

    // linkType 1-常规页面 2-系统页面 3-定制页面 4-弹窗页面 5-开屏页面 6-H5页面 7-自定义链接
    if ([1, 2, 3, 5].includes(Number(linkType))) {
      this.goUrl(linkUrl, ifRedirectTo);
    } else if (linkType == 6) {
      this.goUrl(
        `/pages/webView/webView?href=${encodeURIComponent(url)}`,
        ifRedirectTo
      );
    } else if (linkType == 7) {
      // 本小程序跳转
      if (this.ifSelfMini(appid)) {
        this.goUrl(path, ifRedirectTo);
      } else {
        // 其他小程序跳转
        // 半屏拉起
        if (ifHalfScreen) {
          console.log(extraData);
          wx.$mp.openEmbeddedMiniProgram({
            appId: appid,
            path: path,
            // 需要传递给目标小程序的数据，目标小程序可在 App.onLaunch，App.onShow 中获取到这份数据
            extraData: extraData ? JSON.parse(extraData) : {},
          });
        } else {
          wx.$mp.navigateToMiniProgram({
            appId: appid,
            path: path,
          });
        }
      }
    }
  },
  clickHot(data) {
    if (!data) return Promise.reject();
    return new Promise(async (resolve, reject) => {
      try {
        const app = getApp();
        let crowdIds = []; // 符合该用户的人群包ID集合
        let validData = []; // 符合条件的数据
        let { actions, events } = data;
        // 处理事件  再处理行为
        for (let i = 0; i < events.length; i++) {
          let data = events[i];
          let {
            id,
            conditions,
            operateType, // 1-优惠券实时发券  2-订阅消息
            receiveType, // 券类型 1-活动  2-普通
            activityId, // 活动优惠券编码
            couponId, // 普通优惠券编码
            subscribeScene, // 订阅场景
          } = data;
          // 处理条件
          // 处理条件
          let flag = true;
          for (let j = 0; j < conditions.length; j++) {
            // oneValue 1-是否会员  2-会员等级  3-白名单用户(允许) 4-黑名单用户(排除) 5-是否铜级升级会员(type=2)  6-是否银级升级会员(type=3)  7-是否金级升级会员(type=4) 8-是否在生日活动人群包里
            const { oneValue, secondValue } = conditions[j];
            // 判断注册
            if (oneValue == 1 && secondValue) {
              // 需要注册 但是未注册
              if (secondValue == 1 && app.globalData.userInfo.isMember <= 0) {
                // 结束所有条件判断
                flag = false;
                break;
              } else if (
                secondValue == 2 &&
                app.globalData.userInfo.isMember > 0
              ) {
                // 需要未注册  但是已注册
                // 结束所有条件判断
                flag = false;
                break;
              }
            } else if (oneValue == 2 && secondValue.length) {
              // 会员等级不在符合的条件之内 结束所有条件判断
              if (
                !secondValue.includes(
                  String(this.globalData.userInfo.cardLevel)
                )
              ) {
                flag = false;
                break;
              }
            } else if (oneValue == 3 && secondValue) {
              // 白名单
              if (!crowdIds.length) {
                let res = await checkCrowd();
                crowdIds = res.data;
              }
              if (!crowdIds.length || !crowdIds.includes(secondValue)) {
                flag = false;
                break;
              }
            } else if (oneValue == 4 && secondValue) {
              // 黑名单
              if (!crowdIds.length) {
                let res = await checkCrowd();
                crowdIds = res.data;
              }
              if (crowdIds.includes(secondValue)) {
                flag = false;
                break;
              }
            } else if (oneValue == 5 && secondValue) {
              // 是否铜级升级会员
              let { data } = await getGiftLeveShow({ type: 2 });
              // 需要是,但不是 结束判断
              if (secondValue == 1 && !data) {
                // 结束所有条件判断
                flag = false;
                break;
              } else if (secondValue == 2 && data) {
                //   不需要是，但实际上是
                // 结束所有条件判断
                flag = false;
                break;
              }
            } else if (oneValue == 6 && secondValue) {
              // 是否银级升级会员
              let { data } = await getGiftLeveShow({ type: 3 });
              // 需要是,但不是 结束判断
              if (secondValue == 1 && !data) {
                // 结束所有条件判断
                flag = false;
                break;
              } else if (secondValue == 2 && data) {
                //   不需要是，但实际上是
                // 结束所有条件判断
                flag = false;
                break;
              }
            } else if (oneValue == 7 && secondValue) {
              // 是否金级升级会员
              let { data } = await getGiftLeveShow({ type: 4 });
              // 需要是,但不是 结束判断
              if (secondValue == 1 && !data) {
                // 结束所有条件判断
                flag = false;
                break;
              } else if (secondValue == 2 && data) {
                //   不需要是，但实际上是
                // 结束所有条件判断
                flag = false;
                break;
              }
            } else if (oneValue == 8 && secondValue) {
              // 是否生日人群
              let { data } = await getGiftLeveShow({ type: 1 });
              // 需要是,但不是 结束判断
              if (secondValue == 1 && !data) {
                // 结束所有条件判断
                flag = false;
                break;
              } else if (secondValue == 2 && data) {
                //   不需要是，但实际上是
                // 结束所有条件判断
                flag = false;
                break;
              }
            }
          }
          // 满足条件 做事情
          if (flag) {
            try {
              // 发券
              if (operateType == 1) {
                if (receiveType == 1 && activityId) {
                  // 活动券
                  await usercouponreceive({
                    receiveType,
                    activityId,
                  }).then((res) => {
                    wx.showToast({
                      title: "发券成功",
                      icon: "none",
                    });
                  });
                } else if (receiveType == 2 && couponId) {
                  // 普通券
                  await usercouponreceive({
                    receiveType,
                    couponId,
                  }).then((res) => {
                    wx.showToast({
                      title: "发券成功",
                      icon: "none",
                    });
                  });
                }
              } else if (operateType == 2 && subscribeScene?.length) {
                // 订阅消息
                // let canSubscribe = true
                // // 是否全局开启
                // let open = await this.ifmainSwitch()
                // // 未开启需要开弹窗
                // if (!open && modalType == 1) {
                //   // 弹窗提醒 终止判断 结束判断
                //   wx.$mp.getCurrentPage().showOveralModal('subscribe')
                //   canSubscribe = false
                //   resolve([])
                //   return
                // }
                // // 未开启 不需要弹窗  提醒订阅失败  继续走流程
                // if (!open) {
                //   canSubscribe = false
                //   wx.showToast({
                //     title: '订阅失败',
                //     icon: 'none'
                //   })
                // } else { // 有订阅权限 继续判断次数
                //   // 根据整个订阅频次判断
                //   if (frequencyType == 2) {
                //     // 该订阅消息超出了订阅频次
                //     if (!this.getDaysSub('subscribe' + id, openDays, openDays)) {
                //       canSubscribe = false
                //     }
                //   }
                //   // 通过模板订阅次数判断
                let { openType, openNumbers } = app.globalData.subscribeConfig;
                let templateIds = subscribeScene.map((item) => {
                  return {
                    templateId: item,
                  };
                });

                // 需要根据频次判断
                if (openType == 2 && openNumbers) {
                  let res = await subscribeMsgIds({
                    templateIds: subscribeScene.join(","),
                  });
                  templateIds = res.data;
                }

                // 如果失败就终止整个判断
                await this.handleSubscribe(templateIds, id);
              }
            } catch (e) {
              // 事件失败  要结束整个潘丹
              console.log(e, "事件失败");
              resolve([]);
              return;
            }
          }
        }
        // 处理行为
        for (let i = 0; i < actions.length; i++) {
          let data = actions[i];
          let { conditions } = data;
          // 处理条件
          if (conditions.length) {
            // 有条件 要判断
            let flag = true;
            for (let j = 0; j < conditions.length; j++) {
              // oneValue 1-是否会员  2-会员等级  3-白名单用户(允许) 4-黑名单用户(排除) 5-是否铜级升级会员(type=2)  6-是否银级升级会员(type=3)  7-是否金级升级会员(type=4) 8-是否在生日活动人群包里
              const { oneValue, secondValue } = conditions[j];
              // 判断注册  未注册什么也不做
              if (oneValue == 1 && secondValue) {
                // 需要注册 但是未注册
                if (secondValue == 1 && app.globalData.userInfo.isMember <= 0) {
                  // 结束所有条件判断
                  flag = false;
                  break;
                } else if (
                  secondValue == 2 &&
                  app.globalData.userInfo.isMember > 0
                ) {
                  // 需要未注册  但是已注册
                  // 结束所有条件判断
                  flag = false;
                  break;
                }
              } else if (oneValue == 2 && secondValue.length) {
                // 会员等级不在符合的条件之内 结束所有条件判断
                if (
                  !secondValue.includes(
                    String(this.globalData.userInfo.cardLevel)
                  )
                ) {
                  flag = false;
                  break;
                }
              } else if (oneValue == 3 && secondValue) {
                // 白名单
                if (!crowdIds.length) {
                  let res = await checkCrowd();
                  crowdIds = res.data;
                }
                if (!crowdIds.length || !crowdIds.includes(secondValue)) {
                  flag = false;
                  break;
                }
              } else if (oneValue == 4 && secondValue) {
                // 黑名单
                if (!crowdIds.length) {
                  let res = await checkCrowd();
                  crowdIds = res.data;
                }
                if (crowdIds.includes(secondValue)) {
                  flag = false;
                  break;
                }
              } else if (oneValue == 5 && secondValue) {
                // 是否铜级升级会员
                let { data } = await getGiftLeveShow({ type: 2 });
                // 需要是,但不是 结束判断
                if (secondValue == 1 && !data) {
                  // 结束所有条件判断
                  flag = false;
                  break;
                } else if (secondValue == 2 && data) {
                  //   不需要是，但实际上是
                  // 结束所有条件判断
                  flag = false;
                  break;
                }
              } else if (oneValue == 6 && secondValue) {
                // 是否银级升级会员
                let { data } = await getGiftLeveShow({ type: 3 });
                // 需要是,但不是 结束判断
                if (secondValue == 1 && !data) {
                  // 结束所有条件判断
                  flag = false;
                  break;
                } else if (secondValue == 2 && data) {
                  //   不需要是，但实际上是
                  // 结束所有条件判断
                  flag = false;
                  break;
                }
              } else if (oneValue == 7 && secondValue) {
                // 是否金级升级会员
                let { data } = await getGiftLeveShow({ type: 4 });
                // 需要是,但不是 结束判断
                if (secondValue == 1 && !data) {
                  // 结束所有条件判断
                  flag = false;
                  break;
                } else if (secondValue == 2 && data) {
                  //   不需要是，但实际上是
                  // 结束所有条件判断
                  flag = false;
                  break;
                }
              } else if (oneValue == 8 && secondValue) {
                // 是否生日人群
                let { data } = await getGiftLeveShow({ type: 1 });
                // 需要是,但不是 结束判断
                if (secondValue == 1 && !data) {
                  // 结束所有条件判断
                  flag = false;
                  break;
                } else if (secondValue == 2 && data) {
                  //   不需要是，但实际上是
                  // 结束所有条件判断
                  flag = false;
                  break;
                }
              }
            }
            // 如果未终止符合条件
            if (flag) {
              validData.push(data);
            }
          } else {
            // 没有条件直接符合
            validData.push(data);
          }
        }
        resolve(validData);
      } catch (err) {
        resolve([]);
      }
    });
  },
  // 判断订阅消息总开关
  ifmainSwitch() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        withSubscriptions: true,
        success(res) {
          console.log(res);
          // 没有开总开关
          if (res.subscriptionsSetting?.mainSwitch === false) {
            resolve(false);
          } else {
            resolve(true);
          }
        },
        fail() {
          resolve(false);
        },
      });
    });
  },
  // 场景订阅消息
  subscribe(scene) {
    return new Promise(async (resolve, reject) => {
      getSubscribeByScene({
        scene,
      })
        .then((result) => {
          resolve(this.handleSubscribe(result.data, scene));
        })
        .catch(() => {
          // 订阅消息报错
          reject();
        });
    });
  },
  // 订阅消息 并处理订阅结果
  handleSubscribe(data = [], id) {
    let templateIds = data.map((item) => item.templateId);
    console.log(templateIds, "订阅模板");
    const app = getApp();

    return new Promise(async (resolve, reject) => {
      // 没有真正的订阅消息
      if (!templateIds?.length) {
        resolve();
        return;
      }
      // 处理订阅消息频次问题  app.globalData.subscribeConfig
      let {
        modalType,
        noOpenType,
        noOopenDays,
        noOpenFrequency,
        frequencyType,
        openDays,
        openFrequency,
        openType,
        openNumbers,
      } = app.globalData.subscribeConfig;

      // modalType 1-弹窗提醒 2-直接跳过(toast提示即可)
      // noOpenType 1-每次提醒  2-频次提醒  noOopenDays天noOpenFrequency次
      const currentInstance = wx.$mp.getCurrentPage();
      // 判断是否开启订阅权限  是否需要权限弹窗提醒
      // // 判断订阅消息总开关
      let mainSwitch = await this.ifmainSwitch();
      // 没有打开权限
      if (!mainSwitch) {
        // 不需要提醒 直接跳过
        if (modalType == 2) {
          wx.showToast({
            title: "订阅失败",
            icon: "none",
          });
          resolve();
          return;
        } else {
          // 需要提醒
          // 每次弹窗提醒
          if (noOpenType == 1) {
            currentInstance.showOveralModal("subscribe");
            reject();
            return;
          } else {
            // 根据频次提醒
            if (this.getDaysSub("allSubscribe", noOopenDays, noOpenFrequency)) {
              this.setDaysSub("allSubscribe");
              currentInstance.showOveralModal("subscribe");
              reject();
              return;
            } else {
              wx.showToast({
                title: "订阅失败",
                icon: "none",
              });
              resolve();
              return;
            }
          }
        }
      }

      // frequencyType 1-每次点击  2-根据频率点击 openDays 天  openFrequency 次
      // openType 1-不限制  2-根据 openNumbers 次数判断

      // 根据订阅模板次数判断是否需要订阅
      if (openType == 2 && openNumbers) {
        let canSubscribe = data.some((item) => item.subNumber < openNumbers);
        if (!canSubscribe) {
          resolve();
          return;
        }
      }
      // 这里和上面是且的关系  不能改为else if
      if (frequencyType == 2) {
        // 符合条件订阅
        if (this.getDaysSub("subscribe" + id, openDays, openFrequency)) {
          this.setDaysSub("subscribe" + id);
        } else {
          // 不符合条件
          resolve();
          return;
        }
      }

      wx.requestSubscribeMessage({
        tmplIds: templateIds,
        success(res) {
          console.log(res, "订阅结果");
          // 将订阅结果保存下来
          let acceptTemplateIds = [];
          templateIds.forEach((item) => {
            if (res[item] == "accept") {
              acceptTemplateIds.push(item);
            }
          });
          if (acceptTemplateIds.length) {
            addSubscribe(acceptTemplateIds);
          }
          resolve(res);
        },
        fail(err) {
          resolve();
        },
      });
    });
  },

  // 获取 今日是否已订阅
  getTodaySub(scene) {
    const today = dayjs().format("YYYY-MM-DD");
    return !!wx.getStorageSync(`sub_${today}_${scene}`);
  },

  // 设置 今日是否已订阅
  setTodaySub(scene, val) {
    const today = dayjs().format("YYYY-MM-DD");
    wx.setStorageSync(`sub_${today}_${scene}`, val);
  },
  // 设置场景的显示时间
  setDaysSub(scene) {
    const today = dayjs().format("YYYY-MM-DD");
    let value = wx.getStorageSync(scene);
    if (!value) {
      value = [];
    }
    value.push(today);
    wx.setStorageSync(scene, value);
  },
  // 获取场景的显示时间,并判断是否符合条件
  getDaysSub(scene, days, num) {
    const today = dayjs();
    let value = wx.getStorageSync(scene);
    if (!value) {
      value = [];
    }
    let validDays = value.filter((item) => {
      return today.diff(dayjs(item), "day") <= days;
    });
    // 符合条件
    if (validDays.length < num) {
      return true;
    }
    // 不符合条件
    return false;
  },

  // 判断是否注册  未注册弹窗提示
  ifRegister() {
    const app = getApp();
    let result = app.globalData.userInfo.isMember > 0;
    // 已经注册
    if (result) {
      return true;
    } else {
      // 未注册弹窗提醒
      if (app.globalData.giftData) {
        wx.$mp
          .getCurrentPage()
          .showOveralModal("jumpData", app.globalData.giftData);
      } else {
        homeGiftList().then((res) => {
          app.globalData.giftData = {
            imgUrl: res.data.ballImg,
            imgLinks: JSON.parse(res.data.jumpUrl),
          };
          wx.$mp
            .getCurrentPage()
            .showOveralModal("jumpData", app.globalData.giftData);
        });
      }
      return false;
    }
  },
  // 获取用户信息
  async getUserInfo() {
    const app = getApp();
    try {
      // 优先获取隐私协议
      if (!app.globalData.privacyData?.id) {
        await app.getPrincy();
      }
      // 获取用户信息
      const res = await getUserInfo();
      // 默认昵称
      res.data.username = res.data.username || "亲爱的良友";
      // // CRM用户昵称返回无印良品会员时，小程序展示为：亲爱的良友
      // if (res.data.username == '无印良品会员') {
      //   res.data.username = '亲爱的良友'
      // }
      // 线上默认gneder值为3 清空
      res.data.gender = [1, 2].includes(res.data.gender)
        ? res.data.gender
        : null;

      // 如果不是会员 则判断是不是老系统的会员
      if (res.data.originIsMember === 0) {
        const loginRes = await wx.login();

        if (loginRes.code) {
          const memberRes = await isMemember({
            code: loginRes.code,
          });

          // 如果是旧系统的会员，则进行重新绑定
          if (memberRes.data.originIsMember === 1) {
            // 获取并更新用户信息
            const updatedUserInfo = await getUserInfo();
            // 默认昵称
            updatedUserInfo.data.username =
              updatedUserInfo.data.username || "亲爱的良友";
            // // CRM用户昵称返回无印良品会员时，小程序展示为：亲爱的良友
            // if (updatedUserInfo.data.username == '无印良品会员') {
            //   updatedUserInfo.data.username = '亲爱的良友'
            // }
            // 线上默认gneder值为3 清空
            updatedUserInfo.data.gender = [1, 2].includes(
              updatedUserInfo.data.gender
            )
              ? updatedUserInfo.data.gender
              : null;
            app.globalData.userInfo = updatedUserInfo.data;
          } else {
            app.globalData.userInfo = res.data;
          }
        }
      } else {
        app.globalData.userInfo = res.data;
      }

      // 判断当前是否是用户的生日月
      if (app.globalData.userInfo.birthday) {
        const birthday = dayjs(app.globalData.userInfo.birthday);
        const today = dayjs();
        console.log(today.month(), birthday.month());
        if (today.month() === birthday.month()) {
          app.globalData.isBirthday = true;
        }
      } else {
        app.globalData.isBirthday = false;
      }

      // 埋点数据
      app.globalData.identities = [
        {
          type: "wxa.openId", // ⼩程序的openeid
          id: app.globalData.userInfo.openid,
        },
        {
          type: "wechat.unionId",
          id: app.globalData.userInfo.unionid, // 开放平台的unionid
        },
        {
          type: "member_id",
          id: app.globalData.userInfo.cardNo, // 用户的memberCode
        },
      ];
      app.globalData.profile = {
        // nickname: app.globalData.userInfo.username || '',
        // props: { // ⾃定义⽤⼾属性
        //   unionid: app.globalData.userInfo.unionid,
        //   openid: app.globalData.userInfo.openid,
        //   cardNo: app.globalData.userInfo.cardNo,
        //   channelOne: app.globalData.channelOne, // 渠道1
        //   channelTwo: app.globalData.channelTwo, // 渠道2
        // }
      };
      // 埋点数据
      // 只有正式环境上传
      if (wx.$config.env === "prod") {
        app.muji_sdk.setIdentities(app.globalData.identities);
        app.muji_sdk.setProfile(app.globalData.profile);
      }
    } catch (error) {
      console.error("获取用户信息失败", error);
    }
  },

  // 富文本变量替换
  handleContent(content) {
    let app = getApp();
    // 处理用户信息变量参数
    const USER_INFO_VARIABLE = wx.$contants.USER_INFO_VARIABLE;
    const userInfo = app.globalData.userInfo;
    content = USER_INFO_VARIABLE.reduce((total, current) => {
      if (current.name == "称谓") {
        return total.replaceAll(
          `##${current.name}##`,
          (userInfo[current.key] || "")[("", "先生", "女士")]
        );
      }
      return total.replaceAll(
        `##${current.name}##`,
        userInfo[current.key] || ""
      );
    }, content);
    // 处理字体大小
    let rpx = app.globalData.rpx;
    var regex = /font-size:\s*(\d+)(?=px)/g;
    content = content.replace(regex, (matched, fontSize) => {
      const result = fontSize + "r";
      return matched.replace(fontSize, result);
    });
    // 处理pt单位
    var regex = /\s*(\d+pt)/g;
    content = content.replace(regex, (matched, pt) => {
      const result = (Number(pt.slice(0, -2)) * rpx).toFixed(2) + "px";
      return matched.replace(pt, result);
    });
    // 处理 ul 边距
    var regex = /<ul>/g;
    content = content.replace(regex, (pt) => {
      return `<ul style="padding-left:${(40 * rpx).toFixed(2)}px">`;
    });
    // 处理字体设置
    var fontFamilyRegex = /font-family:\s*[^;]+;/g;
    content = content.replace(fontFamilyRegex, (match) => {
      const existingFonts = match
        .replace(/font-family:\s*|;$/g, "")
        .split(",")
        .map((font) => font.trim());
      // , "'MUJIFont2020'" 这个字体的数字1和数字2345宽度不一致，导致内容无法对齐，所以去掉。
      // 去掉之后，即使在开发者工具中是对齐的，但是在真机上依然还是对不齐。几乎所有的字体都存在这个问题，所以无需去掉。
      const newFonts = [
        "'MUJIFont2020'",
        "SourceHanSansCN",
        "'PingFang SC'",
        "'思源黑体'",
        "'Helvetica Neue'",
        "Helvetica",
        "'Hiragino Sans GB'",
        "'Microsoft YaHei'",
        "'微软雅黑'",
        "Arial",
        "sans-serif",
      ];
      const filteredFonts = newFonts.filter(
        (font) => !existingFonts.includes(font)
      );
      return `${match.replace(/;$/, "")}, ${filteredFonts.join(", ")};`;
    });
    // 处理图片的宽和高 width="233"
    var width = /width="(\d+)(?=")/g;
    content = content.replace(width, (matched, fontSize) => {
      const result = (Number(fontSize) * rpx).toFixed(2);
      return matched.replace(fontSize, result);
    });
    var height = /height="(\d+)(?=")/g;
    content = content.replace(height, (matched, fontSize) => {
      const result = (Number(fontSize) * rpx).toFixed(2);
      return matched.replace(fontSize, result);
    });
    // 处理其他参数信息
    return content;
  },
  // 进入页面自动获取定理授权 获取经纬度
  autoAuthLocation() {
    const app = getApp();
    return new Promise((resolve, reject) => {
      // 已经在其他地方获取到了经纬度直接返回
      if (app.globalData.userLatitude) {
        resolve({
          latitude: app.globalData.userLatitude,
          longitude: app.globalData.userLongitude,
        });
        return;
      }
      wx.getSetting({
        success: (res) => {
          // 判断用户是否授权了位置信息
          const isAuthorized = res.authSetting["scope.userLocation"];
          // 从未授权 弹窗授权
          if (isAuthorized === undefined) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                reject({
                  latitude: 0,
                  longitude: 0,
                });
              },
            });
          } else if (isAuthorized === false) {
            // 之前拒绝过  弹窗提醒
            // 一天只弹一次弹窗
            if (!app.getTodaySub("authLocation")) {
              app.setTodaySub("authLocation", 1);
              wx.$mp.getCurrentPage().showOveralModal("store");
            }
            // 用户曾经开启过但拒绝了授权
            reject({
              latitude: 0,
              longitude: 0,
            });
          } else if (isAuthorized === true) {
            // 已经授权过  直接获取
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                reject({
                  latitude: 0,
                  longitude: 0,
                });
              },
            });
          }
        },
        fail: (error) => {
          reject({
            latitude: 0,
            longitude: 0,
          });
        },
      });
    }).then((res) => {
      // offLineStoreTask({
      //   latitude: res.latitude,
      //   longitude: res.latitude,
      // })
      // 全局保存最新的用户所在经纬度
      app.globalData.userLatitude = res.latitude;
      app.globalData.userLongitude = res.longitude;
      return res;
    });
  },
  // 点击开启授权按钮 获取定理授权 获取经纬度  或者打开弹窗
  manualAuthLocation() {
    const app = getApp();
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          // 判断用户是否授权了位置信息
          const isAuthorized = res.authSetting["scope.userLocation"];
          if (isAuthorized === undefined) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                // 失败了弹窗提示
                wx.$mp.getCurrentPage().showOveralModal("store");
              },
            });
          } else if (isAuthorized === false) {
            // 用户曾经开启过但拒绝了授权
            wx.$mp.getCurrentPage().showOveralModal("store");
          } else if (isAuthorized === true) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                console.error("获取位置失败:", error); // 打印错误信息
                reject({
                  latitude: 0,
                  longitude: 0,
                });
              },
            });
          }
        },
        fail: (error) => {
          reject({
            latitude: 0,
            longitude: 0,
          });
        },
      });
    }).then((res) => {
      // 全局保存最新的用户所在经纬度
      app.globalData.userLatitude = res.latitude;
      app.globalData.userLongitude = res.longitude;
      return res;
    });
  },

  // 点击立即开启, 判断用户是否开启过，如果未开启直接跳转到设置页，如果已开启直接获取经纬度（搜索页）
  openAuthLocation() {
    const app = getApp();
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          // 判断用户是否授权了位置信息
          const isAuthorized = res.authSetting["scope.userLocation"];
          if (isAuthorized === undefined) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                // console.log('error', error);
                reject({
                  latitude: 0,
                  longitude: 0,
                });
              },
            });
          } else if (isAuthorized === false) {
            // 用户曾经开启过但拒绝了授权
            wx.$mp.openSetting().then((v) => {
              if (v.isLocationAuthorized) {
                wx.getLocation({
                  type: "gcj02", // 返回经纬度（gcj02坐标系）
                  success: (v) => {
                    resolve({
                      latitude: v.latitude,
                      longitude: v.longitude,
                    });
                  },
                  fail: (error) => {
                    console.error("获取位置失败:", error); // 打印错误信息
                    reject({
                      latitude: 0,
                      longitude: 0,
                    });
                  },
                });
              }
            });
          } else if (isAuthorized === true) {
            wx.getLocation({
              type: "gcj02", // 返回经纬度（gcj02坐标系）
              success: (v) => {
                resolve({
                  latitude: v.latitude,
                  longitude: v.longitude,
                });
              },
              fail: (error) => {
                console.error("获取位置失败:", error); // 打印错误信息
                reject({
                  latitude: 0,
                  longitude: 0,
                });
              },
            });
          }
        },
        fail: (error) => {
          reject({
            latitude: 0,
            longitude: 0,
          });
        },
      });
    }).then((res) => {
      // 全局保存最新的用户所在经纬度
      app.globalData.userLatitude = res.latitude;
      app.globalData.userLongitude = res.longitude;
      return res;
    });
  },

  // 节流
  debounce(fn, delay = 1000) {
    let timer = null;
    return function () {
      var args = arguments;
      if (!timer) {
        //如果定时器还在，说明前一次延迟执行还没有完成
        fn.apply(this, args);
        timer = setTimeout(function () {
          //延迟一段时间才允许下一次执行
          clearTimeout(timer);
          timer = null;
        }, delay);
      }
    };
  },
  // 场景订阅消息 有拒绝返回的
  subscribe2(scene) {
    return new Promise(async (resolve, reject) => {
      getSubscribeByScene({
        scene,
      })
        .then((result) => {
          resolve(this.handleSubscribe2(result.data, scene));
        })
        .catch((err) => {
          // 订阅消息报错
          reject(err);
        });
    });
  },
  // 订阅消息 并处理订阅结果 返回订阅信息
  handleSubscribe2(data = [], id) {
    let templateIds = data.map((item) => item.templateId);
    console.log(templateIds, "订阅模板 2");
    const app = getApp();

    return new Promise(async (resolve, reject) => {
      // 没有真正的订阅消息
      if (!templateIds?.length) {
        resolve();
        return;
      }
      // 处理订阅消息频次问题  app.globalData.subscribeConfig
      let {
        modalType,
        noOpenType,
        noOopenDays,
        noOpenFrequency,
        frequencyType,
        openDays,
        openFrequency,
        openType,
        openNumbers,
      } = app.globalData.subscribeConfig;

      // modalType 1-弹窗提醒 2-直接跳过(toast提示即可)
      // noOpenType 1-每次提醒  2-频次提醒  noOopenDays天noOpenFrequency次
      const currentInstance = wx.$mp.getCurrentPage();
      // 判断是否开启订阅权限  是否需要权限弹窗提醒
      // // 判断订阅消息总开关
      let mainSwitch = await this.ifmainSwitch();
      // 没有打开权限
      if (!mainSwitch) {
        // 不需要提醒 直接跳过
        if (modalType == 2) {
          wx.showToast({
            title: "订阅失败",
            icon: "none",
          });
          resolve();
          return;
        } else {
          // 需要提醒
          // 每次弹窗提醒
          if (noOpenType == 1) {
            currentInstance.showOveralModal("subscribe");
            reject();
            return;
          } else {
            // 根据频次提醒
            if (this.getDaysSub("allSubscribe", noOopenDays, noOpenFrequency)) {
              this.setDaysSub("allSubscribe");
              currentInstance.showOveralModal("subscribe");
              reject();
              return;
            } else {
              wx.showToast({
                title: "订阅失败",
                icon: "none",
              });
              resolve();
              return;
            }
          }
        }
      }

      // frequencyType 1-每次点击  2-根据频率点击 openDays 天  openFrequency 次
      // openType 1-不限制  2-根据 openNumbers 次数判断

      // 根据订阅模板次数判断是否需要订阅
      if (openType == 2 && openNumbers) {
        let canSubscribe = data.some((item) => item.subNumber < openNumbers);
        if (!canSubscribe) {
          resolve();
          return;
        }
      }
      // 这里和上面是且的关系  不能改为else if
      if (frequencyType == 2) {
        // 符合条件订阅
        if (this.getDaysSub("subscribe" + id, openDays, openFrequency)) {
          this.setDaysSub("subscribe" + id);
        } else {
          // 不符合条件
          resolve();
          return;
        }
      }

      wx.requestSubscribeMessage({
        tmplIds: templateIds,
        success(res) {
          console.log(res, "订阅结果");
          res = {
            ...res,
            templateIds,
          };
          // 将订阅结果保存下来
          let acceptTemplateIds = [];
          templateIds.forEach((item) => {
            if (res[item] == "accept") {
              acceptTemplateIds.push(item);
            }
          });
          if (acceptTemplateIds.length) {
            addSubscribe(acceptTemplateIds);
          }
          resolve(res);
        },
        fail(err) {
          resolve();
        },
      });
    });
  },

  // 获取隐私协议数据
  async getPrincy() {
    try {
      const app = getApp();
      // 获取用户隐私协议
      let res = await getPrivacyPolicy();
      app.globalData.privacyData = res.data;

      // 小程序隐私协议是否更新
      let needAuthorization = await new Promise((resolve) => {
        // 版本底不支持
        if (wx.getPrivacySetting) {
          wx.getPrivacySetting({
            success: (res) => {
              console.log(res, "隐私保护指引结果"); // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
              resolve(res.needAuthorization);
            },
            fail: () => {
              resolve(false);
            },
          });
        } else {
          resolve(false);
        }
      });
      app.globalData.needAuthorization = needAuthorization;
      return Promise.resolve();
    } catch (err) {
      console.log(err);
      return Promise.resolve();
    }
  },
};
