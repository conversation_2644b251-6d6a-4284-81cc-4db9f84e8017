const app = getApp()
import {
  throttle
} from '../../../../utils/util'
import {
  storeTypeList
} from '../../../../utils/contants'


Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    outletData: {
      type: Object,
      value: {},
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    storeTag: '',
  },
  lifetimes: {
    attached() {
      const {
        type
      } = this.data.outletData;
      this.setData({
        storeTag: storeTypeList[type] || '',
      })
    },
  },


  /**
   * 组件的方法列表
   */
  methods: {
    // 拨打电话
    callTel: app.debounce(async function (e) {
      let num = e.currentTarget.dataset.num;
      let pattern = /[^u4e00-u9fa5]/g
      num = num.replace(pattern, '')
      wx.makePhoneCall({
        phoneNumber: num,
        success: function () {
          console.log("拨打电话成功！")
        },
        fail: function () {
          console.log("拨打电话失败！")
        }
      })
    }),
    handleClick(e) {
      const {
        id
      } = e.currentTarget.dataset;
      this.triggerEvent('changLocation', {
        id
      })
    },
    goGuide: app.debounce(async function (e) {
      if (app.ifRegister()) {
        const {
          value,
        } = e.currentTarget.dataset;
        wx.$mp.navigateTo({
          url: `/pages/guide/guide?id=${value}`,
        })
      }
    }),
    openLocation({
      currentTarget: {
        dataset: {
          store
        }
      }
    }) {
      if (!store) return;
      wx.openLocation({
        longitude: Number(store.longitude),
        latitude: Number(store.latitude),
        scale: 18,
        name: store.storeName,
        address: store.storeAddress,
        fail: (e) => {
          console.log('openLocation fail', e)
        }
      })
    },
  },

})
