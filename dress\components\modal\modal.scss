// button {
// 	padding: 0;
// 	margin: 0;
// 	background-color: transparent;
// 	border: none;
// 	border-radius: 0;

// 	&::after {
// 		border: none;
// 	}
// }
.ruleModal {
  max-height: 1070rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  // padding: 60rpx 30rpx 60rpx 60rpx;
  overflow: hidden;


  &-title {
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    padding: 60rpx 60rpx 40rpx 60rpx;
    font-family: NotoSansHans;
    font-weight: 500;
    font-size: 36rpx;
    color: #3C3C43;
    line-height: 54rpx;
    letter-spacing: 1rpx;
  }






  &-bottom {
    width: 100%;
    padding-left: 60rpx;
    padding-right: 60rpx;
    width: 100%;
    box-sizing: border-box;
  }

  &-other {
    flex: 1;
    width: 100%;
    padding-left: 60rpx;
    padding-right: 30rpx;
    box-sizing: border-box;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  &-rules {
    max-height: 100%;
    flex-shrink: 0;

    &-box {
      display: flex;
      flex-direction: column;
    }

    &-item {
      padding-right: 30rpx;
      box-sizing: border-box;
      width: 100%;
      display: block;
    }
  }

  &-button {
    flex-shrink: 0;
    box-sizing: border-box;
    margin: 0 auto;

    display: flex;
    justify-content: center;
    align-items: center;
    width: 510rpx;
    height: 80rpx;
    background: #3C3C43;
    border-radius: 5rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #FFFFFF;
    margin-top: 60rpx;

    &.disabled {
      opacity: 0.3;
    }
  }
}
