@import "assets/scss/common";
@import "assets/scss/config";

.register {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;

  .register-form {
    margin: 0 var(--page-margin);

    .avatar-item {
      padding-top: 30rpx;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 208rpx;
      align-items: center;
      margin-bottom: 60rpx;

      .avatar-box {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        position: relative;
        overflow: visible;

        .avatar-img {
          width: 120rpx;
          height: 120rpx;
          border-radius: 60rpx;
        }

        .auth-avatar {
          width: 40rpx;
          height: 40rpx;
          position: absolute;
          bottom: 0;
          right: 0;
          border-radius: 20rpx;
        }
      }



      .avatar-desc {
        margin-top: 30rpx;
        font-size: 28rpx;
        color: var(--text-black-color);
        height: 40rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #3C3C43;
        line-height: 40rpx;
        letter-spacing: 1px;
        text-align: center;
        font-style: normal;
      }
    }

    .form-item {
      border-bottom: 1rpx solid #dbd8d9;
      // height: 114rpx;
      margin-bottom: 60rpx;
      padding-bottom: 22rpx;

      .item-label {
        color: var(--text-gray-color);
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        line-height: 33rpx;
        text-align: left;
        font-style: normal;
      }

      .auth-phone {
        text-align: left;
        line-height: 28rpx;
        position: relative;
        margin-top: 20rpx;
        color: var(--text-black-color);
        font-size: 28rpx;
        display: flex;
        flex-direction: row-reverse;
        background-color: #fff;

        &:active,
        &:focus,
        &:hover {
          background-color: #fff;
        }

        .absolute-box {
          position: absolute;
          bottom: 0;
          left: 0;
          color: var(--text-black-color);
        }

        .relative-box {
          position: relative;
          text-align: left;
          width: 100%;
          margin-top: 20rpx;
        }

        .auth-btn {
          width: 200rpx;
          height: 60rpx;
          background: #F5F5F5;
          border-radius: 5rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 24rpx;
          color: #3C3C43;
          line-height: 60rpx;
          text-align: center;
          font-style: normal;
        }

        .without-data {
          color: var(--text-placeholder-color);
        }
      }

      .item-content {
        margin-top: 20rpx;
        color: var(--text-black-color);
        font-size: 28rpx;
        display: flex;
        justify-content: space-between;

        .radio-group {
          display: flex;
          font-size: 28rpx;
          color: var(--text-black-color);
          right: 30rpx;

          .radio-item {
            display: flex;
            align-items: center;
            font-family: MUJIFont2020,
              MUJIFont2020;
            font-weight: 400;
            font-size: 28rpx;
            line-height: 40rpx;
            letter-spacing: 1px;
            text-align: left;
            font-style: normal;
            height: 28rpx;
            position: relative;

            &:first-child {
              margin-right: 60rpx;
            }

            .radio {
              border-radius: 50%;
              margin-right: 20rpx;
              font-size: 28rpx;
              color: #3C3C43;
            }

            .active {
              border: 1rpx solid var(--btn-primary);
              position: relative;
            }

            .active::before {
              content: '';
              width: 20rpx;
              height: 20rpx;
              background: var(--btn-primary);
              display: block;
              border-radius: 50%;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }

        .item-text {
          flex: 1;
        }

        .icon-button {
          height: 36rpx;
          width: 36rpx;
        }

        .auth-btn {
          width: 200rpx;
          height: 60rpx;
          background: #F5F5F5;
          border-radius: 5rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          font-size: 24rpx;
          color: #3C3C43;
          line-height: 60rpx;
          text-align: center;
          font-style: normal;
        }

        .item-input {
          height: 28rpx;
          font-family: MUJIFont2020,
            MUJIFont2020;
          font-weight: 400;
          font-size: 28rpx;
          color: #3C3C43;
          letter-spacing: 1px;
          text-align: left;
          font-style: normal;

          &::placeholder {
            color: var(--txt-placeholder-color);
            font-weight: 400;
          }
        }

        .without-data {
          color: var(--text-placeholder-color);
        }
      }
    }
  }

  .item-value {
    &.disabled {
      color: #888;
    }
  }

}

.tips-content {
  margin-bottom: 20rpx;
  width: 510rpx;

  &:last-child {
    margin-bottom: 0rpx;
  }

  .tips-txt {
    font-family: SourceHanSansCN,
      SourceHanSansCN,
      'MUJIFont2020';
    font-weight: 300;
    font-size: 26rpx;
    color: #3C3C43;
    line-height: 42rpx;
    letter-spacing: 2px;
    text-align: left;
    font-style: normal;
  }
}

.bottom-box {
  flex-shrink: 0;
  padding-bottom: 60rpx;
  margin-top: var(--page-margin);
  /* 避免被系统底部遮挡 */
  background-color: #fff;
  display: flex;
  justify-content: center;
}
