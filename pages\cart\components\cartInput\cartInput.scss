.cart-input {
  box-sizing: border-box;
  position: absolute;
  left: 0;
  bottom: 10rpx;
  width: 160rpx;
  height: 60rpx;
  border-radius: 5rpx;
  border: 1rpx solid #BBBBBB;
  display: flex;
  align-items: center;
  justify-content: center;

  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;

  .sku-num {
    position: relative;
    z-index: 1;
    display: block;
    height: 40rpx;
    width: 48rpx;
    //flex: 1;
    text-align: center;
    font-size: 24rpx;
    white-space: nowrap;
  }

  .btn-item {
    position: absolute;
    bottom: 0;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    font-size: 24rpx;

    &.del {
      padding-left: 30rpx;
      padding-right: 15rpx;
      left: 0;
    }

    &.add {
      padding-left: 15rpx;
      padding-right: 30rpx;
      right: 0;
    }

    .icon-css {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 14rpx;
      height: 14rpx;

      &:before {
        content: '';
        width: 14rpx;
        height: 2rpx;
        background-color: #000000;
      }

      &.add:after {
        position: absolute;
        left: 0;
        right: 0;
        z-index: 1;
        margin: auto;
        content: '';
        width: 2rpx;
        height: 14rpx;
        background-color: #000000;
      }
    }
  }
}
