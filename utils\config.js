// 域名配置
const baseApi = {
  dev: "https://vip-dev.muji.com.cn/api", // 开发小程序地址
  // develop: 'http://************:10003/api', //本地 开发小程序地址
  // develop: 'http://v6kq6e.natappfree.cc', //本地 开发小程序地址
  uat: "https://vip-uat.muji.com.cn/api", // Uat环境
  prod: "https://vip.muji.com.cn/api", // 正式环境
};
// 小程序appid配置
const appIds = {
  dev: "wxbbbf237e5f89dae3", // 开发小程序appid
  uat: "wx38d029d79dea452e", //  MUJI会员
  prod: "wx4099604b04bf38d1", // 无印良品MUJI
};

// 联系客服
const client = {
  dev:
    "https://ykf-weixin01.7moor.com/wapchat.html?accessId=ad7b7330-69c4-11ef-b17b-35007f3c8afd&fromUrl=&urlTitle=&language=ZHCN",
  uat:
    "https://ykf-weixin01.7moor.com/wapchat.html?accessId=ad7b7330-69c4-11ef-b17b-35007f3c8afd&fromUrl=&urlTitle=&language=ZHCN",
  prod:
    "https://ykf-weixin01.7moor.com/wapchat.html?accessId=2847a8d0-d222-11ee-ac01-372774cf92a2&fromUrl=&urlTitle=&language=ZHCN",
};
//素材
const ossImg = {
  dev: "https://vip-uat-oss-cdn.muji.com.cn/muji/muji-mini",
  uat: "https://vip-uat-oss-cdn.muji.com.cn/muji/muji-mini",
  prod: "https://vip-prod.oss-cn-shanghai.aliyuncs.com/muji/muji-mini",
};

const reportH5 = {
  dev: "https://vip-dev.muji.com.cn/web/",
  uat: "https://vip-uat.muji.com.cn/web/",
  // uat: "http://***********:9991/web/", // 本地测试
  prod: "https://vip.muji.com.cn/web/",
};
const accountInfo = wx.getAccountInfoSync();
console.log(accountInfo);
let result = Object.entries(appIds).filter(
  (item) => item[1] === accountInfo.miniProgram.appId
);
console.log(result);
let env = result[0][0];
console.log("当前环境", env);
const config = {
  env: env, // 区分小程序环境  dev-测试 uat-uat环境 prod-正式环境
  envVersion: accountInfo.miniProgram.envVersion || "release", // 区分代码环境  develop-开发  trial-体验版  release-正式版
  appId: accountInfo.miniProgram.appId,
  baseUrl: baseApi[env],
  client: client[env],
  ossImg: ossImg[env],
  reportH5: reportH5[env],
  tabbarPage: [
    // 一级页面地址
    "/pages/index/index",
    "/pages/exchange/exchange",
    "/pages/qrCode/qrCode",
    "/pages/life/life",
    "/pages/more/more",
  ],
  samePages: [
    // 链路栈 中相同路径页面的时候默认直接返回 // 定义在samePages中的页面特殊处理 不返回
    "/pages/custom/custom", // 二级自定义页面
    "/pages/integralDetails/integralDetails", // 商详页（可以跳购物车，然后跳其他商品）
    "/pages/campaingOpen/campaingOpen", // 活动开屏页 (自定义页面 通过ID区分的数据)
  ],
  noShowPrivicy: [
    // 不需要弹隐私协议的页面路劲
    "/pages/open/open", // 开屏页
    "/pages/open1/open1", // 开屏页
    "/pages/campaingOpen/campaingOpen", // 活动开屏页
    "/pages/userAgreement/userAgreement", // 服务使用协议
    "/pages/userPrivicy/userPrivicy", // 隐私协议
    "/pages/coupon/index", // 旧短链页面
    "/pages/mall/index", // 旧短链页面
    "/pages/information/index", // 旧短链页面
    "/pages/store-detail/index", // 旧短链页面
    "/pages/vip/index", // 旧短链页面
    "/member/pages/transferPage/transferPage", // 会员节中间页
    "/signUp/pages/signUp/signUp", // 汉麻中间页
    "/report/pages/h5/h5", // 20周年报告
  ],
  noShowPhone: [
    // 不需要绑定手机号的页面路径
    "/pages/open/open", // 开屏页
    "/pages/open1/open1", // 开屏页
    "/pages/campaingOpen/campaingOpen", // 活动开屏页
    "/pages/userAgreement/userAgreement", // 服务使用协议
    "/pages/userPrivicy/userPrivicy", // 隐私协议
    "/pages/coupon/index", // 旧短链页面
    "/pages/mall/index", // 旧短链页面
    "/pages/information/index", // 旧短链页面
    "/pages/store-detail/index", // 旧短链页面
    "/pages/vip/index", // 旧短链页面
    "/pages/register/register", // 注册
    "/pages/editPhone/editPhone", // 修改手机号页面
    "/pages/bindPhone/bindPhone", // 绑定手机号页面
    "/member/pages/transferPage/transferPage", // 会员节中间页
    "/signUp/pages/signUp/signUp", // 汉麻中间页
    "/report/pages/h5/h5", // 20周年报告
  ],
  oldPath: new Map([
    // 数组第一个值是原来的老页面  第二个值是小程序的新页面
    ["/pages/coupon/index", "/pages/myCoupon/myCoupon"], // 卡券列表
    ["/pages/infomation/index", "/pages/register/register?type=edit"], // 个人信息编辑
    ["/pages/mall/index", "/pages/life/life"], // 积分商城
    ["/pages/bind-mobile/index", "/pages/index/index"], // 首页
    ["/pages/qrcode/index", "/pages/index/index"], // 首页
    ["/pages/home/<USER>", "/pages/index/index"], // 首页
    ["/pages/vip/index", "/signUp/pages/signUp/signUp"], // campaign的活动报名
  ]),
};

// 埋点配置
var muji = require("./lf-wx-miniapp.min.js");
var sdk = muji.setup({
  name: "muji_sdk", //SDK 使⽤的⼀个默认的全局变量名，会注册在 App 全局函数中
  token: "MS1kZTBhZTI5NS0wYzM3LTRkMzItYWNjMC03YmMxYmIzNWUxNWI=", //必填，⽤于识别埋点⼩程序的连接
  defaultPath: "pages/index/index",
  server_url: "https://app-cdp.muji.com.cn", //必填，埋点数据收集的后端服务器地址
  utm_stractegy: "OVERWRITE", //'OVERWRITE' | 'SESSION' | 'MAX_AGE' 默认OVERWRITE
  //OVERWRITE: ⼀直使⽤前⼀次存储的utm直到新的utm进来
  //SESSION: utm值只在本次launch的过程中有效，下次重新初始化SDK时，不会从存储中获取utm
  //MAX_AGE: ⼀个组utm值进来的时候，记录当时的时间戳，等到下次SDK初始的时候，从存储中获
  //取utm的值，并且⽐较时间戳是否在有效范围内，如果在有效范围内这使⽤该utm，该有效性检查只在
  //SDK初始化时候进⾏，在本次launch中间不会再次检查这组utm的有效性
  utm_max_age: 720, //默认720h
  auto_anonymous_id: true, //默认值 true，如果为false的话，SDK不会⾃动⽣成anonymouseId，如果没有identitiy被设置，SDK则不会发送message到后台
  auto_track: {
    app_launch: true, //⾃动追踪onAppLaunch事件，默认值为true
    app_show: true, //⾃动追踪onAppShow事件，默认值为true
    app_hide: true, //⾃动追踪onAppHide事件，默认值为true
    page_show: true, //⾃动追踪onPageShow事件，默认值为true
    page_share: true, //⾃动追踪onPageShare事件，默认值为true
    page_leave: true, //⾃动追踪离开⻚⾯事件，此事件会记录⻚⾯的停留时⻓，默认值为false
  },
});

wx.$config = config;
