// import dayjs from '../../../../utils/dayjs.min'
const dayjs = require('../../../../utils/dayjs.min')
Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    item: {
      type: Object,
      value: {},
      observer(val) {
        this.handleVal(val);
      }
    },
    type: {
      type: Number
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    points: {},
  },
  /**
   * 组件的方法列表
   */
  methods: {
    handleVal(v) {
      const {
        expireTime,
        ...points
      } = v;
      points.time = dayjs(expireTime).format('YYYY.MM.DD');
      this.setData({
        points,
      })
    }
  }
})
