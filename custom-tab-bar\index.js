import { getTabBar, subscribeMsgIds } from "../api/index";
const app = getApp();

Component({
	options: {
		addGlobalClass: true,
	},
	data: {
		refreshNum: 0,
		selected: 0,
		tabBar: app.globalData.tabbarList,
		isShowTab: false,
		isBirthday: false, // 是否是生日月
		visitor: app.globalData.visitor, // 游客模式
	},
	lifetimes: {
		async attached() {
			console.log("attached-tabbar---------------------------------");
			this.init()
			// if (!app.globalData.tabbarList.id) {
			// 	try {
			// 		// 延迟发起请求
			// 		await new Promise((resolve) => {
			// 			let time = setTimeout(async () => {
			// 				clearTimeout(time);
			// 				time = null;
			// 				// 延迟 0.5 秒后发起请求
			// 				const resData = await getTabBar({
			// 					tenantId: 1,
			// 				});
			// 				resolve(resData);
			// 			}, 500); // 设定延迟时间 0.5 秒
			// 		}).then((resData) => {
			// 			// 处理返回的数据
			// 			let content = JSON.parse(resData.data.content);
			// 			resData.data.list = content.list;
			// 			resData.data.birthday = content.birthday;

			// 			// 将数据存储到 globalData
			// 			app.globalData.tabbarList = resData.data;

			// 			// 设置页面数据
			// 			this.setData({
			// 				tabBar: app.globalData.tabbarList,
			// 				selected: this.data.selected,
			// 				isShowTab: true,
			// 			});
			// 		});
			// 	} catch (e) {
			// 		console.error("Error fetching tabBar data:", e);
			// 	}
			// } else {
			// 	// 如果数据已经存在，直接设置 tabBar 数据
			// 	this.setData({
			// 		tabBar: app.globalData.tabbarList,
			// 		selected: this.data.selected,
			// 		isShowTab: true,
			// 	});
			// }
			// console.log("sssattach-tabbar");
			// this.setData({
			// 	visitor: app.globalData.visitor, // 游客模式
			// });
		},
	},
	pageLifetimes: {
		show() {
			console.log("sssshow-tabbar---------------------------------");
			// this.setData({
			// 	visitor: app.globalData.visitor, // 游客模式
			// });
			this.init()
		},
	},
	methods: {
		async init() {
			if (!app.globalData.tabbarList.id) {
				this.data.refreshNum = this.data.refreshNum + 1
				try {
					// 延迟发起请求
					await new Promise((resolve) => {
						let time = setTimeout(async () => {
							clearTimeout(time);
							time = null;
							// 延迟 0.5 秒后发起请求
							const resData = await getTabBar({
								tenantId: 1,
							});
							resolve(resData);
						}, 500); // 设定延迟时间 0.5 秒
					}).then((resData) => {
						// 处理返回的数据
						let content = JSON.parse(resData.data.content);
						resData.data.list = content.list;
						resData.data.birthday = content.birthday;

						// 将数据存储到 globalData
						app.globalData.tabbarList = resData.data;

						// 设置页面数据
						this.setData({
							tabBar: app.globalData.tabbarList,
							selected: this.data.selected,
							isShowTab: true,
						});
					});
				} catch (e) {
					console.error("Error fetching tabBar data:", e);
					if (this.data.refreshNum < 10) {
						this.init()
					}
				}
			} else {
				// 如果数据已经存在，直接设置 tabBar 数据
				this.setData({
					tabBar: app.globalData.tabbarList,
					selected: this.data.selected,
					isShowTab: true,
				});
			}
			this.setData({
				visitor: app.globalData.visitor, // 游客模式
			});
		},
		touchmove() {
			return false;
		},
		switchTab(e) {
			let { item, index } = e.currentTarget.dataset;
			//     pagePath: tabbarPage[index], // 原有链接
			//     type: 1, // 跳转链接方式  1-原有  2-自定义
			//     自定义链接跳转
			//     linkType: '',
			//     linkUrl: '',
			//     linkName: '',
			//     appid: '',
			//     path: '',
			//     url: ''
			// 点击的当前页面
			if (this.data.selected === index) {
				return;
			}
			// 埋点
			if (item.code) {
				wx.$mp.track({
					event: item.code,
				});
			}
			// 需要注册但是未注册 弹窗提示
			if (item.register && !app.ifRegister()) {
				return;
			}
			console.log("点击", item);
			// 是否有订阅消息
			if (item.subscribeScene && item.subscribeScene.length) {
				this.subscribefn(item);
			} else {
				if (item.type == 1) {
					console.log("跳转，", item.pagePath);
					wx.$mp.switchTab({
						url: item.pagePath,
					});
				} else {
					app.goLink(item);
				}
			}
		},
		subscribefn: app.debounce(async function (data) {
			try {
				let res = await subscribeMsgIds({
					templateIds: data.subscribeScene.join(","),
				});
				let templateIds = res.data;
				await app.handleSubscribe(templateIds, data.id);
			} catch (e) { }

			if (data.type == 1) {
				console.log("跳转，", item.pagePath);
				wx.$mp.switchTab({
					url: data.pagePath,
				});
			} else {
				app.goLink(data);
			}
		}),
		// 游客模式隐私协议
		showPrincy() {
			var currentInstance = wx.$mp.getCurrentPage();
			currentInstance.showOveralModal("secret");
		},
	},
});
