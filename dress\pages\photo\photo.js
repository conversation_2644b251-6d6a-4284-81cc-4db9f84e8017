import { getFitting } from '../../api/index'
import { goActivityHome } from '../../utils/index'
// import dayjs from '../../../utils/dayjs.min'
const dayjs = require('../../../utils/dayjs.min')
Page({
  data: {
    isLoad: false,

    list: [],

    pagenation: {
      pageNum: 0,
      total: 0,
      loading: true,
      finished: false
    }
  },

  onLoad(options) {
    this.getFitting()
  },

  onShow() {
    if (!this.data.isLoad) return (this.data.isLoad = true)
    this.refresh()
  },

  // 滚动到底部
  handleScrollToBottom() {
    if (this.data.pagenation.loading || this.data.pagenation.finished) return
    this.getFitting()
  },
  // 获取试穿记录
  async getFitting() {
    const { pagenation, list } = this.data
    const pageSize = 10
    this.setData({ 'pagenation.loading': true })
    const { data } = await getFitting({ pageNum: ++pagenation.pageNum, pageSize })
    Object.assign(pagenation, {
      loading: false,
      finished: Math.ceil(data.count / pageSize) === pagenation.pageNum,
      total: data.count
    })
    data.list.forEach(item => {
      item._created = dayjs(item.created).format('YYYY.MM.DD HH:mm')
    })
    this.setData({ list: list.concat(data.list), pagenation })
  },

  // 去生成
  handleGenerateTap() {
    // wx.navigateTo({ url: '/dress/pages/camera/camera' })
    goActivityHome()
  },

  // 刷新
  async refresh() {
    const { pagenation } = this.data
    const { data } = await getFitting({ pageNum: 1, pageSize: 1 })
    if (data.count !== pagenation.total) {
      Object.assign(pagenation, { pageNum: 0 })
      this.setData({ pagenation, list: [] })
      this.getFitting()
    }
  },

  // 点击试穿记录
  handleItemTap(e) {
    const index = e.currentTarget.dataset.index
    const { id, imgUrl } = this.data.list[index]
    wx.navigateTo({ url: `/dress/pages/camera/camera?url=${encodeURIComponent(imgUrl)}&id=${id}` })
  },
  onShareAppMessage() {
    return {
      title: '生成你的「自然有生活」同款海报',
      imageUrl: this.data.$cdn + '/dress/share.png',
      path: '/dress/pages/index/index'
    }
  },
})
