<view catch:tap="handleGo" data-orderSn="{{purchaseData.orderSn}}" data-orderSource="{{purchaseData.orderSource}}">
  <view class="purchase-card">
    <view class="purchase-channel">
      <view class="channel-name">
        {{purchaseData.channelName}}
      </view>
      <view class="purchase-time">
        订单时间：{{purchaseData.payTime}}
      </view>
    </view>
    <view class="purchase-num">
      {{purchaseData.price}}元
    </view>
  </view>
</view>