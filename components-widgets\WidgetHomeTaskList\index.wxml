<view class="WidgetHomeTaskList">
	<view bindtap="goTask">
		<BaseHomeModuleTitle item="{{({leftText:item.title||'互动获积分',rightText:'全部',rightIcon:'icon-View1'})}}" />
	</view>
	<view class="_BaseTaskItemWrap">
		<view class="_BaseTaskItem">
			<view wx:for="{{taskList}}" wx:key="id">
				<task-card taskInfo="{{item}}" visitor="{{visitor}}" origin="home" bind:showDialog="showRuleDialog" bind:share="handleInvite" bindupdate="getList" />
			</view>
		</view>
	</view>

</view>
<my-popup show="{{ showRuleDialog}}" borderRadius="0" closeable="{{true}}" title="任务规则" confirmText="我知道了" bindclose="handleClose" bindconfirm="handleClose">
	<image src="{{currentRuleImg}}" mode="widthFix" style="width:100%;display:block">
	</image>
</my-popup>