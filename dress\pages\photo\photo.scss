/* dress/pages/photo/photo.wxss */
.camera-page {
  position: relative;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff9ec;

  .primary-btn {
    width: 150rpx;
    height: 65rpx;
    background: #3c3c43;

    text-align: center;
    line-height: 65rpx;
    font-weight: bold;
    font-size: 25rpx;
    color: #fcf9ee;
    letter-spacing: 3px;
  }

  .count-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 65rpx 45rpx 22rpx;

    .count-item {
      font-weight: 500;
      font-size: 32rpx;
      color: #3c3c43;
    }
    .count-num {
      margin: 0 0.5em;
    }
  }

  .photo-list-wrap {
    flex: 1;
    min-height: 0;
    .photo-list-scroll {
      height: 100%;
    }

    .photo-list {
      display: flex;
      flex-wrap: wrap;
      padding: 22rpx 45rpx 0;
      justify-content: space-between;
    }
    .photo-item {
      margin-bottom: 60rpx;
      width: 317rpx;

      text-align: center;
      font-size: 26rpx;
      line-height: 35rpx;

      .photo-item-img {
        width: 100%;
        height: 422rpx;
      }
      .photo-item-title {
        font-weight: 800;
        margin: 43rpx 0 4rpx;
      }
      .photo-item-time {
        font-weight: bold;
      }
    }
  }

  .no-data {
    margin-top: 520rpx;
    .no-data-text {
      font-size: 32rpx;
      color: #3c3c43;
      letter-spacing: 3rpx;
      text-align: center;
    }
    .primary-btn {
      margin: 120rpx auto 0;
      width: 396rpx;
      height: 75rpx;
      line-height: 75rpx;
      font-size: 26rpx;
    }
  }
}
