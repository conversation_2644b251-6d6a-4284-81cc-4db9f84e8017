import {
  postOrderCreate
} from '../../api/index.js'

const app = getApp()

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: false,

    detail: {},

    // loadingSubmit: false,

    popShow: false,
    err: {
      show: false,
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let that = this
    const eventChannel = this.getOpenerEventChannel()
    // 监听acceptDataFromOpenerPage事件，获取上一页面通过eventChannel传送到当前页面的数据
    eventChannel.on('exchangePreview', function ({
      data
    }) {
      that.setData({
        detail: data,
      })
    })

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  strip(num, precision = 12) {
    return +parseFloat(num.toPrecision(precision));
  },

  onClickSubmit() {
    this.setData({
      loading: true
    })
    const data = {
      remark: '',
      source: this.options.source,
      productList: this.data.detail.productList
    }
    wx.$mp.track({
      event: 'submit_order_exchange',
      props: {
        productList: data.productList.map(item => ({
          productId: item.productId,
          productName: item.productName,
        })),
      }
    })
    postOrderCreate(data).then(res => {
      // debugger
      // console.log('res', res)
      const data = res.data
      const extra = `orderCode=${data.orderCode}&orderAmount=${data.orderAmount || 0}&orderPoint=${data.orderPoint || 0}`
      const url = `/pages/submitOrderResult/submitOrderResult?${extra}`
      wx.$mp.redirectTo({
        url
      })
    }).catch(err => {
      // debugger
      if (err.code === 1005) {
        this.setData({
          err: {
            ...err,
            show: true
          }
        })
      }
    }).finally(() => {
      this.setData({
        loading: false
      })
    })
  },

  onPopConfirm() {
    this.setData({
      err: {
        ...this.data.err,
        show: false
      }
    })
    wx.$mp.switchTab({
      url: '/pages/life/life',
    })
  }
})
