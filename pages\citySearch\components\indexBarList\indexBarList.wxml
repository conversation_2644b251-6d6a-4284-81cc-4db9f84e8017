<view class="indexbar-list">
  <block wx:if="{{type==='all'}}">
    <view wx:if="{{currentSearchCities.length>0}}" class="city-list">
      <view wx:for="{{currentSearchCities}}" class="city-item {{currentCity===cityName? 'active' : ''}}" wx:key="index" wx:for-item="cityName" bind:tap="handleChangeCity" data-city="{{cityName}}">
        {{cityName}}
      </view>
    </view>
    <view wx:else class="city-list">
      <view class="no-data">
        <image src="{{$cdn}}/store/no-data-icon.png" mode="widthFix" />
        <view class="no-data-txt">暂时搜索不到相关城市</view>
      </view>
    </view>
  </block>
  <block wx:else="{{type==='indexbar'}}">
    <view class="letter-box">
      <view class="letter-item">
        #
      </view>
      <view class="letter-item {{currentLetter===item ? 'active': ''}}" wx:for="{{indexArray}}" wx:key="index" bindtap="clickLetter" data-letter="{{item}}">
        {{item}}
      </view>
    </view>
    <view class="city-list">
      <view id="{{'city_' + cityLetter}}" wx:for="{{indexArray}}" wx:key="index" class="city-block" wx:for-item="cityLetter">
        <view class="letter-name">{{cityLetter}}</view>
        <view wx:for="{{cityList[cityLetter]}}" class="city-item" wx:for-item="cityName" bind:tap="handleChangeCity" wx:key="index" data-city="{{cityName}}">
          <view class="city-name">{{cityName}}</view>
          <view wx:if="{{currentCity===cityName? 'active' : ''}}" class="iconfont icon-Mark" style="color: #7F0019; font-size: 24rpx;" />
        </view>
      </view>
    </view>
  </block>
</view>