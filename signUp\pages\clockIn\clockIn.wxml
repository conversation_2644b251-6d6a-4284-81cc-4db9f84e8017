<!--signUp/pages/clockIn/clockIn.wxml-->
<my-page overallModal="{{overallModal}}" loading="{{loading}}">
  <view class="page-container" style="background-image:url({{$cdn}}/signUp/lotteryBack1.png);">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" color="black" />
    <view class="page-content">
      <scroll-view class="clockIn" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
        <view class="clockIn-wrap">
          <view class="clockIn-title">
            <text class="clockIn-icon"></text>
            <view class="clockIn-title-wrap">
              第 <text class="num">{{days}}</text> {{disabled?"天打卡记录":'天'}}
            </view>
          </view>
          <view class="score">
            <view class="prompt" wx:if="{{!islastDay&&!disabled}}">请根据您今天的使用感受做出评分</view>
            <view class="answer {{item.questionType==3||item.questionType==5?'answer1':''}}" wx:for="{{questionList}}">
              <!-- 问答题暂时不展示title 本期先这样用 -->
              <block wx:if="{{item.questionType==5}}">
                <view wx:if="{{!disabled}}" class="answer-title"><text>{{item.title||''}}</text></view>
                <view style="padding-bottom: 60rpx;">
                  <van-uploader multiple class="picture-wrap" file-list="{{ fileList }}" max-count="1" preview-size="172rpx" image-fit="aspectFill" use-before-read="{{true}}" data-index="{{index}}" bind:after-read="afterRead" bind:before-read="beforeRead" bind:delete="del" disabled="{{disabled}}" deletable="{{!disabled}}">
                    <view class="picture">
                      <image class="img" src="{{$cdn}}/signUp/add.png" mode="" />
                    </view>
                  </van-uploader>
                </view>
              </block>
              <block wx:if="{{item.questionType==3}}">
                <view class="textarea">
                  <view class="answer-title"><text>{{item.title||''}}</text></view>
                  <!-- 问答题暂时不展示title 本期先这样用 -->
                  <textarea disabled="{{disabled}}" adjust-position="{{true}}" cursor-spacing="100" show-confirm-bar="{{true}}" maxlength="200" class="txt" value="{{ info[item.alias] }}" bindinput="bindinput" data-alias="{{item.alias}}" placeholder-style="font-size:22rpx;color: #979797;" bindkeyboardheightchange="keyboardChange" placeholder="{{item.questionTips||''}}"></textarea>
                  <view class="textNum">{{info[item.alias].length}}/200</view>
                </view>

              </block>
              <block wx:if="{{item.questionType==2}}">
                <view class="answer-title"><text>{{item.title||''}}</text></view>
                <view class="skin">
                  <view wx:if="{{(items.isClick&&disabled)||!disabled}}" wx:for="{{item.options}}" wx:for-item="items" wx:for-index="indexs" bind:tap="clickTap" data-index="{{index}}" data-indexs="{{indexs}}" class="skin-item {{items.isClick?'active':''}}">{{items.title}}
                  </view>

                </view>
              </block>
              <block wx:if="{{item.questionType==4}}">
                <view class="answer-title" style="margin-bottom: {{item.subTitle?'12rpx':'20rpx'}};"><text>{{item.title||''}}</text>
                  <view class="answer-text" wx:if="{{item.subTitle}}">{{item.subTitle}}</view>
                </view>
                <view class="answer-star">
                  <view>
                    <van-rate disabled-color="#C8B49A" disabled="{{disabled}}" value="{{info[item.alias]}}" bind:change="changeRate1" data-alias="{{item.alias}}" data-index="{{index}}" size="48rpx" gutter="27rpx" color="#C8B49A" void-color="#C8B49A" count="{{item.maxScore}}" icon="{{$cdn}}/signUp/start1.png" void-icon="{{$cdn}}/signUp/start2.png"></van-rate>
                    <!--  -->
                  </view>
                  <view class="num" wx:if="{{info[item.alias]}}"><text class="key">{{info[item.alias]}}/{{item.maxScore}} </text></view>
                </view>
              </block>
            </view>
            <!-- disabled="{{!isComplate}}"  -->
            <view class="bottom-box">
              <basic-button wx:if="{{type != 'view'}}" disabled="{{loda}}" width="{{510}}" size="large" bind:click="submit">
                立即提交完成打卡
              </basic-button>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
  </view>
</my-page>