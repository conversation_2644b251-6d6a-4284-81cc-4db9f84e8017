import {
  getqrcode_base64
} from '../../../api/index.js'
import {
  lotteryUserGet_prizes,
  lotteryTaskInfo,
  lotteryPointRedeem,
  lotteryList,
  lotteryDraw,
  initLotteryNum,
  lotteryClockIn,
  shareTaskPage
} from '../../api/index'
import {
  BlindBox,
  PrizeDraw,
  BlindBoxPrizeDraw,
  prizeTypeP1,
  ShareTitle
} from '../../utils/lotteryCode'
const dayjs = require('../../../utils/dayjs.min')
const app = getApp()
let isShare = false
let timerOnLoad, timer2task, timerPopup2Lottery, timerLotteryLoading;
const today = dayjs().format('YYYY-MM-DD')

Page({
  data: {
    loading: false,
    isOnLoad: false, // 页面是否刚刚onload
    pageCurrent: '', // start 开始猫咪动效页 | popup弹窗页 | lottery 抽奖页 | task任务页
    shadowShow: false,
    all: 6,
    finish: 4,
    showInvite: false,
    lotteryCode: null, //当前获取 我要展示盲盒 还是 抽奖的类型
    lotteryInfo: null, //抽奖任务信息
    lotteryTimesLoaded: false, // 可抽奖次数是否完成加载
    lotteryAnimating: false, // 大树在执行动效中，似乎不执行 transition start、run
    lotteryPopupShow: false,
    lotteryPopupLoading: false,
    lotteryPopupLoadingTime: -1,
    lotteryPopupInfo: null, // 抽奖弹窗信息
    taskList: [], //任务展示数组格式优化
    pointsUser: 0,
    itemTask: {},
    timeTo: 2000,
    showShareSuccess: false,
    shareSuccessTimes: 0,
  },
  // options.page 为空 或者 task 时，进入任务页
  // options.page 为 start 时，直接进入开始页，自动展示猫咪动画
  // options.page 为 lottery 时，直接进入抽奖页，不展示动画
  async onLoad(options) {
    this.toast = this.selectComponent('#customToast')
    if (options?.page) {

      // 到动效页面，如果已注册，且无抽奖记录，自动抽盲盒
      // 如果从注册页返回，已经注册，且无抽奖记录，自动抽盲盒
      const isRegistered = app.globalData.userInfo.isMember > 0
      if (
        (
          options?.page === 'start' ||
          options?.page === 'popup' && options?.from === 'register'
        ) &&
        isRegistered
      ) {
        this.getLotteryRecord().then(res => {
          if (!res) {
            initLotteryNum({
              code: BlindBox
            }).then(() => {
              this.getLotteryDraw(BlindBox) //盲盒抽奖
            })
          }
        })
      }
      this.setData({
        isOnLoad: true,
        pageCurrent: options.page
      })
      clearTimeout(timerOnLoad)
      timerOnLoad = setTimeout(() => {
        this.setData({
          isOnLoad: false
        })
      }, 500)
    } else {
      this.setData({
        isOnLoad: true,
        pageCurrent: 'task'
      })
      clearTimeout(timerOnLoad)
      timerOnLoad = setTimeout(() => {
        this.setData({
          isOnLoad: false
        })
      }, 500)
    }
  },
  async onShow(options) {
    const isRegistered = app.globalData.userInfo.isMember > 0
    this.setData({
      isRegistered: isRegistered
    })
    this.updatePoints()
    if (isRegistered) {
      this.getTaskInfo()
    }

    if (isShare) {
      const today = dayjs().format('YYYY-MM-DD')
      const shareSuccessTimes = this.data.itemTask.allReadyNum
      this.setData({ showShareSuccess: true, shareSuccessTimes: shareSuccessTimes })
      isShare = false
    }
  },
  async updatePoints() { //更新当前积分的展示
    await app.getUserInfo()
    const isRegistered = app.globalData.userInfo.isMember > 0
    this.setData({
      isRegistered: isRegistered,
      pointsUser: app.globalData.userInfo.points || 0
    })
  },
  async getTaskInfo() {
    this.setData({
      loading: true
    })
    const {
      lotteryCode
    } = this.data
    let newArr = [] //整个新的数组
    let InviteObj = [] // 邀请用户
    let sharingActivityObj = [] // sharingActivity 分享活动
    const resLotteryList = await lotteryList({}) //抽奖列表参数
    const ClockIn = await lotteryClockIn({
      code: PrizeDraw
    }) //抽奖打卡
    const initNum = await initLotteryNum({
      code: PrizeDraw
    }) //初始抽奖化接口
    try {
      const resInfo1 = await this.getLotteryInfo()
      if (resInfo1.code === 0) {
        const taskNew = resInfo1.data.taskList || []
        if (taskNew && taskNew.length > 0) {
          InviteObj = taskNew.filter(item => item.taskDesc === 4) //邀请任务
          sharingActivityObj = taskNew.filter(item => item.taskDesc === 6) //分享活动
        }
        if (resInfo1.data.clockInFlag) {
          newArr.push({ //每日打卡
            clockInFlag: resInfo1.data.clockInFlag, //已打卡未打卡
            alreadyClockInDay: resInfo1.data.alreadyClockInDay, //已打卡天数
            clockInLotteryNum: resInfo1.data.clockInLotteryNum, // 每次打卡获得的抽奖次数
            title: '每日参与',
            showType: 'clockIn', //展示当前类型的任务
            todayClockInFlag: resInfo1.data.todayClockInFlag, //今日是否已打卡
            alreadyClockInDayAll: resInfo1.data.alreadyClockInDay || 0
          })
        }

        // sharingActivity 分享活动
        if (sharingActivityObj && sharingActivityObj.length > 0) {

          sharingActivityObj.forEach(itemsharing => {
            newArr.push({ //邀请用户
              title: '分享任务',
              showType: 'sharingActivity', //展示当前类型的任务
              prizeNum: itemsharing.taskRewardList ? itemsharing.taskRewardList.reduce((acc, item) => item.rewardType === 3 ? acc + item.prizeNum : acc, 0) : 0,
              allReadyNum: itemsharing.allReadyNum || 0,
              readyNum: itemsharing.readyNum,
            })
          })
        }

        // Invite 邀请用户
        if (InviteObj && InviteObj.length > 0) {
          const avatar = this.data.$cdn + '/default-avatar.png'
          console.log(InviteObj, 'InviteObjInviteObjInviteObj')
          InviteObj.forEach(itemInvite => {
            if (!itemInvite.inviteFriendsImgList) {
              itemInvite.inviteFriendsImgList = []
            }
            if (!itemInvite.allReadyNum) {
              itemInvite.allReadyNum = 0
            }
            // itemInvite.allReadyNum = 2
            newArr.push({ //邀请用户
              title: '邀请新用户',
              showType: 'Invite', //展示当前类型的任务
              InviteAll: itemInvite.allReadyNum || 0, //任务完成的次数
              allReadyNum: itemInvite.allReadyNum, // 任务已经完成次数
              inviteFriendsImgList: itemInvite.inviteFriendsImgList ? (itemInvite.inviteFriendsImgList.length < itemInvite.allReadyNum ? [...Array(itemInvite.allReadyNum - itemInvite.inviteFriendsImgList.length).fill(avatar), ...itemInvite.inviteFriendsImgList] : itemInvite.inviteFriendsImgList) : Array(itemInvite.allReadyNum).fill(avatar), //头像列表
              taskRewardList: itemInvite.taskRewardList || [], //任务奖励任务
              readyNum: itemInvite.readyCycle === 3 ? itemInvite.totalReadyNum : itemInvite.readyNum, //任务完成次数的总长度
              prizeNum: itemInvite.taskRewardList ? itemInvite.taskRewardList.reduce((acc, item) => item.rewardType === 3 ? acc + item.prizeNum : acc, 0) : 0, //奖励的次数展示
              totalReadyNum: itemInvite.totalReadyNum, //  每日上限
              readyCycle: itemInvite.readyCycle
            })
          })

        }
        if (resInfo1.data.todayClockInFlag) {
          newArr.push({ //积分
            pointFlag: resInfo1.data.pointFlag, //是否支持积分兑换抽奖次数
            pointNum: resInfo1.data.pointNum, //积分数量/次
            readyType: resInfo1.data.readyType === 'readyDay' ? '日' : '月', // readyDay、readyMonth 类型
            // title: `${resInfo1.data.pointNum}积分兑换1次`,
            title: '消耗积分抽奖',
            redeemNum: resInfo1.data.redeemNum || 0, // "兑换次数"
            alreadyRedeemNum: resInfo1.data.alreadyRedeemNum, // "已兑换次数")
            readyDay: resInfo1.data.readyDay > 1 ? resInfo1.data.readyDay : '', // (value = "每几天/月") 这是前面这个这段，1 2 3 4 5这样的 1不展示
            showType: 'point', //展示当前类型的任务
          })
        }
        this.setData({
          taskList: newArr,
          loading: false,
        })
        // 先存一个 本次邀请的数量  提示的数量   taskInviteNum      allReadyNum总的完成次数
        // 获取邀请的数量 大于 0 的时候 本地取这个数量 taskInviteNum || 0     
        // 本次邀请的数量 - 本地存储的数量 = 值 如果  大于 0  就弹 差值提醒几次 
        // let newTaskInviteNum = wx.getStorageSync('taskInviteNum') || 0
       
        // if (InviteObj) {
        //   console.log(InviteObj);
        //   let newTaskInviteNum = InviteObj[0].allReadyNum - wx.getStorageSync('taskInviteNum') || 0
        //   if (newTaskInviteNum > 0) {
        //     this.toast.showToast('邀请成功', newTaskInviteNum)
        //   }
        //   wx.setStorageSync('taskInviteNum', InviteObj[0].allReadyNum);
        // }

      }

    } catch (err) {
      this.setData({
        loading: false,
      })
    }

  },

  onTapShareSuccess() {
    this.setData({ showShareSuccess: false })
  },

  getLotteryInfo() {
    return new Promise((resolve, reject) => {
      //任务对象详情接口
      lotteryTaskInfo({
        lotteryCode: PrizeDraw
      }).then(res => {
        if (res.code === 0) {
          this.setData({
            lotteryInfo: res.data,
            lotteryTimesLoaded: true
          })
        }
        resolve(res)
      }).catch(err => {
        reject(err)
      })
    })
  },

  // 获取盲盒抽奖记录
  getLotteryRecord() {
    return new Promise((resolve, reject) => {
      this.setData({
        loading: true
      })
      const data = {
        pageNum: 1,
        pageSize: 10,
        lotteryCode: BlindBox,
        prizeType: prizeTypeP1
      }
      lotteryUserGet_prizes(data).then(res => {
        this.setData({
          loading: false
        })
        console.log(res)
        const hasLottery = res?.data?.list?.length > 0
        resolve(hasLottery)
      }).catch(err => {
        this.setData({
          loading: false
        })
      })
    })
  },

  // 抽奖流程
  getLotteryDraw(code = PrizeDraw) {
    return new Promise((resolve, reject) => {
      this.setData({
        loading: true
      })
      lotteryDraw({
        code
      }).then(res => {
        this.getLotteryInfo().then(() => {
          this.setData({
            loading: false
          })
          resolve(res)
        }).catch(err => {
          this.setData({
            loading: false
          })
          reject(err)
        })
      }).catch(err => {
        this.setData({
          loading: false
        })
        reject(err)
      })
    })
  },

  onTapDesc() {
    wx.$mp.navigateTo({
      url: '/member/pages/gift/gift',
    })
  },

  // 点击弹窗按钮，判断是否新用户，是否需要领取盲盒
  onTapStartPopup() {
    const isRegistered = app.globalData.userInfo.isMember > 0
    if (isRegistered) {
      // 已注册
      this.setData({
        pageCurrent: 'popup'
      })
    } else {
      // 未注册
      const backUrl = encodeURIComponent(`/member/pages/task/task?page=popup&from=register`)
      wx.$mp.redirectTo({
        url: `/pages/register/register?backUrl=${backUrl}`,
      })
    }
  },
  handleLotteryTimer() {
    if (this.data.lotteryPopupLoadingTime > -1) {
      clearTimeout(timerLotteryLoading)
      timerLotteryLoading = setTimeout(() => {
        this.setData({
          lotteryPopupLoadingTime: this.data.lotteryPopupLoadingTime - 1
        })
        this.handleLotteryTimer()
      }, 1000)
    } else {
      clearTimeout(timerLotteryLoading)
    }
  },
  onTapLottery: app.debounce(function () {
    if (this.data.pageCurrent === 'task') {
      this.onTap2Tree()
      return
    }
    if (
      this.data.pageCurrent === 'lottery' &&
      this.data.lotteryInfo?.surplusCount > 0 &&
      !this.data.lotteryPopupLoading &&
      this.data.lotteryPopupLoadingTime < 0
    ) {
      // 抽奖
      this.setData({
        loading: true,
        lotteryPopupLoading: true,
      })
      this.getLotteryDraw().then(res => {
        const data = res?.data
        const lotteryPopupInfo = data?.length ? data[0] : data || null

        if (!lotteryPopupInfo.prizesType) {
          console.log('data', data)
          console.error('没有 prizesType')

          // 这里按说不用写这么多逻辑，但是现在UAT总是抽到空的数组，没办法测倒计时的样式
          let lotteryPopupLoadingTime = 2
          // if (this.data.lotteryPopupShow) {
          //   lotteryPopupLoadingTime = 2
          // }

          this.setData({
            lotteryPopupShow: true,
            loading: false,
            lotteryPopupLoading: false,
            lotteryPopupLoadingTime: lotteryPopupLoadingTime,
            lotteryPopupInfo,
          })
          clearTimeout(timerLotteryLoading)
          this.handleLotteryTimer()
          return
        }

        // 1实物商品 2电子券 3积分 4优惠券 99空
        if (lotteryPopupInfo.prizesType === 3) {
          this.updatePoints()
        }

        // setTimeout(() => {
        this.setData({
          lotteryPopupShow: true,
          loading: false,
          lotteryPopupLoading: false,
          lotteryPopupLoadingTime: 2,
          lotteryPopupInfo,
        })
        clearTimeout(timerLotteryLoading)
        this.handleLotteryTimer()
        // }, 2000)
      }).catch(err => {
        this.setData({
          lotteryPopupShow: false,
          loading: false,
          lotteryPopupLoading: false,
          lotteryPopupInfo: null,
        })
      })
    } else if (this.data.lotteryInfo?.surplusCount === 0) {
      wx.showToast({
        title: '抽奖次数已用完',
        icon: 'none'
      })
    }
  }),
  // 弹窗继续抽奖
  onTapLotteryPopupLottery() {
    this.onTapLottery()
  },
  // 点击弹窗获取抽奖机会，先关闭弹窗，再切换页面UI
  onTapLotteryPopupTask: app.debounce(function () {
    this.onTapLotteryPopupClose()
    clearTimeout(timer2task)
    timer2task = setTimeout(() => {
      this.onTap2Task()
    }, 400)
  }),
  onTap2Task() {
    this.setData({
      pageCurrent: 'task'
    })
  },
  onTap2Tree() {
    this.setData({
      pageCurrent: 'lottery'
    })
  },
  onTapMore: app.debounce(function () {
    wx.$mp.navigateTo({
      url: '/member/pages/lottery/lottery',
    })
  }),
  onLogoTransitionEnd() {
    if (this.data.pageCurrent === 'popup') {
      clearTimeout(timerPopup2Lottery)
      timerPopup2Lottery = setTimeout(() => {
        this.setData({
          pageCurrent: 'lottery'
        })
      }, 400)
    }
  },
  onLotteryTransitionStart() {
    console.log('onLotteryTransitionStart')
    this.setData({
      lotteryAnimating: true
    })
  },
  onLotteryTransitionRun() {
    console.log('onLotteryTransitionRun')
    this.setData({
      lotteryAnimating: true
    })
  },
  onLotteryTransitionEnd() {
    console.log('onLotteryTransitionEnd')
    this.setData({
      lotteryAnimating: false
    })
  },

  onTapLotteryPopupClose() {
    this.setData({
      lotteryPopupShow: false
    })
  },
  onTapLotteryPopupGift: app.debounce(function () {
    wx.$mp.navigateTo({
      url: '/member/pages/gift/gift',
    })
  }),

  scroll(e) {
    let shadowShow = false
    let {
      scrollTop
    } = e.detail
    if (scrollTop > 20) {
      shadowShow = true
    }
    if (shadowShow !== this.data.shadowShow) {
      this.setData({
        shadowShow
      })
    }
  },
  goClockIn: app.debounce(async function (e) { //去打卡
    app.subscribe('remindToCheckIn').then((res) => {
      console.log(res, 'traddddddddddddddddddddddd');
      if (Object.values(res || {}).includes('reject')) {
        wx.showToast({
          title: '订阅失败',
          icon: 'none',
        });

      } else {
        wx.showToast({
          title: '订阅成功',
          icon: 'none',
        });
      }

    })
  }, 500),
  // goSharing: app.debounce(async function (e) {//去分享活动
  //   const { item } = e.currentTarget.dataset
  //   this.setData({ loading: true, itemTask: item })
  //   // 生成poster图片
  //   try {
  //     let src = await this.createCanvas()
  //     this.setData({
  //       invitePoster: src,
  //       showInvite: true,
  //       loading: false
  //     })
  //   } catch (err) {
  //     this.setData({
  //       showInvite: false,
  //       loading: false
  //     })
  //   }

  // }, 500),
  goPoint: app.debounce(async function (e) { //去兑换抽奖次数
    this.setData({
      loading: true,
      timeTo: 2000
    })
    const {
      item
    } = e.currentTarget.dataset
    lotteryPointRedeem({
      code: PrizeDraw
    }).then(async res => {
      await app.getUserInfo()
      if (res.code === 0) {
        this.toast.showToast('积分兑换成功', 1)
        this.setData({
          pointsUser: app.globalData.userInfo.points || 0
        })
      }
      this.getTaskInfo()
    }).catch(err => {
      if (err.code === 1007) {
        // this.toast.showToast(err.msg)
        this.toast.showToast(`今日已兑换${item.redeemNum}次`)
      }
      if (err.code === 1006) {
        this.toast.showToast(err.msg)
      }
    }).finally(() => {
      this.setData({
        loading: false
      });
    })
  }, 500),
  async goInvite(e) { // 去邀请

  },
  // canvas加载图片
  loadImage(canvas, src) {
    return new Promise((resolve, reject) => {
      let img1 = canvas.createImage()
      img1.src = src
      img1.onload = function () {
        resolve(img1)
      }
      img1.onerror = function (e) {
        wx.showToast({
          title: '图片加载失败',
          icon: 'none',
        })
        console.log('图片加载失败')
        reject()
      }
    })
  },
  // 获取太阳码
  getCanvasImg() {
    // trial 1：体验版 3：正式版本 上线后改为正式版本
    let linColor = '{ "r": 161, "g": 135, "b": 100 }'
    return getqrcode_base64({
      isHyaline: 1,
      page: 'pages/index/index',
      scene: 'inviteUserId=' + this.data.userInfo.id,
      trial: wx.$config.envVersion === 'release' ? 3 : 1,
      width: 300,
      linColor: linColor
    }).then(({
      data
    }) => {
      // console.log(data, 'data 二维码打印');
      return data
    }).catch(err => {
      console.log(err, '------------')
    })
  },
  // 生成海报图片
  // createCanvas() {
  //   if (this.data.invitePoster) {
  //     return Promise.resolve(this.data.invitePoster)
  //   }
  //   return new Promise((resolve, reject) => {
  //     this.createSelectorQuery()
  //       .select('#myCanvas')
  //       .fields({ node: true, size: true })
  //       .exec(async (res) => {
  //         console.log(res)
  //         let { node: canvas, width, height } = res[0]
  //         let { $cdn } = this.data
  //         let { rpx } = app.globalData
  //         let dpr = 4096 / height - 0.01
  //         const ctx = canvas.getContext('2d')
  //         ctx.imageSmoothingEnabled = true
  //         canvas.width = width * dpr
  //         canvas.height = height * dpr
  //         try {
  //           ctx.scale(dpr, dpr)
  //           ctx.clearRect(0, 0, width, height)
  //           // 以上代码固定
  //           // 以下内容根据UI实际书写
  //           ctx.fillStyle = '#ffffff '
  //           ctx.fillRect(0, 0, width, height)
  //           // 绘制背景
  //           let img1 = await this.loadImage(canvas, $cdn + '/firendShip/poster.png')
  //           ctx.drawImage(img1, 0, 0, 750 * rpx, 1008 * rpx)
  //           // 绘制二维码
  //           let data = await this.getCanvasImg()
  //           let img2 = await this.loadImage(canvas, 'data:image/png;base64,' + data)
  //           ctx.drawImage(img2, 547 * rpx, 812 * rpx, 151 * rpx, 151 * rpx)
  //           // 绘制圆圈
  //           ctx.strokeStyle = '#EEEAE1'
  //           ctx.lineWidth = 1 * rpx
  //           ctx.beginPath()
  //           ctx.arc(623.5 * rpx, 887.5 * rpx, 80.5 * rpx, 0, Math.PI * 2)
  //           ctx.stroke()
  //         } catch (err) {
  //           reject()
  //           return
  //         }
  //         wx.canvasToTempFilePath({
  //           x: 0,
  //           y: 0,
  //           width: width,
  //           height: height,
  //           destWidth: width * 4,
  //           destHeight: height * 4,
  //           canvas: canvas,
  //           success: (res) => {
  //             this.data.invitePoster = res.tempFilePath
  //             resolve(res.tempFilePath)
  //           },
  //           fail(e) {
  //             console.log(e, '失败')
  //             reject()
  //           },
  //         })
  //       })
  //   })
  // },
  closesuccess() { //分享之后的逻辑
    shareTaskPage().then(res => {
      if (res.code === 0) {
        console.log(this.data.itemTask)

        if (this.data.itemTask.readyNum !== this.data.itemTask.allReadyNum) {
          isShare = true
          // this.toast.showToast('分享成功', 1)
        }
        this.setData({
          showInvite: false
        })
        this.getTaskInfo()
      }
    })
  },
  // 关闭邀请弹窗
  closeModal() {
    this.setData({
      showInvite: false
    })
  },
  onReachBottom() {

  },
  onShareAppMessage(options) {
    if (options && options.target && options.target.dataset) {

      const {
        type,
        item
      } = options.target.dataset
      if (type && type === 'sharingActivity') {
        isShare = true
        // app.setTodaySub('task_share_success', true)
        // const today = dayjs().format('YYYY-MM-DD')
        // wx.setStorageSync(`task_share_success_${today}`, this.data.shareSuccessTimes + 1)
        this.setData({
          itemTask: item
        })
        this.closesuccess()
      }
      if (type && type === 'Invite') {


      }
    }

    console.log(options)

    if (options.from === 'button') {
      return {
        title: ShareTitle,
        imageUrl: this.data.$cdn + '/firendShip/share.png',
        // path: '/pages/index/index'
        path: 'member/pages/transferPage/transferPage'
      }
    } else {
      return {
        title: ShareTitle,
        imageUrl: this.data.$cdn + '/firendShip/share.png',
        // path: '/pages/index/index'
        path: 'member/pages/transferPage/transferPage'
      }
    }

  }
})
