const app = getApp()
Component({
  properties: {
    src: String, // 要分享的图片地址，必须为本地路径或临时路径
    // 逻辑控制
    show: {
      type: Boolean,
      value: false,
      observer(value) {
        if (value) {
          this.init()
        }
      }
    },
    ImgStyle: {
      type: String,
      value: "default",
    }
  },
  data: {
    tip: false
  },
  methods: {
    async init() {
      let that = this
      let {
        src
      } = this.data;
      // 网络图片需要先转换为本地图片
      if (src.startsWith('http')) {
        wx.downloadFile({
          url: src,
          success(res) {
            that.shareImg(res.tempFilePath)
          }
        })
      } else {
        this.shareImg(src)
      }
    },
    // 分享图片
    shareImg(path) {
      let that = this
      console.log(that.data.ImgStyle, 'that.data.ImgStyle');
      wx.showShareImageMenu({
        path,
        style: that.data.ImgStyle,
        success() {
          that.triggerEvent('success')
        },
        fail(err) {
          // 没有保存图片权限 给与提醒
          if (err.errMsg === 'showShareImageMenu:fail auth deny') {
            that.setData({
              tip: true
            })
          }
        },
        complete() {
          that.triggerEvent('close')
        }
      })
    },
    cancel() {
      this.setData({
        tip: false
      })
    },
    // 前往开启
    confirm() {
      wx.$mp.openSetting()
      this.setData({
        tip: false
      })
    }
  }
})
