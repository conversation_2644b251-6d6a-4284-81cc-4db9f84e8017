.share {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 750rpx;

  .poster-image {
    display: block;
    margin: 0 auto 136rpx;
    width: 478rpx;
    box-shadow: 0rpx 4rpx 4rpx 0rpx rgba(0, 0, 0, 0.3);
    border-radius: 10rpx;
  }

  .bottom {
    border-radius: 20rpx 20rpx 0 0;
    background-color: #ededed;

    .button-group {
      padding: 44rpx 0 62rpx;
      display: flex;
      align-items: center;
      justify-content: space-around;
    }

    .button {
      .icon {
        width: 106rpx;
        height: 106rpx;
      }

      .text {
        margin-top: 12rpx;
        font-size: 20rpx;
        color: #818181;
        line-height: 28rpx;
        text-align: center;
      }
    }

    .wx-button {
      margin: 0;
      padding: 0;
      border-radius: 0;
      background-color: transparent;
      line-height: 1;

      &::after {
        border: none;
      }
    }

    .cancel {
      background-color: #fff;
      padding: 40rpx 0 calc(var(--safe-bottom) + 40rpx);
      text-align: center;
      line-height: 35rpx;
      font-size: 32rpx;
    }
  }
}
