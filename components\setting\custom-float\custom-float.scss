/* components/setting/custom-float/custom-float.wxss */
.box {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  pointer-events: none;
  width: auto;
  height: auto;

  &-view {
    pointer-events: auto;
    position: relative;
    height: auto;
  }

  &-content {
    display: flex;
    flex-direction: column;
  }

  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:last-child {
      margin-bottom: 0 !important;
    }
  }

  &-text {
    margin-top: 10rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16rpx;
    color: #000000;
    line-height: 22rpx;
    white-space: nowrap;
  }

}
