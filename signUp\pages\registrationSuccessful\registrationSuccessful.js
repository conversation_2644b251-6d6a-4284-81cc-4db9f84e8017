// signUp/pages/registrationSuccessful/registrationSuccessful.js
const app = getApp()
import {
  getSubscribeByScene,
} from '../../../api/index.js';
import {
  getCampaignType,
  getEnrollInfo
} from '../../api/index.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    disabled: false,
    showContact1: false,
    signUpInfo: {},
    userSignInfo: {},
    campaignCode: wx.getStorageSync('campaignCode'),
    sendSmsTime: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(app.globalData, 'app.globalData 渠道');
    console.log(this.data.overallModal, 'overallModal');
    this.getSubNum()
  },
  onShow() {
    this.getData()
  },
  // 查询订阅次数
  getSubNum() {
    getSubscribeByScene({
      scene: "enroll_success"
    }).then(res => {
      console.log(res.data, '订阅信息');
      console.log(this.data.userSignInfo, '用户报名信息');
      let disabled = false
      // 有小于1的  就需要订阅
      disabled = res.data.some(item => item.subNumber < 1)
      console.log(disabled, 'disabled');
      this.setData({
        disabled
      })
    })
  },
  // 获取报名信息数据
  getData() {
    getCampaignType().then(res => {
      // state	活动状态 0未开始 1进行中 2公示期 3已结束
      let data = res.data
      // data.showTime = this.getDate(data.campaignShowTime)
      console.log(data, 'data 活动报名数据');
      this.setData({
        campaignCode: data.campaignCode,
        signUpInfo: data,
        sendSmsTime: data.sendSmsTime
      })
      getEnrollInfo({
        campaignCode: this.data.campaignCode
      }).then(({
        data
      }) => {
        this.setData({
          userSignInfo: data
        })
      })
    })
  },
  // 处理时间
  getDate(time) {
    let date = new Date(time); // 创建一个日期对象
    let year = date.getFullYear(); // 获取年份
    let month = date.getMonth() + 1; // 获取月份（0-11），需要加1
    let day = date.getDate(); // 获取日

    // 拼接成“2023年1月1日”的格式
    let formattedDate = `${year}年${month}月${day}日`;
    console.log(formattedDate); // 输出：2023年1月1日（假设今天是2023年1月1日）
    return formattedDate
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },
  // 用户点击拒绝授权 展开提示在某个地方开启授权弹框
  openGuide() {
    this.setData({
      showContact1: true
    })
  },
  closeGuide() {
    this.setData({
      showContact1: false
    })
  },
  // 订阅 报名成功
  submit: app.debounce(async function () {
    wx.$mp.track({
      event: 'recruit_Subscription_click',
    })
    let that = this
    if (app.ifRegister()) {
      app.subscribe2('enroll_success').then((res) => {
        let templateIds = res?.templateIds || []
        if (templateIds && templateIds.length > 0) {
          templateIds.forEach((item) => {
            if (res[item] == 'reject') {
              wx.showToast({
                title: '拒绝订阅',
                icon: 'none',
              });
            } else if (res[item] == "accept") {
              wx.showToast({
                title: '订阅成功',
                icon: 'none',
              });
            }
          })
        }
        setTimeout(() => {
          that.getSubNum()
        }, 3000)
      })
    }
  }),
  // 跳转到活动规则页面
  activeRules: app.debounce(async function () {
    wx.$mp.navigateTo({
      url: '/signUp/pages/activityRules/activityRules',
    })
  }),
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
