const app = getApp()
Component({
  properties: {
    // 显示弹窗
    show: {
      type: Boolean,
      value: false,
      observer(e) {
        if (this.data.show && !this.data.jumpData.imgUrl) {
          this.triggerEvent('close')
        }
      }
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      value: true
    },
    // 弹窗背景色
    background: {
      type: String,
      value: 'transparent'
    },
    // 弹窗圆角
    borderRadius: {
      type: Number,
      value: 0
    },
    // 弹窗跳转展示数据
    jumpData: {
      type: null,
      value() {
        return {
          imgUrl: '', // 展示图片
          imgLinks: [], // 图片热区跳转
        }
      }
    }
  },
  data: {},

  methods: {
    // 关闭按钮
    close() {
      this.triggerEvent('close')
    },
    // 关闭弹窗
    goModal() {
      this.triggerEvent('close')
    }
  }
})
