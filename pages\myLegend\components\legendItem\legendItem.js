const app = getApp()

Component({
  options: {
    multipleSlots: true,
    addGlobalClass: true
  },
  properties: {
    legendData: {
      type: Object,
      value: {},
    },
    typeShow: {
      type: String,
      value: 'Legend',
    }
  },
  /**
   * 组件的初始数据
   */
  data: {
    btnList: [{
      label: '里程规则',
      value: 'exchangeRecord',
      icon: ''
    }]
  },
  attached() {
    console.log(this.data.legendData); // 等同于页面的 onLoad

  },


  /**
   * 组件的方法列表
   */
  methods: {
    toDetail: app.debounce(async function (e) {
      console.log(e);
      const { legenddata, typeshow } = e.currentTarget.dataset
      // pointsType	积分类型 1任务 2非任务
      switch (typeshow) {
        case 'coupon':
          if (legenddata.pointsType !== 1) {
            wx.$mp.track({
              event: 'member_point_details_click',
              props: {
                bonusSn: legenddata.bonusSn
              }
            })
            wx.$mp.navigateTo({
              url: '/pages/pointsDetails/pointsDetails?bonusSn=' + legenddata.bonusSn,
            })
          }
          break
        case 'Legend':
          wx.$mp.track({
            event: 'member_legend_details_click',
            props: {
              mileageSn: legenddata.mileageSn
            }
          })
          wx.$mp.navigateTo({
            url: '/pages/mileageDetails/mileageDetails?mileageSn=' + legenddata.mileageSn,
          })
          break
      }
    })
  }
})
