Component({
  properties: {
    // 组件数据
    data: {
      type: Object,
      value() {
        return {}
      }
    },
    // 组件宽度
    width: {
      type: Number,
      value: 750
    }
  },
  data: {

    show: false

  },
  lifetimes: {
    attached() {
      let time = setTimeout(() => {
        this.setData({
          show: true
        })
        clearTimeout(time)
        time = null
      }, 2000);
    }
  },
  methods: {
    // 返回顶部
    goTop() {
      this.triggerEvent('goTop')
    },
    // scroll 锚点滚动跳转
    goAchor(e) {
      this.triggerEvent('goAchor', e.detail)
    },
    // 对弹窗的操作
    goModal(e) {
      this.triggerEvent('goModal', e.detail)
    },
    // 分享
    goShare(e) {
      let {
        shareTitle,
        shareImg,
        sharePath
      } = e.detail
      this.triggerEvent('goShare', {
        shareTitle,
        shareImg,
        sharePath
      })
    },
  }
})
