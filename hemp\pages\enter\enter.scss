/* hemp/pages/index/index.wxss */
.page {
  height: 100vh;
  overflow: hidden;
  &-sign {
    position: relative;
    width: 750rpx;
    background-position: 0 0;
    background-size: 750rpx 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-family: SourceHanSansCN;
    &-title {
      margin-top: 174rpx;
      width: 483rpx;
      height: 132rpx;
    }
    &-item {
      position: relative;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      width: 600rpx;
      border-bottom: 2rpx solid #000;
      padding: 85rpx 5rpx 8rpx;
    }
    &-label {
      font-weight: 400;
      font-size: 33rpx;
      margin-right: 20rpx;
      font-family: SourceHanSansCN;
    }
    &-value {
      font-weight: 400;
      font-size: 42rpx;
      flex: 1;
      text-align: right;
      font-family: "MUJIFont2020", SourceHanSansCN;
    }

    &-btn {
      position: relative;
      top: 8rpx;
      margin-left: 20rpx;
      width: 130rpx;
      height: 48rpx;
      background: #000000;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 17rpx;
      color: #fff;
    }
  }
  &-logo1 {
    width: 242rpx;
    height: 100rpx;
    margin-top: 166rpx;
    margin-bottom: 180rpx;
  }
  &-submit {
    width: 356rpx;
    height: 65rpx;
  }
  &-tip {
    font-family: "MUJIFont2020", SourceHanSansCN;
    font-weight: 400;
    font-size: 25rpx;
    color: #000000;
    margin-top: 24rpx;
  }
}
