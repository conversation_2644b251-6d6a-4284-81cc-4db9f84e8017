// signUp/pages/activateCheckIn/activateCheckIn.js
import {
  SignInUserOpen1,
  getQuestionnaireList,
  getCampaignType
} from '../../api/index.js'
const app = getApp()
// 'agree'
const requiredFields = ['expectation', 'materialUrl',]
Page({

  /**
   * 页面的初始数据
   */
  data: {
    topTitle: '欢迎开启\n7日极简护肤',
    materialUrl: [],
    clickType: [],
    // 1提升皮肤耐受性，缓解敏感症状 2增加保湿度 3改善肤色不均 4舒缓泛红现象 5改善毛孔粗大 6改善皮肤屏障，增强保护力（多选，逗号分隔）
    typeList: [{
      label: "缓解敏感现象",
      value: 1,
      isClick: false
    },
    {
      label: "暗沉改善",
      value: 2,
      isClick: false
    },
    {
      label: "皮肤更加水润",
      value: 3,
      isClick: false
    },
    {
      label: "毛孔变得细腻",
      value: 4,
      isClick: false
    },
    {
      label: "光泽度提升",
      value: 5,
      isClick: false
    },
    {
      label: "细纹/初老\n迹象减少",
      value: 6,
      isClick: false
    },
    {
      label: "痘痘/闭口\n减少",
      value: 7,
      isClick: false
    }
    ],
    isComplate: false,
    disabled: false,
    info: {
      "expectation": "",
      // "source": app.globalData.channel || 1,
      channelTwo: app.globalData.channelTwo,
      channelOne: app.globalData.channelOne,
      "materialUrl": "",
      "campaignCode": wx.getStorageSync('campaignCode'),
      // agree: false
    },
    questionList: [],
    showPopup: false,
    infoLottery: {},
    isClick: null,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.openPopup()
    wx.hideNavigationBarLoading();
    this.getList()
    this.getDatas()
    console.log(app.globalData.channelOne, '渠道1');
  },
  getDatas() {
    getCampaignType().then(res => {
      wx.setStorageSync('campaignCode', res.data.campaignCode)
      this.setData({
        CampaignData: res.data
      })
    })
  },
  // 问题列表
  getList() {
    getQuestionnaireList({
      days: 0,
      scene: 1
    }).then(({
      data
    }) => {
      console.log(data, '问卷列表');
      this.setData({
        questionList: data
      })
    })
  },
  chooseAvatar(e) {
    const {
      avatarUrl
    } = e.detail
    console.log(avatarUrl, 'avatarUrl');
    // wx.$uploadFile({
    //   filePath: avatarUrl
    // }).then(({
    //   data
    // }) => {
    //   this.setData({
    //     ['info.avatar']: data
    //   })
    //   this.validateForm()
    // })
  },
  clickUploader() {
    console.log(this.data.isClick, 'this.data.isClick');
    if (!this.data.isClick) {
      this.openPopup()
      return
    }
  },
  // 上传前校验
  beforeRead(e) {
    let {
      fileType,
      size,
      type
    } = e.detail.file
    if (fileType != 'image') {
      app.toast('请上传图片格式')
      return Promise.reject()
    } else if (size > 20 * 1024 * 1024) {
      app.toast('图片限制在20M以内')
      return Promise.reject()
    }
    this.afterRead(e)
    return Promise.resolve()
  },
  // 上传图片
  afterRead(event) {
    const {
      file
    } = event.detail
    let that = this
    console.log(file, 'file 1111111111');
    wx.cropImage({
      src: file.url, // 图片路径
      cropScale: '1:1', // 裁剪比例，可以根据需求调整
      success: (res) => {
        const tempFilePath = res.tempFilePath; // 裁剪后图片的临时文件路
        console.log('裁剪后的图片路径:', tempFilePath);
        // 如果要上传服务器，可以使用 wx.uploadFile 接口
        wx.$uploadFiles({
          fileUrl: [tempFilePath]
        }).then(({
          data
        }) => {
          console.log(data, 'data 图片上传 返回图片路径');
          this.setData({
            materialUrl: [{
              url: data
            }],
            "info.materialUrl": data ? data[0] : (wx.$config.ossImg + '/signUp/active-prizeAward1.png')
          })
          this.validateForm()
        })
      },
      fail: function (err) {
        console.error('裁剪图片失败:', err);
      }
    })

  },
  // 删除图片
  del(event) {
    const {
      index
    } = event.detail
    let {
      index: idx
    } = event.currentTarget.dataset
    let materialUrl = this.data.materialUrl
    materialUrl.splice(index, 1)

    this.setData({
      "materialUrl": materialUrl,
      "info.materialUrl": '',
    })
    this.validateForm()
  },
  clickTap(e) {
    console.log(this.data.isClick, 'this.data.isClick');
    if (!this.data.isClick) {
      this.openPopup()
      return
    }
    let index = e.currentTarget.dataset.query
    let clickType = this.data.clickType
    let typeList = this.data.typeList

    if (clickType.includes(index)) {
      // 有当前索引的 删除
      clickType = clickType.filter(item => item !== index)
      typeList[index].isClick = !typeList[index].isClick
    } else if (clickType.length < 3) {
      clickType.push(index)
      typeList[index].isClick = !typeList[index].isClick
    } else {
      wx.showToast({
        title: '最多选择3个',
        icon: 'none'
      });
    }
    let dataList = typeList.filter((item, indexs) => clickType.includes(indexs))
    let list = []
    dataList.forEach(item => {
      list.push(item.value)
    })
    this.setData({
      typeList,
      clickType,
      "info.expectation": list.join(',')
    })
    this.validateForm()
  },
  // 校验表单里的必填字段是否都填写
  validateForm() {
    const {
      info
    } = this.data;
    const allFilled = requiredFields.every(field => info[field]);
    console.log(allFilled, 'allFilled');
    console.log(info, 'info');
    this.setData({
      isComplate: allFilled,
    })
  },
  // 提交开启打卡数据
  submit: app.debounce(async function () {
    // 开启打卡体验
    wx.$mp.track({
      event: 'open_information_submit_click',
    })
    console.log(this.data.info, 'isClick 555555');
    // 判断是否已经阅读活动规则 并且数据填写完整
    if (this.data.isClick && this.data.isComplate) {
      this.validateForm()
      SignInUserOpen1(this.data.info).then(res => {
        console.log(res.success);
        if (res.success) {
          // wx.showToast({
          //   title: '成功',
          //   icon: 'success',
          // });
          this.setData({
            disabled: true
          })
          // 跳转到打卡页面 不返回当前页面
          wx.redirectTo({
            url: `/signUp/pages/clock/clock`,
          })
        }
      }).catch(err => {
        console.log(err);
        this.setData({
          disabled: false,
          isComplate: false,
        })
      })
    } else {
      this.openPopup()

    }
  }),
  openPopup() {
    this.setData({
      showPopup: true
    })
  },
  closePopup: app.debounce(async function (e) {
    this.setData({
      showPopup: false,
      loading: true
    })
    let isclick = e.detail
    this.setData({
      isClick: isclick
    })
    console.log(this.data.isComplate, 'isComplate 555555');
    console.log(this.data.isClick, 'isClick 555555');
    let info = this.data.info
    if (this.data.isClick && this.data.isComplate) {
      // info.materialUrl = info.info.join(",")
      // this.setData({
      //   ['info.agree']: true,
      // })
      this.validateForm()
      // console.log(info, 'isClick 6666666');
      SignInUserOpen1(info).then(res => {
        console.log(res.success);
        if (res.success) {
          // wx.showToast({
          //   title: '成功',
          //   icon: 'success',
          // });
          this.setData({
            disabled: true
          })
          // 跳转到打卡页面 不返回当前页面
          wx.redirectTo({
            url: `/signUp/pages/clock/clock`,
          })
        }
      }).catch(err => {
        console.log(err);
        this.setData({
          disabled: false,
          isComplate: false,
        })
      })
    }
  }),
  go() {
    console.log("《MUJI隐私协议》");
    this.setData({
      showPopup: true
    })
  },
  onTapPrivacyCheck() {
    this.setData({
      "info.agree": !this.data.info.agree
    })
    this.validateForm()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    // 页面分享
    return {
      title: '和我一起体验「极简护肤」开启七天打卡活动',
      imageUrl: wx.$config.ossImg + '/signUp/share.png',
      path: '/signUp/pages/signUp/signUp',
    }
  }
})
