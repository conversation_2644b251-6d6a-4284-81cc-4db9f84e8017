.cart-card {
  display: flex;
  // margin-bottom: var(--page-margin);
  height: 200rpx;
  position: relative;

  .cart-img {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;
    width: 200rpx;
    //background-color: pink;
    background-color: #FAFAFA;
    margin-right: 30rpx;
    position: relative;

    .img {
      display: block;
      width: 188rpx;
      height: 188rpx;
    }
  }

  .cart-info {
    position: relative;
    height: 100%;
    flex: 1;


    .cart-title {
      width: 380rpx;
      height: 80rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      margin-bottom: 20rpx;
    }

    .cart-price {
      position: relative;
      margin-bottom: 27rpx;
      height: 40rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 28rpx;
      color: #3C3C43;
      line-height: 40rpx;
      text-align: left;
      font-style: normal;
    }

    .sku-num {
      text-align: left;
      font-size: 24rpx;
    }

    .use-btn {
      position: absolute;
      width: 142rpx;
      height: 50rpx;
      background: #3C3C43;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #FFFFFF;
      line-height: 50rpx;
      text-align: center;
      font-style: normal;
      right: 0;
      bottom: 0;
    }
  }
}
