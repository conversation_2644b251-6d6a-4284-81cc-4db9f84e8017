<!--signUp/pages/activeEnd/activeEnd.wxml 22号后 活动结束-->
<my-page overallModal="{{overallModal}}">
  <view class="page-container">
    <view class="overPage-wrap">
      <!-- <view class="left-icon1 left-icon-width" bindtap="activeRules" style="top:calc(38rpx + {{navBarHeight+statusBarHeight+'px'}})">
        <image src="{{$cdn}}/signUp/Live_gauge_rule.png" mode="" />
      </view>
      <view class="left-icon2 left-icon-width" bindtap="activeRules2" style="top:calc(212rpx + {{navBarHeight+statusBarHeight+'px'}})">
        <image src="{{$cdn}}/signUp/Publish_the_result.png" mode="" />
      </view> -->
      <view bind:tap="activeRules" style="top:calc(38rpx + {{navBarHeight+statusBarHeight+'px'}})" class="page-rule">活动规则</view>
      <view style="top:calc(212rpx + {{navBarHeight+statusBarHeight+'px'}})" bind:tap="activeRules2" class="page-zhongjiang">中选公示</view>
      <view class="overPage-top" style="width: 100%;">
        <custom-header style="margin-bottom:-{{navBarHeight+statusBarHeight+'px'}}" isShare="{{true}}" background="transparent" type="{{1}}" color="black" />
        <image src="{{$cdn}}/signUp/activeEnd/Clock_in_1.jpg" mode="widthFix" style="width:750rpx;flex-shrink:0" wx:if="{{type=='SignInEnd'}}" />
        <image src="{{$cdn}}/signUp/activeEnd/Clock_in_2.jpg" mode="widthFix" style="width:750rpx;flex-shrink:0" wx:elif="{{type=='SignInNot'}}" />
        <image src="{{$cdn}}/signUp/activeEnd/Clock_in_3.jpg" mode="widthFix" style="width:750rpx;flex-shrink:0" wx:else />
        <view class="bottom-box">
          <basic-button width="{{670}}" disabled="{{!disabled}}" loading="{{loading}}" size="large" bind:click="click">
            {{!disabled?"已订阅下次活动":"订阅下次活动"}}
          </basic-button>
          <view class="pullDownView">
            <view class="viewIconfont iconfont icon-Pull-down"></view>
            <view class="viewText">下拉查看更多</view>
          </view>
        </view>

      </view>
      <view class="overPage-line"></view>
      <!-- <view class="overPage-bottom"> -->
      <!-- <view class="bottom-title">
          <view class="bottom-title1"> <text>{{'敏感肌用\n基础补水系列'}}</text> </view>
          <view class="bottom-title2"> 新品介绍 </view>
        </view> -->
      <view class="picture">
        <image class="img" mode="widthFix" style="width:750rpx;flex-shrink:0" src="{{$cdn}}/signUp/overpage2.png" />
      </view>
      <!-- </view> -->
    </view>
    <!-- 拒绝订阅 提示框 -->
    <guidePopup showGuide="{{showContact1}}" bind:finish="closeGuide" />
  </view>
</my-page>