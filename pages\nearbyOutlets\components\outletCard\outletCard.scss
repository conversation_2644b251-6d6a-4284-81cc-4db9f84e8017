.outlet-card {
  width: 670rpx;
  background: #FAFAFA;
  box-sizing: border-box;
  position: relative;
  margin: 0 auto;
  padding: 40rpx 30rpx 40rpx 30rpx;

  .card-box {
    display: flex;
    justify-content: space-between;
  }

  .box-right {
    .distance-box {
      // width: 86rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      // position: absolute;
      // top: 94rpx;
      // right: 37rpx;

      .distance-icon {
        width: 72rpx;
        height: 72rpx;
        background: #FFFFFF;
        box-shadow: 0rpx 0rpx 16rpx 0rpx rgba(0, 0, 0, 0.06);
        border-radius: 36rpx;

        image {
          width: 72rpx;
          height: 72rpx;
        }
      }

      .num {
        height: 32rpx;
        font-family: MUJIFont2020;
        font-weight: 400;
        font-size: 24rpx;
        color: #3C3C43;
        line-height: 32rpx;
        text-align: center;
        font-style: normal;
        margin-top: 12rpx;
      }
    }
  }

  .name {
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 500;
    font-size: 28rpx;
    color: #3C3C43;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 20rpx;
    position: relative;

    .tag {
      // display: inline-block;
      // width: 86rpx;
      padding: 0 10rpx;
      height: 36rpx;
      background: #F4EEDE;
      font-family: PingFangSC,
        PingFang SC;
      font-weight: 500;
      font-size: 22rpx;
      color: #3C3C43;
      // line-height: 36rpx;
      text-align: center;
      font-style: normal;
      position: relative;
      top: -2rpx;
      margin-left: 10rpx;
      display: inline-flex;
      align-items: center;
    }
  }

  .info {
    font-family: PingFangSC,
      PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;
    margin-bottom: 40rpx;

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 5rpx;

      &:first-child {
        margin-bottom: 22rpx;
      }

      .iconfont {
        font-size: 36rpx;
      }

      .address-name {
        width: 440rpx;
      }

      .open-time {
        width: 440rpx;
      }

    }


  }

  .service-box {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 24rpx;
    color: #888888;
    line-height: 32rpx;
    text-align: left;
    font-style: normal;

    .service-list {
      display: flex;
      gap: 30rpx;
      flex-wrap: wrap;
      width: 100%;
      box-sizing: border-box;
      padding: 30rpx;
      background-color: #fff;
      margin-top: 20rpx;
      margin-bottom: 39rpx;

      .service-item {
        width: calc((100% - 60rpx) / 3);
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        color: #888888;
        line-height: 28rpx;
        text-align: left;
        font-style: normal;

        .service-icon {
          flex-basis: 44rpx;
          height: 44rpx;
          width: 44rpx;
          margin-right: 14rpx;
        }

        .service-name {
          flex: 1;
        }
      }

    }
  }

  .btn-box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-item {
      width: 290rpx;
      height: 70rpx;
      border-radius: 5rpx;
      border: 2rpx solid #BBBBBB;
      line-height: 70rpx;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      font-style: normal;
      box-sizing: border-box;
    }

    .black-bg {
      background-color: #3C3C43;
      color: #fff;
      border: none;
    }
  }
}
