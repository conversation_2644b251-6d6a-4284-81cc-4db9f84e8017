<view class="store">
  <view class="store-title" bindtap="goStore">
    <BaseHomeModuleTitle item="{{({leftText:item.title||'附近门店',rightText:'探索更多门店',rightIcon:'icon-View1'})}}" />

  </view>
  <view
    style="background-image: url({{$cdn}}/homeStoreBg.png);background-size: 100% 100%;background-repeat: no-repeat;  padding: 36rpx 30rpx 31rpx;">
    <view wx:if="{{userLatitude}}"  data-img="{{storeData.weworkImages}}"  bindtap="goGuide">
      <view class="store-header">
        <view class="store-header-left"  >
          <view class="iconfont icon-Place1" style="font-size: 38rpx;margin-right: 5rpx;" /> 附近门店
        </view>
        <view class="store-header-right" catchtap="openLocation">
          <image src="{{$cdn}}/positionHome.png" class=""
            style="width:28rpx;flex-shrink:0;height: 28rpx;margin-right: 10rpx;" /> 
            <view class="store-header-right-title">距离{{storeData.distance}}</view>
        </view>
      </view>
      <view class="store-name" >
        {{storeData.storeName}}
      </view>
      <view class="store-address" >
        {{storeData.storeAddress}}
      </view>
      <!-- storeData.images || -->
      <image style="height: 337rpx;" class="store-img" mode="aspectFill"  src="{{item.imgUrl}}" 
        />

    </view>
    <block wx:else>
      <view class="store-unauthorized">
        <view class="store-unauthorized-li">
          请开启定位授权
        </view>
        <view class="store-unauthorized-tips">
          为您推荐附近门店
        </view>
        <view class="store-unauthorized-bth" bindtap="goAuth">
          <view class="iconfont icon-Place1" style="font-size: 36rpx;margin-right: 10rpx;" />开启授权
        </view>
      </view>
      <!-- <image style="height: 200px;" mode="aspectFill" class="store-img" src="{{$cdn}}/emptyStore.jpg" /> -->
      <!-- storeData.images ||  -->
      <image style="height: 337rpx;" class="store-img" src="{{item.imgUrl}}"
        />
      <!-- <view class="store-content" style="padding-bottom:12rpx">
        <view class="store-title">请开启定位授权</view>
      </view>
      <view class="store-operate">
        <view class="store-operate-info">
          <text class="store-icon iconfont icon-Place1" style="color:#3C3C43;font-size:50rpx;"></text>
          <view>为您推荐附近门店</view>
        </view>
        <view class="store-operate-btn" bindtap="goAuth">开启授权</view>
      </view> -->
    </block>
  </view>

</view>
