/* signUp/pages/restartPage/restartPage.wxss */
.page-container {
  height: auto;

  .overPage-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    // justify-content: flex-end;
    align-items: center;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.51) 1%, #F8F6ED 100%);
    position: relative;

    .page-rule {
      //position: absolute;
      z-index: 10;

      position: fixed;
      right: 0rpx;
      top: 243rpx;
      width: 44rpx;
      height: 146rpx;
      background: #C7B397;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      writing-mode: vertical-rl;
      letter-spacing: 4rpx;
    }

    .page-zhongjiang {
      z-index: 10;
      position: fixed;
      right: 0rpx;
      top: 421rpx;
      width: 44rpx;
      height: 146rpx;
      background: #C7B397;
      font-family: MUJIFont2020,
        SourceHanSansCN;
      font-weight: 400;
      font-size: 22rpx;
      color: #FFFFFF;
      line-height: 24rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      writing-mode: vertical-rl;
      letter-spacing: 4rpx;
    }

    .left-icon1 {
      position: absolute;
      right: 0;
      z-index: 2;
    }

    .left-icon2 {
      position: absolute;
      right: 0;
      z-index: 2;
    }

    .left-icon-width {
      image {
        width: 42rpx;
        height: 144rpx;
      }
    }

    .overPage-top {

      // height: calc(100% - 1087rpx);
      width: 100%;
      background-size: 100% auto;
      background-repeat: no-repeat;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;

      position: relative;

      .bottom-box {
        // margin-top: -350rpx;
        position: absolute;
        bottom: 0rpx;
        z-index: 1;
      }

      .pullDownView {
        margin-top: 54rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .viewIconfont {
          transform: scaleY(-1)
        }

        .viewText {
          margin-bottom: 83rpx;
          font-family: MUJIFont2020,
            SourceHanSansCN;
          font-weight: 400;
          font-size: 22rpx;
          color: #3C3C43;
          line-height: 33rpx;
          letter-spacing: 1rpx;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
    }

    .overPage-line {
      width: 100%;
      height: 1rpx;
      background-color: #D8D8D8;
      z-index: 1;
    }

    .overPage-bottom {
      width: 100%;
      background: #F8F6ED;

      .bottom-title {
        margin-left: 45rpx;
        margin-top: 40rpx;

        .bottom-title1 {
          font-family: SourceHanSansCN;
          font-weight: 700;
          font-size: 50rpx;
          color: #2E2E2E;
          line-height: 57rpx;
          letter-spacing: 6rpx;
          text-align: left;
        }

        .bottom-title2 {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 169rpx;
          height: 54rpx;
          margin-top: 30rpx;
          box-sizing: border-box;
          padding-top: 3rpx;
          font-family: SourceHanSansCN;
          font-weight: 500;
          font-size: 29rpx;
          // line-height: 42rpx;
          letter-spacing: 1rpx;
          text-align: center;
          color: #FFFFFF;
          background: #3C3C43;
          border-radius: 27rpx 27rpx 27rpx 27rpx;
        }
      }

      .picture {
        width: 706rpx;
        height: 2408rpx;
        margin: auto;
        padding-top: 55rpx;
        padding-bottom: 20rpx;

        .img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
