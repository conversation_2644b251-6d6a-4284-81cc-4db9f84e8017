// pages/myCoupon/myCoupon.ts
const app = getApp()
import {
  usercouponlist
} from '../../api/index'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabList: [{
      label: '全部',
      value: 'allCouponList',
    }, {
      label: '优惠券',
      value: 'couponList',
    }, {
      label: '商品券',
      value: 'physicalCouponList',
    }],
    currentLevel: 0,
    currentList: [],
    // loading: false,
    listType: 'allCouponList',
    scrollTop: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow(options) {
    console.log(options,'optionsoptionsoptionsoptionsoptions');
    if(options.currentLevel){
      this.setData({
        currentLevel:Number(options.currentLevel),
        listType:this.data.tabList[Number(options.currentLevel)]['value']
      })
    }
    await this.getList()
  },
  getList() {
    // this.setData({
    //   loading: true
    // })
    usercouponlist().then(res => {
      this.setData({
        currentList: res.data[this.data.listType] || []
      })
    }).finally(() => {
      // this.setData({
      //   loading: false
      // })
    })
  },
  tolishi: app.debounce(async function (e) {
    wx.$mp.navigateTo({
      url: '/pages/historicalGift/historicalGift',
    })
  }),
  toShop: app.debounce(async function (e) {
    // console.log(e);
    wx.$mp.switchTab({
      url: '/pages/life/life',
    })
  }),
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  onChangeTab(e) {
    const {
      value,
      index
    } = e.detail;
    this.setData({
      currentLevel: index,
      listType: value,
      scrollTop: 0
    })
    this.getList()
  },
  onShareAppMessage() {}
})
