<!--pages/complateTask/complateTask.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
    <view slot="content" class="page-title">
      已过期任务
    </view>
  </custom-header>
  <view class="page-content">
    <view class="task-list">
      <view wx:for="{{taskList}}" wx:key="id">
        <task-card taskInfo="{{item}}" type="{{type}}" />
      </view>
      <block wx:if="{{!loading}}">
        <no-data-available top="{{536}}" data="{{taskList}}" text="暂无过期任务" />
      </block>
    </view>
  </view>
</my-page>