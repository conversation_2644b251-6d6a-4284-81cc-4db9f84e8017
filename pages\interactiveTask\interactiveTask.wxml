<!--pages/interactiveTask/interactiveTask.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <custom-header type="{{1}}" isBackHidden="{{ isBackHidden }}">
    <view slot="content" class="page-title">
      互动任务
    </view>
  </custom-header>
  <view class="page-content" style="height: calc(100vh -  {{navHeight}}px)">
    <view id="tabBar">
      <tabs bind:change="onChangeTab" active="{{type}}" tabList="{{tabList}}" currentTab="{{type}}" showNew="{{showNew}}" />
    </view>
    <scroll-view scroll-y="{{true}}" class="task-list" style="height: calc(100vh - {{navHeight + tabHeight}}px)" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}">
      <view class="task-list-box">
        <block wx:if="{{taskList.length>0}}">
          <view class="task-list-item" wx:for="{{taskList}}" wx:key="id">
            <task-card taskInfo="{{item}}" type="{{type}}" bind:showDialog="showRuleDialog" bindupdate="getList"/>
          </view>
        </block>
        <block wx:else>
          <no-data-available top="{{536}}" data="{{taskList}}" text="暂无互动任务" />
        </block>
      </view>
      <view class="bottom-box">
        <view bind:tap="handleGoto" class="complate-btn" data-url="/pages/expireTask/expireTask">已过期任务</view>
      </view>
    </scroll-view>
    <!-- <block wx:if="{{!loading}}">
      <no-data-available top="{{536}}" data="{{taskList}}" text="暂无互动任务" />
    </block>
    <view wx:if="{{taskList.length===0 }}" class="fixed-bottom">
      <view bind:tap="handleGoto" class="complate-btn" data-url="/pages/expireTask/expireTask">已过期任务</view>
    </view> -->
  </view>
</my-page>
<my-popup show="{{ showRuleDialog}}" borderRadius="0" closeable="{{true}}" title="任务规则" confirmText="我知道了" bindclose="handleClose" bindconfirm="handleClose">
  <image src="{{currentRuleImg}}" mode="widthFix" style="width:100%;display:block">
  </image>
</my-popup>