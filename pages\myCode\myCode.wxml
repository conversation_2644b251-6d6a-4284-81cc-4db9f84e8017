<!--pages/myCode/myCode.wxml-->
<my-page loading="{{loading}}" overallModal="{{overallModal}}" catch:overallModalConfirm="overallModalConfirm">
  <view class="page-container">
    <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" />
    <view class="page-content">
      <view class="member-box">
        <view class="member-bg"></view>
        <view class="member-info">
          <view class="member-txt">
            <view class="info-item">
              <view class="title">
                昵称
              </view>
              <view class="name">
                {{userInfo.username}}
              </view>
            </view>
            <view class="info-item">
              <view class="title">
                等级
              </view>
              <view class="name">
                {{ userInfo.cardLevelName}}
              </view>
            </view>
          </view>
          <view class="member-right">
            <view class="avatar">
              <image src="{{userInfo.avatar ? userInfo.avatar : $cdn + '/default-avatar.png'}}" mode="" />
            </view>
            <view class="member-time" wx:if="{{userInfo.registerTime}}">注册时间：{{registerTime}}</view>
          </view>
        </view>
        <view class="code-box">
          <view class="qrCode">
            <view class="title">会员码可累计里程和积分 (不支持付款)</view>
            <view class="code-tip">
              <image src="{{$cdn}}/tip.png" class="code-icon" />
              <view class="code-info">使用会员码截图视为无效</view>
            </view>
            <view class="code-img" bind:tap="handleGo">
              <image class="code" src="{{codeImg}}" mode="" style="width: 315rpx; height: 315rpx;" />
              <canvas canvas-id="myQrcode" id="myQrcode" class="canvas-box"
                style="width: 180px; height: 180px;"></canvas>
            </view>
            <view class="tips-barcode" bind:tap="handleGo">点击二维码查看条形码</view>
          </view>
          <view class="custom-dashed-border" />
          <view class="code-txt" bindtap="copyTxt" data-copy="{{userInfo.cardNo}}">
            {{userInfo.cardNo}}
            <view class="iconfont icon-Clone" />
          </view>
          <view class="custom-dashed-border" />
          <!-- <view class="barCode">
            <canvas id="barcode" type="2d" style="width: 400rpx; height: 200rpx;" />
          </view> -->
        </view>
      </view>
    </view>
    <!-- <view class="bottom-box">
      <basic-button disabled="{{!isComplate}}" width="{{600}}" btnState="primary" size="large" bind:click="register">
        微信支付
      </basic-button>
    </view> -->
    <view class="bottom-box">
      <basic-button width="{{600}}" btnState="primary" size="large" bind:click="handleGoMember">
        <image style="width: 40rpx;height: 40rpx;margin-right: 10rpx;vertical-align: middle;" src="{{$cdn}}/membershipCard.png" mode="" /> 微信会员卡
      </basic-button>
    </view>
  </view>
</my-page>
