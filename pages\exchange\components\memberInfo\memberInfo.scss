.member-info {
  width: 652rpx;
  margin: 0 auto;
  position: absolute;
  z-index: 2;
  left: 50%;
  transform: translateX(-50%);
  transition: bottom 0.5s;

  .member-bg {
    height: 260rpx;
    border-radius: 10rpx 10rpx 0 0;
    overflow: hidden;
    z-index: 3;
    position: relative;
    background-size: 100% 100%;
  }

  .top-right-up {
    margin-left: 9rpx;
    width: 18rpx;
    height: 18rpx;
  }

  .expand-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 27rpx;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 20rpx;
    color: #3C3C43;
    line-height: 27rpx;
    font-style: normal;
    width: 100%;
    text-align: center;
    z-index: 9;
  }

  .top-position {
    margin-bottom: 30rpx;
    transition: opacity 0.5s;
  }

  .bottom-position {
    margin-top: 27rpx;
    transition: opacity 0.5s;
  }


  .member-content {
    display: flex;
    padding: 30rpx 20rpx 0rpx 20rpx;
    position: relative;
    box-sizing: border-box;
    width: 100%;
    z-index: 0;
    transition: opacity 0.5s;


    .level-tag {
      position: absolute;
      width: 130rpx;
      // padding: 6rpx 15rpx 8rpx;
      height: 46rpx;
      background: #FFFFFF;
      border-radius: 5rpx;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #3C3C43;
      line-height: 46rpx;
      text-align: center;
      font-style: normal;
      top: 30rpx;
      right: 40rpx;
      z-index: 99;
    }


    .member-avatar {
      width: 140rpx;
      height: 140rpx;
      border: 1rpx solid #FFFFFF;
      margin-right: 31rpx;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      // background-color: pink;
      image {
        width: 120rpx;
        height: 120rpx;
      }
    }

    .member-desc {
      position: relative;
      flex: 1;
      width: 0;
      // background-color: green;
      // padding-top: 4rpx;



      .row {
        width: 100%;
        display: flex;

        &:first-child {
          margin-bottom: 36rpx;
        }

        .member-item1 {
          width: 100%;
          margin-top: 14rpx;

          .item-label {
            // width: 100%;
            display: flex;
            align-items: center;
            height: 28rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 20rpx;
            color: #3C3C43;
            // line-height: 28rpx;
            text-align: left;
            font-style: normal;

          }

          .item-value {
            width: 100%;
            height: 40rpx;
            font-family: MUJIFont2020, SourceHanSansCN;
            // font-family: MUJIFont2020, MUJIFont2020;
            font-weight: 600;
            font-size: 28rpx;
            color: #3C3C43;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .member-item {
          width: 33%;
          // margin-top: 14rpx;

          .item-label {
            display: flex;
            align-items: center;
            height: 28rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 20rpx;
            color: #3C3C43;
            // line-height: 28rpx;
            text-align: left;
            font-style: normal;

            .right-jian {
              width: 18rpx;
              height: 18rpx;
              margin-left: 5rpx;
            }
          }

          .item-value {
            height: 40rpx;
            font-family: PingFangSC, PingFang SC;
            // font-family: MUJIFont2020, MUJIFont2020;
            font-weight: 600;
            font-size: 28rpx;
            color: #3C3C43;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
          }

          .item-num {
            height: auto;
            margin-top: 10rpx;
            font-family: MUJIFont2020, MUJIFont2020;
            font-weight: bold;
            font-size: 28rpx;
            color: #3C3C43;
            // line-height: 56rpx;
            text-align: left;
            font-style: normal;
          }
        }
      }
    }
  }

}
