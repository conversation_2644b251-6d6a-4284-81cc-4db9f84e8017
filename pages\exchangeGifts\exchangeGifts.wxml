<my-page loading="{{loading}}" overallModal="{{overallModal}}">
  <view class="page-content">
    <view class="fixed-top" id="fixedTop">
      <custom-header type="{{3}}" isBackHidden="{{ isBackHidden }}">
        <view slot="content" class="page-title">
          <view class="nav-btn">
            <view class="btn">
              <navigator url="/pages/productSearch/productSearch">
                <text class="iconfont icon-Search"></text>
              </navigator>
            </view>
            <view class="divided" />
            <view class="btn">
              <navigator url="/pages/cart/cart">
                <text class="iconfont icon-Cart"></text>
              </navigator>
            </view>
          </view>
        </view>
      </custom-header>
      <view class="fixed-content">
        <my-credits myCredits="{{currentPoint}}" />
        <filer-box shelfId="{{currentShelfId}}" bind:filter="handleFilter" topHeight="{{topHeight}}" filterData="{{filterData}}" />
      </view>
    </view>
    <scroll-view style="height: calc(100vh -  {{topHeight}}px + {{navHeight}}px)" class="list-box" enhanced="{{true}}" show-scrollbar="{{ false }}" scroll-y bounces="{{true}}" bindscrolltolower="getMore">
      <view class="total-box">
        共有{{productList.length}}件商品
      </view>
      <block wx:if="{{bannerContent!==''}}">
        <banner-list bannerContent="{{bannerContent}}" />
      </block>
      <view class="product-list">
        <block wx:if="{{productList.length>0}}">
          <view class="product-item" wx:for="{{productList}}" wx:key="id">
            <product-card skuInfo="{{item}}" />
          </view>
        </block>
        <block wx:else>
          <no-data-available data="{{legendLis}}" text="暂无商品" />
        </block>
      </view>
    </scroll-view>
  </view>
</my-page>
