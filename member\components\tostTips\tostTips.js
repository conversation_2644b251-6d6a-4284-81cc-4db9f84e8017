const app = getApp()
Component({
  properties: {
    title: String,
    icon: String,
    duration: {
      type: Number,
      value: 2000
    }
  },
  data: {
    show: false,
    msg: null,
    msgTips: null,
    timer: null
  },
  methods: {
    showToast: app.debounce(function (msg, msgTips) {
      this.setData({ show: true, msg, msgTips: msgTips });
      const timer = setTimeout(() => {
        this.hideToast();
      }, this.properties.duration);
      this.setData({ timer });
    }, 300),
    hideToast() {
      this.setData({ show: false, msg: null, msgTips: null });
      clearTimeout(this.data.timer);
    }
  }
});