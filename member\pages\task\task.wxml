<my-page class="task-my-page page-current-{{pageCurrent ? pageCurrent : 'loading'}} {{isOnLoad ? 'on-load' : ''}}"
  overallModal="{{overallModal}}" loading="{{loading}}">
  <!-- 外层全屏容器 -->
  <view class="task-wrapper">

    <!-- 动画1 start -->
    <view class="section-start">
      <view class="start-mask"></view>
      <!--猫咪动画-->
      <image wx:if="{{pageCurrent === 'start' || pageCurrent === 'popup'}}" class="start-image"
        src="{{$cdn}}/member/start-2-25.png" />
      <!--<image wx:if="{{pageCurrent === 'start' || pageCurrent === 'popup'}}" class="start-image start-image-1" src="{{$cdn}}/member/start-1.webp" />-->
      <!--猫咪动画 最后一帧-->
      <image wx:if="{{pageCurrent === 'start' || pageCurrent === 'popup'}}" class="start-last-image"
        src="{{$cdn}}/member/start-2-last.webp" />
      <!--<image wx:if="{{pageCurrent === 'start' || pageCurrent === 'popup'}}" class="start-last-image start-last-image-1" src="{{$cdn}}/member/start-last-1.webp" />-->
    </view>

    <!-- 动画2 popup -->
    <view class="section-popup">
      <!--<view class="popup-mask"></view>-->
      <view class="popup-title">
        <block wx:if="{{isRegistered}}">
          您的优惠券礼包已到账
          <view class="popup-title-desc">可前往「<text bind:tap="onTapDesc" class="link">我的礼券</text>」查看</view>
        </block>
        <block wx:else>
          欢迎来到MUJI良友节
          <view class="popup-title-desc">注册会员即可领取优惠券礼包</view>
        </block>
      </view>
      <view bind:tap="onTapStartPopup" class="popup-button">
        <block wx:if="{{isRegistered}}">收获更多礼遇</block>
        <block wx:else>立即注册</block>
      </view>
      <image class="popup-window" src="{{$cdn}}/member/window.svg" />

      <image wx:if="{{pageCurrent === 'popup'}}" class="popup-butterfly anim" src="{{$cdn}}/member/butterfly.webp" />
      <image class="popup-butterfly static" src="{{$cdn}}/member/butterfly.png" />
      <image class="popup-butterfly-preload" src="{{$cdn}}/member/butterfly.webp" />
    </view>

    <!-- 飞地 不随容器上下移动 -->
    <view class="section-lottery-outer">
      <view class="lottery-outer-text">抽奖赢好礼</view>
      <image bind:animationend="onLogoTransitionEnd" class="lottery-outer-logo" src="{{$cdn}}/member/logo.svg"></image>
    </view>

    <!-- 内层移动区域 -->
    <view class="task-content">

      <!-- 抽奖区域 -->
      <view class="section-lottery">
        <view class="lottery-tree-wrapper">
          <image class="lottery-tree" bind:tap="onTapLottery" src="{{$cdn}}/member/tree.svg"></image>
          <image class="lottery-tree-box box11" src="{{$cdn}}/member/tree-box-1.svg"></image>
          <image class="lottery-tree-box box12" src="{{$cdn}}/member/tree-box-1.svg"></image>
          <image class="lottery-tree-box box2" src="{{$cdn}}/member/tree-box-2.svg"></image>
        </view>
        <view class="lottery-draw-text" bind:tap="onTap2Tree">
          <image class="lottery-draw-text-img" src="{{$cdn}}/firendShip/drawPrize.png" />
        </view>

        <view class="lottery-footer" bind:transitionstart="onLotteryTransitionStart"
          bind:transitionrun="onLotteryTransitionRun" bind:transitionend="onLotteryTransitionEnd">
          <view bind:tap="onTapLottery"
            class="lottery-title {{!lotteryTimesLoaded ? 'hide' : ''}} {{lotteryInfo && lotteryInfo.surplusCount === 0 ? 'disabled' : ''}}">
            <image wx:if="{{lotteryInfo && lotteryInfo.surplusCount === 0}}" class="image-hand"
              src="{{$cdn}}/member/hand-black.png" />
            <image wx:else class="image-hand" src="{{$cdn}}/member/hand.png" />
            点击抽取
          </view>
          <view class="lottery-desc {{!lotteryTimesLoaded ? 'hide' : ''}}">
            <block wx:if="{{lotteryInfo && lotteryInfo.surplusCount > 0}}">
              当前可抽奖次数：
              <text class="lottery-desc-num">{{lotteryInfo.surplusCount}}</text>
            </block>
            <block wx:else>您暂无抽奖次数</block>
          </view>
          <view catch:tap="onTap2Task" class="lottery-button">获取更多抽奖次数</view>
        </view>

        <view class="lottery-popup {{lotteryPopupShow ? 'show' : ''}}">
          <view class="lottery-popup-content">
            <image bind:tap="onTapLotteryPopupClose" class="popup-close" src="{{$cdn}}/member/lottery-popup-close.png">
            </image>
            <view class="popup-title">恭喜您抽中</view>
            <view wx:if="{{lotteryPopupInfo}}" class="popup-content {{lotteryPopupLoading ? 'loading' : ''}}">
              <image class="bg" src="{{$cdn}}/member/lottery-popup-bg.png" />

              <view class="popup-transition">
                <!--prizesType 1实物商品 2电子券 3积分 4优惠券 99空-->
                <block wx:if="{{lotteryPopupInfo.prizesType === 3}}">
                  <view class="popup-content-header">
                    <view class="number">{{lotteryPopupInfo.pointsNum}}</view>
                    <view class="unit">积分</view>
                  </view>
                  <view class="popup-content-footer">
                    <view class="desc">前往「积分商城」使用</view>
                  </view>
                </block>
                <image wx:else class="popup-content-image" src="{{lotteryPopupInfo.detailImageUrl}}" />
              </view>

            </view>
            <view class="popup-footer">
              <view class="lottery-times {{}}">
                <view wx:if="{{lotteryInfo.surplusCount > 0}}" bind:tap="onTapLotteryPopupLottery" class="button-d">
                  <view class="button-d-text disabled {{lotteryPopupLoadingTime > -1 ? 'show' : ''}}">
                    ({{lotteryPopupLoadingTime > -1 ? lotteryPopupLoadingTime : 0}}s) 后可继续抽奖
                  </view>
                  <!--<view-->
                  <!--  class="button-d-text disabled {{lotteryPopupLoadingTime < 1 && lotteryPopupLoading ? 'show' : '' }}">-->
                  <!--  活动人数较多 请稍后-->
                  <!--</view>-->
                  <view class="button-d-text {{lotteryPopupLoadingTime < 0 ? 'show' : '' }}">
                    继续抽奖 ({{lotteryInfo.surplusCount}})
                  </view>
                </view>
                <view wx:else bind:tap="onTapLotteryPopupTask" class="button-d">获取更多抽奖次数</view>
              </view>
              <view bind:tap="onTapLotteryPopupGift" class="button-g">查看我的礼券</view>
            </view>
          </view>
          <view class="lottery-popup-mask"></view>
        </view>
      </view>

      <view class="task-scroll-wrapper">
        <view class="task-scroll-shadow" wx:if="{{shadowShow}}"></view>
        <scroll-view scroll-y bindscroll="scroll" style="height: calc(100vh - 640rpx)">
          <view class="page">
            <!-- <image class="page-header" src="{{$cdn}}/firendShip/bookBg.png" mode="widthFix" /> -->
            <!-- <image class="page-prize" src="{{$cdn}}/firendShip/drawPrize.png" /> -->
            <view class="page-title">获得更多抽奖次数</view>
            <view class="page-subTitle {{!lotteryTimesLoaded ? 'hide' : ''}}">当前可抽奖次数：<text class="page-task-num"
                style="font-weight: 700;font-size: 24rpx;color: #231815;">{{lotteryInfo.surplusCount||0}}</text></view>
            <view class="page-task">
              <view class="page-task-item" style="position: relative;" wx:for="{{taskList}}" wx:for-item="task"
                wx:for-index="taskKey" wx:key="taskKey">
                <view class="page-task-title">{{task.title}}
                  <view wx:if="{{task.showType === 'clockIn'}}">（已访问<text
                      class="page-task-num">{{task.alreadyClockInDay||0}}</text>天）</view>
                </view>
                <view class="page-task-subTitle" wx:if="{{task.showType === 'clockIn'}}">
                  每日访问活动首页，可获得<text class="page-task-num">{{task.clockInLotteryNum||0}}</text>次抽奖机会
                </view>
                <!-- 邀请用户 -->
                <view class="page-task-subTitle" wx:if="{{task.showType === 'Invite'}}">
                  每邀请<text class="page-task-num">{{1}}</text>位新用户注册，可获得<text
                    class="page-task-num">{{1}}</text>次抽奖机会，每日上限<text
                    class="page-task-num">{{task.totalReadyNum||0}}</text>次
                </view>
                <!-- 分享活动 -->
                <view class="page-task-subTitle" wx:if="{{task.showType === 'sharingActivity'}}">
                  分享活动可获得<text class="page-task-num">{{task.prizeNum}}</text>次抽奖机会，每日仅限<text
                    class="page-task-num">{{1}}</text>次
                </view>
                <!-- 积分兑换 -->
                <view class="page-task-subTitle" wx:if="{{task.showType === 'point'}}">
                  <text class="page-task-num">{{task.pointNum}}</text>积分兑换<text
                    class="page-task-num">{{1}}</text>次抽奖机会，每日上限<text
                    class="page-task-num">{{task.redeemNum}}</text>次（可用积分：<text class="page-task-num">{{pointsUser
                    ||0}}</text>）
                </view>
                <view class="page-task-info">
                  <!-- 邀请用户 -->
                  <view class="page-task-info-box" wx:if="{{task.showType === 'Invite'}}">
                    <view class="page-task-show">
                      <!--  {{index<5?'active':''}} {{index<task.InviteAll?'active':''}} -->
                      <view class="page-task-Invite {{index<task.InviteAll?'active':''}}" style="--index:{{index}}"
                        wx:for="{{task.readyNum}}" wx:key="index">
                        <image wx:if="{{index<task.InviteAll}}" src="{{task.inviteFriendsImgList[index]}}"
                          mode="widthFix" />
                      </view>
                    </view>
                  </view>
                  <!-- 这个是积分的展示 -->
                  <view class="page-task-info-box" wx:if="{{task.showType === 'point'}}">
                    <view class="page-task-show">
                      <!-- {{index<task.alreadyRedeemNum?'active':''}} -->
                      <view class="page-task-point" style="--index:{{index}}" wx:for="{{task.redeemNum}}"
                        wx:key="index">
                        <image style="width: 100%;height: 100%;"
                          src="{{$cdn}}/member/{{index<task.alreadyRedeemNum?'':'un'}}completed.png" mode="" />
                        <!-- <view style="font-size: 26rpx; font-weight: 100;" class="iconfont icon-Mark"></view> -->
                      </view>
                    </view>
                  </view>
                  <!-- <view class="page-task-btn" wx:if="{{task.showType === 'clockIn'}}" bindtap="goClockIn" data-item="{{task}}">
                    去打卡
                  </view> -->
                  <view class="page-task-btn" style="  margin-top: 38rpx;" wx:if="{{task.showType === 'point'}}"
                    bindtap="goPoint" data-item="{{task}}">
                    去兑换
                  </view>
                  <button class="page-task-btn" style="margin-top: 38rpx;  background-color: #231815 !important;"
                    wx:if="{{task.showType === 'Invite'}}" data-type="Invite" data-item="{{task}}" open-type="share">
                    去邀请
                  </button>
                  <!-- <view class="page-task-btn" style="  margin-top: 26rpx;" wx:if="{{task.showType === 'Invite'}}"
                    bindtap="goInvite" data-item="{{task}}">
                    去邀请
                  </view> -->
                </view>
                <button class="page-task-btn"
                  style="position: absolute;right: 0;top: 64rpx;   background-color: #231815 !important;"
                  wx:if="{{task.showType === 'sharingActivity'}}" data-type="sharingActivity" data-item="{{task}}"
                  open-type="share">
                  去分享
                </button>
                <!-- top:50%;transform: translateY(-50%); -->
                <view class="page-task-btn"
                  style="position: absolute;right: 0;top: 64rpx;  background-color: #231815 !important;"
                  wx:if="{{task.showType === 'clockIn'}}" bindtap="goClockIn" data-item="{{task}}">
                  提醒我
                </view>
                <!-- <view class="page-task-btn" style="position: absolute;right: 0;top:50%;transform: translateY(-50%); "
                  wx:if="{{task.showType === 'sharingActivity'}}" bindtap="goSharing" data-item="{{task}}">
               去分享
                </view> -->
              </view>
            </view>
            <view style="width:100%;height:60rpx"></view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>

  <custom-header background="transparent" type="{{1}}" isBackHidden="{{ isBackHidden }}" isFixed>
    <view slot="content" class="lottery-header-more">
      <image bind:tap="onTapMore" src="{{$cdn}}/firendShip/more.png" class="lottery-header-more-img" />
    </view>
  </custom-header>
  <!-- 提示弹窗 -->
  <tostTips id="customToast" duration="{{timeTo}}" />
  <!-- 邀请组件 -->
  <share-poster src="{{invitePoster}}" show="{{showInvite}}" bindsuccess="closesuccess" bindclose="closeModal">
  </share-poster>
  <move-icon showIcon="{{pageCurrent === 'task'}}"></move-icon>
  <!-- 生成海报 -->
  <canvas class="page-canvas" id="myCanvas" type="2d"></canvas>

  <member-popup show="{{ showShareSuccess }}" closeable="{{false}}" borderRadius="{{0}}"
    title="{{shareSuccessTimes > 0 ? '今日已分享' : '分享成功'}}" confirmText="我知道了" cancelText=""
    content="{{shareSuccessTimes > 0 ? '今日次数已达上限' : '获得1次抽奖机会'}}" bindconfirm="onTapShareSuccess" bindcancel=""
    data-key="success" showType="normal"></member-popup>
</my-page>