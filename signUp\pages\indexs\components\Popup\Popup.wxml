<my-modal show="{{isShow}}" background="{{'#00000000'}}" closeable="{{true}}" borderRadius="{{0}}" bindclose="onClose">
  <view class="clockPopup tab-bar">
    <view class="clockPopup-content" style="background:#FBF8F3;--radius:0rpx;">
      <!-- <view class="title"> <text>{{title}}</text> </view> -->
      <view class="text">
        <!-- <text>您的收货地址及您的联系方式仅用于本次活动奖品寄送。MUJI上海承诺严格保密，不以任何方式公开您的个人信息。如果我们要将您的个人信息用于其它用途，我们将以合理的方式向您告知，并再次征得您的同意。</text> -->
        <scroll-view catch:touchmove="touchmove1" style="height: 795rpx;" bindscrolltolower="bindscrolltolower" class="Participate" enhanced="{{ true }}" bounces="{{ false }}" show-scrollbar="{{ false }}" scroll-y>
          <view style="image-rendering: -moz-crisp-edges; image-rendering:-o-crisp-edges; image-rendering: -webkit-optimize-contrast; image-rendering: crisp-edges; -ms-interpolation-mode: nearest-neighbor;">
            <image src="{{$cdn}}/signUp/acitveRules2.png" mode="widthFix" style="width:554rpx;flex-shrink:0;height: auto;" />
            <image src="{{$cdn}}/signUp/acitveRules3.png" mode="widthFix" style="width:554rpx;flex-shrink:0;height: auto;" />
            <image src="{{$cdn}}/signUp/acitveRules4.png" mode="widthFix" style="width:554rpx;flex-shrink:0;height: auto;" />
          </view>
        </scroll-view>

      </view>
      <view class="clock-btn">
        <basic-button width="{{510}}" disabled="{{disabled}}" size="large" data-isclick='{{true}}' bind:click="onClose">
          我已同意
        </basic-button>
      </view>
    </view>
  </view>
</my-modal>