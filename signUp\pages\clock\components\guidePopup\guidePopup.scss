@import "assets/scss/config";

.v-model {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.guide {
  z-index: 1001;
}

.guide-box {
  position: absolute;
  z-index: 10001;
  transition: all 0.2s;

  width: 0px;
  height: 0px;
  left: 233rpx;
  top: 172rpx;
  box-shadow: rgb(33 33 33 / 80%) 0px 0px 0px 0px, rgb(33 33 33 / 50%) 0px 0px 0px 5000px;
  margin: 0;
}

.guide-box::before {
  content: '';
  color: #FFFFFF;
  height: 100%;
  width: 100%;
  border-radius: 8rpx;
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  padding: 7rpx;
}

.tips {
  width: 520rpx;
  height: 44rpx;
  font-family: MUJIFont2020,
    SourceHanSansCN;
  font-weight: 700;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 43rpx;
  letter-spacing: 1px;
  text-align: left;

  position: absolute;
  top: 172rpx;
  left: -50%;
}

.iconfont {
  width: 125rpx;
  height: 125rpx;
  color: #FFFFFF;
  // background-color: black;
  background-size: 100% 100%;

  position: absolute;
  top: 13rpx;
  left: calc(-50% + 173rpx);
}
